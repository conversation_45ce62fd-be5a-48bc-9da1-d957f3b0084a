# DeepSeek-INT8 SGLang推理测试项目

## 项目简介

本项目基于SGLang框架实现DeepSeek-INT8量化模型的推理测试，严格遵循使用规则，包含完整的环境检查、推理脚本和测试报告。

## 快速开始

### 1. 环境准备

```bash
# 激活虚拟环境
source /workspace/sglang_test/bin/activate

# 切换到工作目录
cd /workspace/sglang_test_workspace/deepseek_int8_inference_task

# 检查环境（推荐首先运行）
python simple_test.py
```

### 2. 运行推理测试

#### 方式一：Runtime模式（直接推理）
```bash
python deepseek_int8_inference.py
```

#### 方式二：服务器模式（推荐）
```bash
# 启动服务器并运行客户端测试
bash run_deepseek_inference.sh
```

#### 方式三：手动启动服务器
```bash
# 启动服务器
python3 -m sglang.launch_server \
    --model-path /home/<USER>/deepseek-int8 \
    --quantization w8a8_int8 \
    --trust-remote-code \
    --port 30001 \
    --host 127.0.0.1 \
    --tp-size 1 \
    --mem-fraction-static 0.8 \
    --disable-cuda-graph

# 在另一个终端运行客户端
python deepseek_int8_inference_client.py
```

## 文件说明

| 文件名 | 功能描述 |
|--------|----------|
| `simple_test.py` | 环境检查脚本，验证模型文件、配置和SGLang兼容性 |
| `deepseek_int8_inference.py` | Runtime模式推理脚本 |
| `deepseek_int8_inference_client.py` | 客户端模式推理脚本 |
| `run_deepseek_inference.sh` | 自动化启动脚本 |
| `deepseek_config.json` | 推理配置文件 |
| `DeepSeek_INT8_SGLang推理测试总结报告.md` | 详细技术报告 |

## 配置参数

### 模型配置
- **模型路径**: `/home/<USER>/deepseek-int8`
- **量化方式**: `w8a8_int8`
- **服务器端口**: `30001`
- **GPU内存使用**: `80%`

### 推理参数
- **最大新token数**: `200`
- **温度**: `0.7`
- **top_p**: `0.9`
- **最大总token数**: `512`

## 测试内容

### 1. 环境检查
- 模型文件完整性验证
- 配置文件格式检查
- SGLang兼容性测试
- GPU资源可用性检查

### 2. 推理测试
- 基本问答测试（5个中文问题）
- 结构化输出测试
- 情感分类测试
- 性能统计分析

## 已知问题

### DeepSeek-V3兼容性问题
当前SGLang版本(0.5.2)与DeepSeek-V3模型存在兼容性问题：
```
AttributeError: 'NoneType' object has no attribute 'hidden_states'
```

**解决方案**:
1. 等待SGLang官方修复
2. 尝试其他版本的SGLang
3. 关注相关GitHub issue: https://github.com/sgl-project/sglang/pull/3888

## 技术要求

### 硬件要求
- GPU: NVIDIA A100或同等性能GPU
- 内存: 至少16GB GPU内存
- 存储: 至少20GB可用空间

### 软件要求
- Python 3.10+
- SGLang 0.5.2
- PyTorch 2.0+
- CUDA 11.8+

## 故障排除

### 1. 导入错误
```bash
# 重新安装sgl-kernel
uv pip install sgl-kernel --force-reinstall
```

### 2. GPU内存不足
```bash
# 调整内存使用比例
--mem-fraction-static 0.6
```

### 3. 服务器启动失败
```bash
# 检查端口占用
netstat -tulpn | grep 30001

# 更换端口
--port 30002
```

## 性能优化建议

1. **使用多GPU**: 增加`--tp-size`参数
2. **调整批处理大小**: 修改`max_running_requests`
3. **启用CUDA图**: 移除`--disable-cuda-graph`（兼容性问题解决后）
4. **内存优化**: 调整`mem_fraction_static`参数

## 联系信息

如有问题或建议，请参考：
- SGLang官方文档: https://github.com/sgl-project/sglang
- DeepSeek模型文档: https://github.com/deepseek-ai/DeepSeek-V3

## 更新日志

- **2025-09-15**: 初始版本发布
  - 完成环境检查脚本
  - 实现Runtime和服务器模式推理
  - 生成详细技术报告
  - 识别并记录兼容性问题
