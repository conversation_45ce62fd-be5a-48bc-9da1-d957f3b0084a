#!/usr/bin/env python3
"""
简化的DeepSeek-INT8模型测试脚本
用于验证模型文件和基本配置
"""

import os
import sys
import json
import logging
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def check_model_files():
    """检查模型文件"""
    model_path = "/home/<USER>/deepseek-int8"
    
    logger.info("="*60)
    logger.info("检查DeepSeek-INT8模型文件")
    logger.info("="*60)
    
    if not os.path.exists(model_path):
        logger.error(f"❌ 模型路径不存在: {model_path}")
        return False
    
    logger.info(f"✅ 模型路径存在: {model_path}")
    
    # 检查关键文件
    required_files = [
        "config.json",
        "tokenizer.json", 
        "tokenizer_config.json"
    ]
    
    missing_files = []
    for file_name in required_files:
        file_path = os.path.join(model_path, file_name)
        if os.path.exists(file_path):
            logger.info(f"✅ 找到文件: {file_name}")
        else:
            logger.warning(f"⚠️  缺少文件: {file_name}")
            missing_files.append(file_name)
    
    # 检查模型权重文件
    weight_files = []
    for file_name in os.listdir(model_path):
        if file_name.endswith('.safetensors'):
            weight_files.append(file_name)
            logger.info(f"✅ 找到权重文件: {file_name}")
    
    if not weight_files:
        logger.error("❌ 未找到任何权重文件(.safetensors)")
        return False
    
    logger.info(f"✅ 总共找到 {len(weight_files)} 个权重文件")
    
    return len(missing_files) == 0

def check_model_config():
    """检查模型配置"""
    model_path = "/home/<USER>/deepseek-int8"
    config_file = os.path.join(model_path, "config.json")
    
    logger.info("\n" + "="*60)
    logger.info("检查模型配置")
    logger.info("="*60)
    
    try:
        with open(config_file, 'r') as f:
            config = json.load(f)
        
        logger.info("✅ 成功读取配置文件")
        
        # 检查关键配置项
        key_configs = [
            "model_type",
            "architectures", 
            "vocab_size",
            "hidden_size",
            "num_attention_heads",
            "num_hidden_layers"
        ]
        
        logger.info("\n关键配置项:")
        for key in key_configs:
            if key in config:
                logger.info(f"  {key}: {config[key]}")
            else:
                logger.warning(f"  ⚠️  缺少配置项: {key}")
        
        # 检查量化配置
        if "quantization_config" in config:
            logger.info(f"\n✅ 找到量化配置:")
            quant_config = config["quantization_config"]
            for key, value in quant_config.items():
                logger.info(f"  {key}: {value}")
        else:
            logger.warning("\n⚠️  未找到quantization_config，这可能是正常的")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 读取配置文件失败: {e}")
        return False

def check_tokenizer_config():
    """检查分词器配置"""
    model_path = "/home/<USER>/deepseek-int8"
    tokenizer_config_file = os.path.join(model_path, "tokenizer_config.json")
    
    logger.info("\n" + "="*60)
    logger.info("检查分词器配置")
    logger.info("="*60)
    
    try:
        with open(tokenizer_config_file, 'r') as f:
            tokenizer_config = json.load(f)
        
        logger.info("✅ 成功读取分词器配置文件")
        
        # 检查关键配置项
        key_configs = [
            "tokenizer_class",
            "vocab_size",
            "model_max_length"
        ]
        
        logger.info("\n分词器配置项:")
        for key in key_configs:
            if key in tokenizer_config:
                logger.info(f"  {key}: {tokenizer_config[key]}")
            else:
                logger.warning(f"  ⚠️  缺少配置项: {key}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 读取分词器配置文件失败: {e}")
        return False

def check_sglang_compatibility():
    """检查SGLang兼容性"""
    logger.info("\n" + "="*60)
    logger.info("检查SGLang兼容性")
    logger.info("="*60)
    
    try:
        # 添加sglang路径
        sglang_path = "/workspace/sglang/python"
        if sglang_path not in sys.path:
            sys.path.insert(0, sglang_path)
        
        import sglang as sgl
        logger.info(f"✅ SGLang导入成功，版本: {sgl.__version__}")
        
        # 检查量化支持
        try:
            from sglang.srt.model_executor.model_runner import ModelRunner
            logger.info("✅ ModelRunner导入成功")
        except ImportError as e:
            logger.warning(f"⚠️  ModelRunner导入失败: {e}")
        
        # 检查DeepSeek模型支持
        try:
            from sglang.srt.models.deepseek_nextn import DeepseekV3ForCausalLMNextN
            logger.info("✅ DeepSeek模型类导入成功")
        except ImportError as e:
            logger.warning(f"⚠️  DeepSeek模型类导入失败: {e}")
        
        return True
        
    except ImportError as e:
        logger.error(f"❌ SGLang导入失败: {e}")
        return False

def check_gpu_memory():
    """检查GPU内存"""
    logger.info("\n" + "="*60)
    logger.info("检查GPU内存")
    logger.info("="*60)
    
    try:
        import torch
        
        if torch.cuda.is_available():
            gpu_count = torch.cuda.device_count()
            logger.info(f"✅ 检测到 {gpu_count} 个GPU")
            
            for i in range(gpu_count):
                gpu_name = torch.cuda.get_device_name(i)
                total_memory = torch.cuda.get_device_properties(i).total_memory / 1024**3
                logger.info(f"  GPU {i}: {gpu_name}, 总内存: {total_memory:.2f} GB")
            
            # 检查当前GPU内存使用
            current_device = torch.cuda.current_device()
            allocated = torch.cuda.memory_allocated(current_device) / 1024**3
            cached = torch.cuda.memory_reserved(current_device) / 1024**3
            logger.info(f"  当前GPU内存使用: 已分配 {allocated:.2f} GB, 已缓存 {cached:.2f} GB")
            
            return True
        else:
            logger.error("❌ 未检测到可用的GPU")
            return False
            
    except Exception as e:
        logger.error(f"❌ 检查GPU内存失败: {e}")
        return False

def generate_summary_report():
    """生成总结报告"""
    logger.info("\n" + "="*80)
    logger.info("DeepSeek-INT8 模型环境检查总结报告")
    logger.info("="*80)
    
    results = {}
    
    # 执行各项检查
    logger.info("\n1. 检查模型文件...")
    results["model_files"] = check_model_files()
    
    logger.info("\n2. 检查模型配置...")
    results["model_config"] = check_model_config()
    
    logger.info("\n3. 检查分词器配置...")
    results["tokenizer_config"] = check_tokenizer_config()
    
    logger.info("\n4. 检查SGLang兼容性...")
    results["sglang_compatibility"] = check_sglang_compatibility()
    
    logger.info("\n5. 检查GPU内存...")
    results["gpu_memory"] = check_gpu_memory()
    
    # 生成总结
    total_checks = len(results)
    passed_checks = sum(1 for v in results.values() if v)
    
    logger.info("\n" + "="*80)
    logger.info("检查结果总结")
    logger.info("="*80)
    
    test_names = {
        "model_files": "模型文件检查",
        "model_config": "模型配置检查",
        "tokenizer_config": "分词器配置检查",
        "sglang_compatibility": "SGLang兼容性检查",
        "gpu_memory": "GPU内存检查"
    }
    
    for test_key, result in results.items():
        test_name = test_names.get(test_key, test_key)
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"  {test_name}: {status}")
    
    logger.info(f"\n总检查项: {total_checks}")
    logger.info(f"通过检查: {passed_checks}")
    logger.info(f"失败检查: {total_checks - passed_checks}")
    logger.info(f"通过率: {passed_checks/total_checks*100:.1f}%")
    
    # 保存报告
    import time
    report_data = {
        "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
        "model_path": "/home/<USER>/deepseek-int8",
        "total_checks": total_checks,
        "passed_checks": passed_checks,
        "success_rate": passed_checks/total_checks*100,
        "results": results
    }
    
    report_file = "deepseek_int8_environment_check_report.json"
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report_data, f, indent=2, ensure_ascii=False)
    
    logger.info(f"\n📄 详细报告已保存到: {report_file}")
    
    if passed_checks == total_checks:
        logger.info("\n🎉 所有检查通过！环境配置正常。")
        logger.info("建议：可以尝试运行推理测试。")
    else:
        logger.info(f"\n⚠️  有 {total_checks - passed_checks} 项检查失败。")
        logger.info("建议：请根据上述错误信息进行相应的修复。")

def main():
    """主函数"""
    try:
        generate_summary_report()
    except KeyboardInterrupt:
        logger.info("\n用户中断检查")
    except Exception as e:
        logger.error(f"检查过程中出现未预期的错误: {e}")
        import traceback
        logger.error(f"错误详情: {traceback.format_exc()}")

if __name__ == "__main__":
    main()
