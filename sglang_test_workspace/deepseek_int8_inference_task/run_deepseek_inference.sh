#!/bin/bash
"""
DeepSeek-INT8 SGLang推理启动脚本
严格遵循使用规则，使用服务器模式
"""

echo "🚀 DeepSeek-INT8 SGLang推理测试启动脚本"
echo "=============================================="

# 1. 激活虚拟环境
echo "1. 激活SGLang虚拟环境..."
source /workspace/sglang_test/bin/activate

# 2. 切换到工作目录
echo "2. 切换到工作目录..."
cd /workspace/sglang_test_workspace/deepseek_int8_inference_task

# 3. 检查Python环境
echo "3. 检查Python环境..."
which python
python --version

# 4. 检查SGLang安装
echo "4. 检查SGLang安装..."
python -c "import sglang; print(f'SGLang版本: {sglang.__version__}')" 2>/dev/null || echo "SGLang导入失败"

# 5. 检查模型路径
echo "5. 检查模型路径..."
if [ -d "/home/<USER>/deepseek-int8" ]; then
    echo "✅ 找到模型路径: /home/<USER>/deepseek-int8"
    ls -la /home/<USER>/deepseek-int8/ | head -10
else
    echo "❌ 模型路径不存在: /home/<USER>/deepseek-int8"
    echo "请确保模型已下载到指定路径"
    exit 1
fi

# 6. 启动SGLang服务器（后台运行）
echo "6. 启动SGLang服务器..."
echo "使用参数: --quantization w8a8_int8"
echo "=============================================="

# 启动服务器
python3 -m sglang.launch_server \
    --model-path /home/<USER>/deepseek-int8 \
    --quantization w8a8_int8 \
    --trust-remote-code \
    --port 30001 \
    --host 127.0.0.1 \
    --tp-size 1 \
    --mem-fraction-static 0.8 \
    --disable-cuda-graph &

SERVER_PID=$!
echo "SGLang服务器已启动，PID: $SERVER_PID"

# 等待服务器启动
echo "等待服务器启动..."
sleep 30

# 7. 运行推理脚本
echo "7. 运行DeepSeek-INT8推理脚本..."
echo "=============================================="
python deepseek_int8_inference_client.py

# 8. 关闭服务器
echo "8. 关闭SGLang服务器..."
kill $SERVER_PID
wait $SERVER_PID 2>/dev/null

echo "=============================================="
echo "✅ 推理测试完成"