#!/usr/bin/env python3
"""
基于SGLang的DeepSeek-INT8模型推理脚本
严格遵循使用规则，基于现有源码实现
"""

import os
import sys
import time
import json
import logging
from pathlib import Path
from typing import List, Dict, Any

# 添加sglang路径
sglang_path = "/workspace/sglang/python"
if sglang_path not in sys.path:
    sys.path.insert(0, sglang_path)

# 导入sglang模块
try:
    import sglang as sgl
    from sglang import function, system, user, assistant, gen, select
    print("✅ SGLang导入成功")
    print(f"SGLang版本: {sgl.__version__}")
except ImportError as e:
    print(f"❌ SGLang导入失败: {e}")
    print("请确保已正确安装SGLang")
    sys.exit(1)

def get_exception_traceback():
    """获取异常追踪"""
    import traceback
    return traceback.format_exc()

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class DeepSeekInt8InferenceConfig:
    """DeepSeek INT8推理配置"""

    def __init__(self):
        # 模型配置
        self.model_path = "/home/<USER>/deepseek-int8"
        self.quantization = "w8a8_int8"  # 使用W8A8 INT8量化

        # 服务器配置
        self.host = "127.0.0.1"
        self.port = 30001
        self.tp_size = 1  # 单GPU推理
        self.mem_fraction_static = 0.8

        # 推理配置
        self.max_tokens = 512
        self.temperature = 0.7
        self.top_p = 0.9
        self.max_new_tokens = 200

        # 测试配置
        self.test_questions = [
            "你好！请介绍一下DeepSeek模型的特点。",
            "什么是人工智能？请简要解释。",
            "请用一段话描述机器学习的基本概念。",
            "深度学习和传统机器学习有什么区别？",
            "请解释一下Transformer架构的核心思想。"
        ]

class DeepSeekInt8Inference:
    """DeepSeek INT8推理类"""

    def __init__(self, config: DeepSeekInt8InferenceConfig):
        self.config = config
        self.runtime = None

    def check_model_exists(self) -> bool:
        """检查模型是否存在"""
        if not os.path.exists(self.config.model_path):
            logger.error(f"模型路径不存在: {self.config.model_path}")
            return False

        # 检查关键文件
        config_file = os.path.join(self.config.model_path, "config.json")
        if not os.path.exists(config_file):
            logger.warning(f"未找到配置文件: {config_file}")
            return False

        logger.info(f"✅ 找到模型路径: {self.config.model_path}")
        return True

    def check_quantization_config(self) -> bool:
        """检查量化配置"""
        try:
            config_path = os.path.join(self.config.model_path, "config.json")
            with open(config_path, 'r') as f:
                model_config = json.load(f)

            # 检查是否有量化配置
            if "quantization_config" in model_config:
                quant_config = model_config["quantization_config"]
                logger.info(f"✅ 找到量化配置: {quant_config}")
                return True
            else:
                logger.warning("⚠️  模型配置中未找到quantization_config")
                # 对于INT8模型，有时配置可能在其他文件中
                return True

        except Exception as e:
            logger.error(f"检查量化配置失败: {e}")
            return False

    def initialize_runtime(self) -> bool:
        """初始化SGLang运行时"""
        try:
            logger.info("正在初始化SGLang运行时...")
            logger.info("注意：根据文档，需要使用--quantization w8a8_int8参数")

            # 基于现有源码和文档，使用正确的量化参数初始化
            self.runtime = sgl.Runtime(
                model_path=self.config.model_path,
                tokenizer_path=self.config.model_path,
                quantization="w8a8_int8",  # 明确指定w8a8_int8量化
                trust_remote_code=True,  # DeepSeek模型需要
                mem_fraction_static=self.config.mem_fraction_static,
                tp_size=self.config.tp_size,
                max_running_requests=8,  # 并发请求数
                disable_cuda_graph=True,  # 禁用CUDA图以避免量化问题
            )

            # 设置为默认后端
            sgl.set_default_backend(self.runtime)

            logger.info("✅ SGLang运行时初始化成功")
            return True

        except Exception as e:
            logger.error(f"❌ 运行时初始化失败: {e}")
            logger.error(f"错误详情: {get_exception_traceback()}")
            return False

    def shutdown_runtime(self):
        """关闭运行时"""
        if self.runtime:
            try:
                self.runtime.shutdown()
                logger.info("✅ 运行时已关闭")
            except Exception as e:
                logger.warning(f"关闭运行时时出现警告: {e}")

    def define_functions(self):
        """定义SGLang函数"""

        @function
        def chat_completion(s, question: str):
            """聊天完成功能"""
            s += system("你是一个有用的AI助手，请用中文回答问题。回答要准确、简洁、有帮助。")
            s += user(question)
            s += assistant(gen("response",
                              max_tokens=self.config.max_new_tokens,
                              temperature=self.config.temperature,
                              top_p=self.config.top_p))

        @function
        def structured_qa(s, question: str):
            """结构化问答"""
            s += system("你是一个专业的AI助手。请按照要求的格式回答问题。")
            s += user(f"请回答以下问题：{question}")
            s += assistant("我来回答这个问题：\n\n" +
                          gen("answer", max_tokens=150, temperature=0.5) +
                          "\n\n总结：" +
                          gen("summary", max_tokens=50, temperature=0.3))

        # 将函数绑定到实例
        self.chat_completion = chat_completion
        self.structured_qa = structured_qa

    def test_basic_inference(self) -> bool:
        """测试基本推理功能"""
        logger.info("="*60)
        logger.info("开始基本推理测试")
        logger.info("="*60)

        try:
            # 定义推理函数
            self.define_functions()

            results = []

            for i, question in enumerate(self.config.test_questions, 1):
                logger.info(f"\n--- 测试 {i}/{len(self.config.test_questions)} ---")
                logger.info(f"问题: {question}")

                start_time = time.time()

                # 执行推理
                state = self.chat_completion.run(question=question)
                response = state["response"]

                end_time = time.time()
                inference_time = end_time - start_time

                result = {
                    "question": question,
                    "response": response,
                    "inference_time": inference_time,
                    "tokens_per_second": len(response.split()) / inference_time if inference_time > 0 else 0
                }
                results.append(result)

                logger.info(f"回答: {response}")
                logger.info(f"推理时间: {inference_time:.2f}秒")
                logger.info(f"大约token/秒: {result['tokens_per_second']:.2f}")
                logger.info("-" * 50)

            # 计算统计信息
            total_time = sum(r["inference_time"] for r in results)
            avg_time = total_time / len(results)
            avg_tokens_per_sec = sum(r["tokens_per_second"] for r in results) / len(results)

            logger.info(f"\n📊 基本推理测试统计:")
            logger.info(f"总测试数: {len(results)}")
            logger.info(f"总时间: {total_time:.2f}秒")
            logger.info(f"平均推理时间: {avg_time:.2f}秒")
            logger.info(f"平均处理速度: {avg_tokens_per_sec:.2f} tokens/秒")

            return True

        except Exception as e:
            logger.error(f"❌ 基本推理测试失败: {e}")
            logger.error(f"错误详情: {get_exception_traceback()}")
            return False

    def test_structured_inference(self) -> bool:
        """测试结构化推理"""
        logger.info("\n" + "="*60)
        logger.info("开始结构化推理测试")
        logger.info("="*60)

        try:
            test_questions = [
                "什么是深度学习？",
                "解释一下注意力机制。",
                "大语言模型的训练过程是怎样的？"
            ]

            for i, question in enumerate(test_questions, 1):
                logger.info(f"\n--- 结构化测试 {i} ---")
                logger.info(f"问题: {question}")

                start_time = time.time()
                state = self.structured_qa.run(question=question)
                end_time = time.time()

                logger.info(f"详细回答: {state['answer']}")
                logger.info(f"简要总结: {state['summary']}")
                logger.info(f"推理时间: {end_time - start_time:.2f}秒")
                logger.info("-" * 50)

            logger.info("✅ 结构化推理测试完成")
            return True

        except Exception as e:
            logger.error(f"❌ 结构化推理测试失败: {e}")
            logger.error(f"错误详情: {get_exception_traceback()}")
            return False

    def test_classification_task(self) -> bool:
        """测试分类任务"""
        logger.info("\n" + "="*60)
        logger.info("开始分类任务测试")
        logger.info("="*60)

        try:
            @function
            def sentiment_classifier(s, text: str):
                s += system("你是一个文本情感分析助手。")
                s += user(f"请分析以下文本的情感倾向：'{text}'\n请从以下选项中选择：")
                s += select("sentiment", choices=["积极", "消极", "中性"])

            test_texts = [
                "今天天气真好，我心情很愉快！",
                "这个产品质量太差了，完全不值这个价格。",
                "这是一个关于人工智能技术的中性描述文档。",
                "太棒了！这个解决方案完美解决了我的问题。",
                "系统运行正常，没有发现异常情况。"
            ]

            for i, text in enumerate(test_texts, 1):
                logger.info(f"\n--- 分类测试 {i} ---")
                logger.info(f"文本: {text}")

                state = sentiment_classifier.run(text=text)
                sentiment = state["sentiment"]

                logger.info(f"情感分类: {sentiment}")
                logger.info("-" * 40)

            logger.info("✅ 分类任务测试完成")
            return True

        except Exception as e:
            logger.error(f"❌ 分类任务测试失败: {e}")
            logger.error(f"错误详情: {get_exception_traceback()}")
            return False

    def run_comprehensive_test(self) -> Dict[str, Any]:
        """运行综合测试"""
        logger.info("🚀 开始DeepSeek-INT8模型综合推理测试")
        logger.info(f"模型路径: {self.config.model_path}")
        logger.info(f"量化方式: {self.config.quantization}")

        results = {}

        # 1. 检查模型文件
        logger.info("\n1. 检查模型文件...")
        results["model_check"] = self.check_model_exists()

        # 2. 检查量化配置
        logger.info("\n2. 检查量化配置...")
        results["quantization_check"] = self.check_quantization_config()

        # 3. 初始化运行时
        logger.info("\n3. 初始化运行时...")
        results["runtime_init"] = self.initialize_runtime()

        if not results["runtime_init"]:
            logger.error("运行时初始化失败，跳过推理测试")
            return results

        try:
            # 4. 基本推理测试
            logger.info("\n4. 基本推理测试...")
            results["basic_inference"] = self.test_basic_inference()

            # 5. 结构化推理测试
            logger.info("\n5. 结构化推理测试...")
            results["structured_inference"] = self.test_structured_inference()

            # 6. 分类任务测试
            logger.info("\n6. 分类任务测试...")
            results["classification_task"] = self.test_classification_task()

        except Exception as e:
            logger.error(f"推理测试过程中出现错误: {e}")
        finally:
            # 关闭运行时
            self.shutdown_runtime()

        return results

    def generate_report(self, results: Dict[str, Any]):
        """生成测试报告"""
        logger.info("\n" + "="*80)
        logger.info("DeepSeek-INT8 推理测试报告")
        logger.info("="*80)

        total_tests = len(results)
        passed_tests = sum(1 for v in results.values() if v)

        logger.info(f"测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info(f"模型路径: {self.config.model_path}")
        logger.info(f"量化方式: {self.config.quantization}")
        logger.info(f"总测试数: {total_tests}")
        logger.info(f"通过测试: {passed_tests}")
        logger.info(f"失败测试: {total_tests - passed_tests}")
        logger.info(f"通过率: {passed_tests/total_tests*100:.1f}%")

        logger.info("\n详细结果:")
        test_names = {
            "model_check": "模型文件检查",
            "quantization_check": "量化配置检查",
            "runtime_init": "运行时初始化",
            "basic_inference": "基本推理测试",
            "structured_inference": "结构化推理测试",
            "classification_task": "分类任务测试"
        }

        for test_key, result in results.items():
            test_name = test_names.get(test_key, test_key)
            status = "✅ 通过" if result else "❌ 失败"
            logger.info(f"  {test_name}: {status}")

        # 保存报告到文件
        report_data = {
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "model_path": self.config.model_path,
            "quantization": self.config.quantization,
            "total_tests": total_tests,
            "passed_tests": passed_tests,
            "success_rate": passed_tests/total_tests*100,
            "results": results
        }

        report_file = "deepseek_int8_inference_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)

        logger.info(f"\n📄 详细报告已保存到: {report_file}")

        if passed_tests == total_tests:
            logger.info("\n🎉 所有测试通过！DeepSeek-INT8模型推理功能正常。")
        else:
            logger.info(f"\n⚠️  有 {total_tests - passed_tests} 个测试失败，请检查相关配置。")

def main():
    """主函数"""
    try:
        # 创建配置
        config = DeepSeekInt8InferenceConfig()

        # 创建推理器
        inference = DeepSeekInt8Inference(config)

        # 运行综合测试
        results = inference.run_comprehensive_test()

        # 生成报告
        inference.generate_report(results)

    except KeyboardInterrupt:
        logger.info("\n用户中断测试")
    except Exception as e:
        logger.error(f"测试过程中出现未预期的错误: {e}")
        logger.error(f"错误详情: {get_exception_traceback()}")

if __name__ == "__main__":
    main()