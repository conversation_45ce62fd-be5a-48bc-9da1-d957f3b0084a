#!/usr/bin/env python3
"""
基于SGLang的DeepSeek-INT8模型推理客户端脚本
连接到SGLang服务器进行推理测试
严格遵循使用规则，基于现有源码实现
"""

import os
import sys
import time
import json
import logging
import requests
from pathlib import Path
from typing import List, Dict, Any

# 添加sglang路径
sglang_path = "/workspace/sglang/python"
if sglang_path not in sys.path:
    sys.path.insert(0, sglang_path)

# 导入sglang模块
try:
    import sglang as sgl
    from sglang import function, system, user, assistant, gen, select
    print("✅ SGLang导入成功")
    print(f"SGLang版本: {sgl.__version__}")
except ImportError as e:
    print(f"❌ SGLang导入失败: {e}")
    print("请确保已正确安装SGLang")
    sys.exit(1)

def get_exception_traceback():
    """获取异常追踪"""
    import traceback
    return traceback.format_exc()

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class DeepSeekInt8InferenceClient:
    """DeepSeek INT8推理客户端"""
    
    def __init__(self):
        # 服务器配置
        self.server_url = "http://127.0.0.1:30001"
        self.model_path = "/home/<USER>/deepseek-int8"
        self.quantization = "w8a8_int8"
        
        # 推理配置
        self.max_tokens = 512
        self.temperature = 0.7
        self.top_p = 0.9
        self.max_new_tokens = 200
        
        # 测试配置
        self.test_questions = [
            "你好！请介绍一下DeepSeek模型的特点。",
            "什么是人工智能？请简要解释。",
            "请用一段话描述机器学习的基本概念。",
            "深度学习和传统机器学习有什么区别？",
            "请解释一下Transformer架构的核心思想。"
        ]
        
    def check_server_status(self) -> bool:
        """检查服务器状态"""
        try:
            response = requests.get(f"{self.server_url}/health", timeout=10)
            if response.status_code == 200:
                logger.info("✅ SGLang服务器运行正常")
                return True
            else:
                logger.error(f"❌ 服务器状态异常: {response.status_code}")
                return False
        except Exception as e:
            logger.error(f"❌ 无法连接到服务器: {e}")
            return False
    
    def initialize_client(self) -> bool:
        """初始化客户端连接"""
        try:
            logger.info("正在初始化SGLang客户端...")
            
            # 设置SGLang后端为服务器端点
            sgl.set_default_backend(sgl.RuntimeEndpoint(self.server_url))
            
            logger.info("✅ SGLang客户端初始化成功")
            return True
            
        except Exception as e:
            logger.error(f"❌ 客户端初始化失败: {e}")
            logger.error(f"错误详情: {get_exception_traceback()}")
            return False
    
    def define_functions(self):
        """定义SGLang函数"""
        
        @function
        def chat_completion(s, question: str):
            """聊天完成功能"""
            s += system("你是一个有用的AI助手，请用中文回答问题。回答要准确、简洁、有帮助。")
            s += user(question)
            s += assistant(gen("response", 
                              max_tokens=self.max_new_tokens, 
                              temperature=self.temperature,
                              top_p=self.top_p))
        
        @function  
        def structured_qa(s, question: str):
            """结构化问答"""
            s += system("你是一个专业的AI助手。请按照要求的格式回答问题。")
            s += user(f"请回答以下问题：{question}")
            s += assistant("我来回答这个问题：\n\n" + 
                          gen("answer", max_tokens=150, temperature=0.5) + 
                          "\n\n总结：" + 
                          gen("summary", max_tokens=50, temperature=0.3))
        
        # 将函数绑定到实例
        self.chat_completion = chat_completion
        self.structured_qa = structured_qa
    
    def test_basic_inference(self) -> bool:
        """测试基本推理功能"""
        logger.info("="*60)
        logger.info("开始基本推理测试")
        logger.info("="*60)
        
        try:
            # 定义推理函数
            self.define_functions()
            
            results = []
            
            for i, question in enumerate(self.test_questions, 1):
                logger.info(f"\n--- 测试 {i}/{len(self.test_questions)} ---")
                logger.info(f"问题: {question}")
                
                start_time = time.time()
                
                # 执行推理
                state = self.chat_completion.run(question=question)
                response = state["response"]
                
                end_time = time.time()
                inference_time = end_time - start_time
                
                result = {
                    "question": question,
                    "response": response,
                    "inference_time": inference_time,
                    "tokens_per_second": len(response.split()) / inference_time if inference_time > 0 else 0
                }
                results.append(result)
                
                logger.info(f"回答: {response}")
                logger.info(f"推理时间: {inference_time:.2f}秒")
                logger.info(f"大约token/秒: {result['tokens_per_second']:.2f}")
                logger.info("-" * 50)
            
            # 计算统计信息
            total_time = sum(r["inference_time"] for r in results)
            avg_time = total_time / len(results)
            avg_tokens_per_sec = sum(r["tokens_per_second"] for r in results) / len(results)
            
            logger.info(f"\n📊 基本推理测试统计:")
            logger.info(f"总测试数: {len(results)}")
            logger.info(f"总时间: {total_time:.2f}秒")
            logger.info(f"平均推理时间: {avg_time:.2f}秒")
            logger.info(f"平均处理速度: {avg_tokens_per_sec:.2f} tokens/秒")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 基本推理测试失败: {e}")
            logger.error(f"错误详情: {get_exception_traceback()}")
            return False
    
    def test_structured_inference(self) -> bool:
        """测试结构化推理"""
        logger.info("\n" + "="*60)
        logger.info("开始结构化推理测试")
        logger.info("="*60)
        
        try:
            test_questions = [
                "什么是深度学习？",
                "解释一下注意力机制。",
                "大语言模型的训练过程是怎样的？"
            ]
            
            for i, question in enumerate(test_questions, 1):
                logger.info(f"\n--- 结构化测试 {i} ---")
                logger.info(f"问题: {question}")
                
                start_time = time.time()
                state = self.structured_qa.run(question=question)
                end_time = time.time()
                
                logger.info(f"详细回答: {state['answer']}")
                logger.info(f"简要总结: {state['summary']}")
                logger.info(f"推理时间: {end_time - start_time:.2f}秒")
                logger.info("-" * 50)
            
            logger.info("✅ 结构化推理测试完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ 结构化推理测试失败: {e}")
            logger.error(f"错误详情: {get_exception_traceback()}")
            return False
    
    def test_classification_task(self) -> bool:
        """测试分类任务"""
        logger.info("\n" + "="*60)
        logger.info("开始分类任务测试")
        logger.info("="*60)
        
        try:
            @function
            def sentiment_classifier(s, text: str):
                s += system("你是一个文本情感分析助手。")
                s += user(f"请分析以下文本的情感倾向：'{text}'\n请从以下选项中选择：")
                s += select("sentiment", choices=["积极", "消极", "中性"])
            
            test_texts = [
                "今天天气真好，我心情很愉快！",
                "这个产品质量太差了，完全不值这个价格。",
                "这是一个关于人工智能技术的中性描述文档。",
                "太棒了！这个解决方案完美解决了我的问题。",
                "系统运行正常，没有发现异常情况。"
            ]
            
            for i, text in enumerate(test_texts, 1):
                logger.info(f"\n--- 分类测试 {i} ---")
                logger.info(f"文本: {text}")
                
                state = sentiment_classifier.run(text=text)
                sentiment = state["sentiment"]
                
                logger.info(f"情感分类: {sentiment}")
                logger.info("-" * 40)
            
            logger.info("✅ 分类任务测试完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ 分类任务测试失败: {e}")
            logger.error(f"错误详情: {get_exception_traceback()}")
            return False
    
    def run_comprehensive_test(self) -> Dict[str, Any]:
        """运行综合测试"""
        logger.info("🚀 开始DeepSeek-INT8模型综合推理测试（客户端模式）")
        logger.info(f"服务器地址: {self.server_url}")
        logger.info(f"模型路径: {self.model_path}")
        logger.info(f"量化方式: {self.quantization}")
        
        results = {}
        
        # 1. 检查服务器状态
        logger.info("\n1. 检查服务器状态...")
        results["server_check"] = self.check_server_status()
        
        if not results["server_check"]:
            logger.error("服务器连接失败，跳过推理测试")
            return results
        
        # 2. 初始化客户端
        logger.info("\n2. 初始化客户端...")
        results["client_init"] = self.initialize_client()
        
        if not results["client_init"]:
            logger.error("客户端初始化失败，跳过推理测试")
            return results
        
        try:
            # 3. 基本推理测试
            logger.info("\n3. 基本推理测试...")
            results["basic_inference"] = self.test_basic_inference()
            
            # 4. 结构化推理测试
            logger.info("\n4. 结构化推理测试...")
            results["structured_inference"] = self.test_structured_inference()
            
            # 5. 分类任务测试
            logger.info("\n5. 分类任务测试...")
            results["classification_task"] = self.test_classification_task()
            
        except Exception as e:
            logger.error(f"推理测试过程中出现错误: {e}")
        
        return results
    
    def generate_report(self, results: Dict[str, Any]):
        """生成测试报告"""
        logger.info("\n" + "="*80)
        logger.info("DeepSeek-INT8 推理测试报告（客户端模式）")
        logger.info("="*80)
        
        total_tests = len(results)
        passed_tests = sum(1 for v in results.values() if v)
        
        logger.info(f"测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info(f"服务器地址: {self.server_url}")
        logger.info(f"模型路径: {self.model_path}")
        logger.info(f"量化方式: {self.quantization}")
        logger.info(f"总测试数: {total_tests}")
        logger.info(f"通过测试: {passed_tests}")
        logger.info(f"失败测试: {total_tests - passed_tests}")
        logger.info(f"通过率: {passed_tests/total_tests*100:.1f}%")
        
        logger.info("\n详细结果:")
        test_names = {
            "server_check": "服务器连接检查",
            "client_init": "客户端初始化",
            "basic_inference": "基本推理测试",
            "structured_inference": "结构化推理测试",
            "classification_task": "分类任务测试"
        }
        
        for test_key, result in results.items():
            test_name = test_names.get(test_key, test_key)
            status = "✅ 通过" if result else "❌ 失败"
            logger.info(f"  {test_name}: {status}")
        
        # 保存报告到文件
        report_data = {
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "server_url": self.server_url,
            "model_path": self.model_path,
            "quantization": self.quantization,
            "total_tests": total_tests,
            "passed_tests": passed_tests,
            "success_rate": passed_tests/total_tests*100,
            "results": results
        }
        
        report_file = "deepseek_int8_inference_client_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)
        
        logger.info(f"\n📄 详细报告已保存到: {report_file}")
        
        if passed_tests == total_tests:
            logger.info("\n🎉 所有测试通过！DeepSeek-INT8模型推理功能正常。")
        else:
            logger.info(f"\n⚠️  有 {total_tests - passed_tests} 个测试失败，请检查相关配置。")

def main():
    """主函数"""
    try:
        # 创建推理客户端
        client = DeepSeekInt8InferenceClient()
        
        # 运行综合测试
        results = client.run_comprehensive_test()
        
        # 生成报告
        client.generate_report(results)
        
    except KeyboardInterrupt:
        logger.info("\n用户中断测试")
    except Exception as e:
        logger.error(f"测试过程中出现未预期的错误: {e}")
        logger.error(f"错误详情: {get_exception_traceback()}")

if __name__ == "__main__":
    main()
