#!/usr/bin/env python3
"""
SGLang DeepSeek算子深度分析脚本
直接hook到SGLang的量化层和模型层，捕获所有关键算子调用
"""

import os
import sys
import traceback
import json
import torch
import torch.nn as nn
import numpy as np
from typing import Dict, List, Tuple, Any, Optional
from collections import defaultdict
import time
import threading
from functools import wraps

# 必须在导入 sglang 前设置分布式/通信相关环境
os.environ.setdefault("SGL_DISABLE_TP_MEMORY_INBALANCE_CHECK", "1")
os.environ.setdefault("UCX_VFS_ENABLE", "n") 
os.environ.setdefault("NCCL_P2P_DISABLE", "1")
os.environ.setdefault("NCCL_IB_DISABLE", "1")
os.environ.setdefault("SGLANG_IS_FLASHINFER_AVAILABLE", "false")

class SGLangOperatorProfiler:
    """SGLang算子性能分析器"""
    
    def __init__(self):
        self.operations = []
        self.layer_shapes = {}
        self.start_time = time.time()
        self.current_step = "initialization"
        self.layer_count = defaultdict(int)
        
    def log_operation(self, op_type: str, layer_name: str, input_shapes: List, 
                     output_shapes: List, extra_info: Dict = None):
        """记录算子操作"""
        timestamp = time.time() - self.start_time
        
        record = {
            'timestamp': timestamp,
            'step': self.current_step,
            'op_type': op_type,
            'layer_name': layer_name,
            'input_shapes': input_shapes,
            'output_shapes': output_shapes,
            'extra_info': extra_info or {}
        }
        
        self.operations.append(record)
        
        # 实时输出关键信息
        print(f"[{timestamp:.3f}s] [{self.current_step}] {op_type} @ {layer_name}")
        print(f"  输入shape: {input_shapes}")
        print(f"  输出shape: {output_shapes}")
        if extra_info:
            print(f"  额外信息: {extra_info}")
        print()
        
    def set_step(self, step_name: str):
        """设置当前执行步骤"""
        self.current_step = step_name
        print(f"\n=== 切换到步骤: {step_name} ===")
        
    def analyze_and_save(self, filename: str):
        """分析并保存结果"""
        # 按算子类型统计
        op_stats = defaultdict(int)
        layer_stats = defaultdict(int)
        
        for op in self.operations:
            op_stats[op['op_type']] += 1
            layer_stats[op['layer_name']] += 1
            
        # 分析shape模式
        shape_patterns = {}
        for op in self.operations:
            key = f"{op['op_type']}_{op['layer_name']}"
            if key not in shape_patterns:
                shape_patterns[key] = []
            shape_patterns[key].append({
                'input_shapes': op['input_shapes'],
                'output_shapes': op['output_shapes'],
                'step': op['step']
            })
            
        report = {
            'summary': {
                'total_operations': len(self.operations),
                'execution_time': time.time() - self.start_time,
                'op_statistics': dict(op_stats),
                'layer_statistics': dict(layer_stats)
            },
            'detailed_operations': self.operations,
            'shape_patterns': shape_patterns,
            'analysis': self._analyze_deepseek_patterns()
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
            
        print(f"\n详细报告已保存到: {filename}")
        
        # 打印统计信息
        print("\n=== 算子统计 ===")
        for op_type, count in sorted(op_stats.items(), key=lambda x: x[1], reverse=True):
            print(f"{op_type}: {count} 次")
            
        print("\n=== 层级统计 ===")  
        for layer_name, count in sorted(layer_stats.items(), key=lambda x: x[1], reverse=True)[:10]:
            print(f"{layer_name}: {count} 次")
            
    def _analyze_deepseek_patterns(self):
        """分析DeepSeek特定的算子模式"""
        patterns = {
            'attention_patterns': [],
            'mlp_patterns': [],
            'moe_patterns': [],
            'quantization_patterns': []
        }
        
        for op in self.operations:
            op_type = op['op_type']
            layer_name = op['layer_name']
            
            # 分类算子
            if 'attn' in layer_name.lower() or 'attention' in layer_name.lower():
                patterns['attention_patterns'].append(op)
            elif 'mlp' in layer_name.lower():
                patterns['mlp_patterns'].append(op)
            elif 'moe' in layer_name.lower() or 'expert' in layer_name.lower():
                patterns['moe_patterns'].append(op)
            elif 'int8' in op_type or 'quant' in op_type:
                patterns['quantization_patterns'].append(op)
                
        return patterns

# 全局分析器
profiler = SGLangOperatorProfiler()

# Hook函数来监控SGLang的关键组件
def hook_sglang_components():
    """Hook SGLang的关键组件"""
    
    # Hook linear层
    def hook_linear_forward(self, input, *args, **kwargs):
        input_shapes = [tuple(input.shape)] if hasattr(input, 'shape') else [None]
        
        # 调用原始forward
        output = self._original_forward(input, *args, **kwargs)
        
        # 提取输出shape
        if isinstance(output, tuple):
            output_shapes = [tuple(o.shape) if hasattr(o, 'shape') else None for o in output]
        else:
            output_shapes = [tuple(output.shape)] if hasattr(output, 'shape') else [None]
            
        # 记录算子信息
        layer_name = getattr(self, '_layer_name', self.__class__.__name__)
        extra_info = {
            'has_weight': hasattr(self, 'weight'),
            'has_bias': hasattr(self, 'bias'),
            'weight_shape': tuple(self.weight.shape) if hasattr(self, 'weight') else None
        }
        
        profiler.log_operation('linear_forward', layer_name, input_shapes, output_shapes, extra_info)
        
        return output
    
    # Hook量化函数
    def hook_int8_scaled_mm(*args, **kwargs):
        # 提取输入tensor的shape
        input_shapes = []
        for arg in args:
            if hasattr(arg, 'shape'):
                input_shapes.append(tuple(arg.shape))
            else:
                input_shapes.append(None)
                
        # 调用原始函数
        result = profiler._original_int8_scaled_mm(*args, **kwargs)
        
        # 提取输出shape
        output_shapes = [tuple(result.shape)] if hasattr(result, 'shape') else [None]
        
        extra_info = {
            'kwargs': {k: str(v) for k, v in kwargs.items() if not hasattr(v, 'shape')}
        }
        
        profiler.log_operation('int8_scaled_mm', 'quantized_linear', input_shapes, output_shapes, extra_info)
        
        return result
    
    # Hook attention操作
    def hook_attention_forward(self, q, k, v, forward_batch, *args, **kwargs):
        input_shapes = []
        for tensor in [q, k, v]:
            if tensor is not None and hasattr(tensor, 'shape'):
                input_shapes.append(tuple(tensor.shape))
            else:
                input_shapes.append(None)
                
        # 调用原始forward
        output = self._original_forward(q, k, v, forward_batch, *args, **kwargs)
        
        # 提取输出shape
        output_shapes = [tuple(output.shape)] if hasattr(output, 'shape') else [None]
        
        layer_name = getattr(self, '_layer_name', f"attention_layer_{self.layer_id}")
        extra_info = {
            'layer_id': getattr(self, 'layer_id', -1),
            'num_heads': getattr(self, 'tp_q_head_num', -1),
            'head_dim': getattr(self, 'head_dim', -1)
        }
        
        profiler.log_operation('attention_forward', layer_name, input_shapes, output_shapes, extra_info)
        
        return output
    
    # 应用hooks
    try:
        # Hook torch的量化函数
        import sgl_kernel
        if hasattr(sgl_kernel, 'int8_scaled_mm'):
            profiler._original_int8_scaled_mm = sgl_kernel.int8_scaled_mm
            sgl_kernel.int8_scaled_mm = hook_int8_scaled_mm
            print("已Hook sgl_kernel.int8_scaled_mm")
    except ImportError:
        print("sgl_kernel不可用，跳过量化函数hook")
    
    # Hook会在模型加载后应用到具体的层
    profiler._hook_linear_forward = hook_linear_forward
    profiler._hook_attention_forward = hook_attention_forward

def apply_hooks_to_model(model):
    """将hooks应用到已加载的模型"""
    hook_count = 0
    
    def apply_to_module(module, prefix=""):
        nonlocal hook_count
        
        # Hook linear层
        if hasattr(module, 'forward') and hasattr(module, 'weight'):
            if not hasattr(module, '_original_forward'):
                module._original_forward = module.forward
                module._layer_name = f"{prefix}_{module.__class__.__name__}"
                module.forward = profiler._hook_linear_forward.__get__(module, module.__class__)
                hook_count += 1
                
        # Hook attention层
        if hasattr(module, 'forward') and hasattr(module, 'layer_id'):
            if not hasattr(module, '_original_forward'):
                module._original_forward = module.forward
                module._layer_name = f"{prefix}_attention_{module.layer_id}"
                module.forward = profiler._hook_attention_forward.__get__(module, module.__class__)
                hook_count += 1
                
        # 递归处理子模块
        for name, child in module.named_children():
            child_prefix = f"{prefix}.{name}" if prefix else name
            apply_to_module(child, child_prefix)
    
    apply_to_module(model)
    print(f"已应用 {hook_count} 个hooks到模型")
    return hook_count

import sglang as sgl

MODEL_PATH = os.environ.get("MODEL_PATH", "/home/<USER>/deepseek-int8")

def main():
    """主函数"""
    llm = None
    try:
        print("开始初始化SGLang引擎...")
        profiler.set_step("engine_initialization")
        
        # 设置hooks
        hook_sglang_components()
        
        # 初始化引擎
        llm = sgl.Engine(
            model_path=MODEL_PATH,
            tp_size=4,
            quantization="w8a8_int8", 
            trust_remote_code=True,
            dist_init_addr="127.0.0.1:8001",
            disable_cuda_graph=True,
            log_level="info",
        )
        
        print("引擎初始化完成")
        profiler.set_step("model_analysis")
        
        # 应用hooks到已加载的模型
        if hasattr(llm, 'model_runner') and hasattr(llm.model_runner, 'model'):
            hook_count = apply_hooks_to_model(llm.model_runner.model)
            print(f"模型hooks应用完成，共 {hook_count} 个")
            
            # 分析模型结构
            print("\n=== 模型结构分析 ===")
            for name, module in llm.model_runner.model.named_modules():
                if len(list(module.children())) == 0:  # 叶子节点
                    if hasattr(module, 'weight'):
                        weight_shape = tuple(module.weight.shape)
                        print(f"{name}: {module.__class__.__name__} {weight_shape}")
        
        # 开始推理测试
        print("\n开始推理测试...")
        profiler.set_step("inference")
        
        prompt = "请解释什么是人工智能。"
        sampling_params = {"max_new_tokens": 20, "temperature": 0.1}
        
        print(f"输入: {prompt}")
        
        # 执行推理
        output = llm.generate(prompt=prompt, sampling_params=sampling_params)
        
        print(f"输出: {output.get('text', output)}")
        
        profiler.set_step("completed")
        
        # 保存分析报告
        report_filename = f"sglang_deepseek_analysis_{int(time.time())}.json"
        profiler.analyze_and_save(report_filename)
        
        # 对比期望的算子patterns
        print("\n=== 关键算子shape分析 ===")
        
        # 统计不同类型的操作
        attention_ops = [op for op in profiler.operations if 'attention' in op['layer_name']]
        linear_ops = [op for op in profiler.operations if op['op_type'] == 'linear_forward']
        quant_ops = [op for op in profiler.operations if 'int8' in op['op_type']]
        
        print(f"Attention操作: {len(attention_ops)} 次")
        print(f"Linear操作: {len(linear_ops)} 次") 
        print(f"量化操作: {len(quant_ops)} 次")
        
        # 分析关键的shape模式
        if quant_ops:
            print("\n量化算子shape模式:")
            for op in quant_ops[:5]:  # 显示前5个
                print(f"  {op['input_shapes']} -> {op['output_shapes']}")
                
        return 0
        
    except Exception as e:
        print(f"[ERROR] 执行出错: {e}")
        print(traceback.format_exc())
        return 1
    finally:
        if llm is not None:
            try:
                llm.shutdown()
            except Exception:
                pass

if __name__ == "__main__":
    sys.exit(main())
