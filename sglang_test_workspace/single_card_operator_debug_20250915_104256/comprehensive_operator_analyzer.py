#!/usr/bin/env python3
"""
SGLang 全面算子性能分析脚本
专门针对 Prefill 和 Decode 两个阶段进行所有计算相关算子的性能和shape分析
使用 TP=2 (GPU 0,1) 进行测试
"""

import os
import sys
import traceback
import json
import torch
import torch.nn.functional as F
import numpy as np
from typing import Dict, List, Tuple, Any, Optional
from collections import defaultdict
import time
import threading
from functools import wraps
import psutil
import gc

# 设置环境变量
os.environ.setdefault("SGL_DISABLE_TP_MEMORY_INBALANCE_CHECK", "1")
os.environ.setdefault("UCX_VFS_ENABLE", "n") 
os.environ.setdefault("NCCL_P2P_DISABLE", "1")
os.environ.setdefault("NCCL_IB_DISABLE", "1")
os.environ.setdefault("SGLANG_IS_FLASHINFER_AVAILABLE", "false")
os.environ.setdefault("CUDA_VISIBLE_DEVICES", "0,1")  # 限制使用GPU 0,1

class ComprehensiveOperatorAnalyzer:
    """全面的算子性能分析器"""
    
    def __init__(self):
        self.operation_logs = []
        self.performance_stats = {}
        self.memory_usage = {}
        self.phase_timings = {}
        self.start_time = time.time()
        self.current_phase = "initialization"
        self.current_layer = None
        self.token_count = 0
        self.prefill_complete = False
        self.lock = threading.Lock()
        
        # 新增：更细粒度的阶段追踪
        self.substage = None  # prefill_attention, prefill_mlp, decode_attention, decode_mlp等
        
    def set_phase(self, phase: str, substage: str = None):
        """设置当前推理阶段和子阶段"""
        self.current_phase = phase
        self.substage = substage
        current_time = time.time()
        
        phase_key = f"{phase}_{substage}" if substage else phase
        if phase_key not in self.phase_timings:
            self.phase_timings[phase_key] = []
        self.phase_timings[phase_key].append(current_time - self.start_time)
        
        print(f"\n[PHASE] {phase} -> {substage if substage else 'main'}")
        
    def detect_prefill_decode_transition(self, input_shapes: List[Tuple]):
        """自动检测prefill到decode的转换"""
        # 通过sequence length的变化来判断
        for shape in input_shapes:
            if len(shape) >= 2:
                seq_len = shape[1] if len(shape) > 1 else shape[0]
                if isinstance(seq_len, int):
                    if seq_len > 1 and not self.prefill_complete:
                        # 检测到长序列，应该是prefill阶段
                        if self.current_phase != "prefill":
                            self.set_phase("prefill")
                        return "prefill"
                    elif seq_len == 1 and (self.prefill_complete or self.token_count > 0):
                        # 检测到单token，应该是decode阶段
                        if not self.prefill_complete:
                            self.prefill_complete = True
                            print(f"[TRANSITION] Prefill -> Decode 转换检测到")
                        if self.current_phase != "decode":
                            self.set_phase("decode")
                        return "decode"
        return self.current_phase
        
    def set_current_layer(self, layer_name: str, layer_id: int = None):
        """设置当前层信息"""
        self.current_layer = {
            'name': layer_name,
            'id': layer_id,
            'timestamp': time.time() - self.start_time
        }
        
    def log_operation(self, op_name: str, input_tensors: List[torch.Tensor], 
                     output_tensors: List[torch.Tensor], extra_info: Dict = None,
                     execution_time: float = None):
        """记录算子操作和性能数据"""
        with self.lock:
            # 提取tensor信息
            input_info = []
            total_input_elements = 0
            for tensor in input_tensors:
                if hasattr(tensor, 'shape') and hasattr(tensor, 'dtype'):
                    shape = tuple(tensor.shape)
                    dtype = str(tensor.dtype)
                    numel = tensor.numel()
                    total_input_elements += numel
                    
                    # 计算内存使用
                    element_size = tensor.element_size() if hasattr(tensor, 'element_size') else 4
                    memory_mb = (numel * element_size) / (1024 * 1024)
                    
                    input_info.append({
                        'shape': shape,
                        'dtype': dtype,
                        'numel': numel,
                        'memory_mb': memory_mb
                    })
                else:
                    input_info.append({'shape': None, 'dtype': None, 'numel': 0, 'memory_mb': 0})
                    
            output_info = []
            total_output_elements = 0
            for tensor in output_tensors:
                if hasattr(tensor, 'shape') and hasattr(tensor, 'dtype'):
                    shape = tuple(tensor.shape)
                    dtype = str(tensor.dtype)
                    numel = tensor.numel()
                    total_output_elements += numel
                    
                    element_size = tensor.element_size() if hasattr(tensor, 'element_size') else 4
                    memory_mb = (numel * element_size) / (1024 * 1024)
                    
                    output_info.append({
                        'shape': shape,
                        'dtype': dtype,
                        'numel': numel,
                        'memory_mb': memory_mb
                    })
                else:
                    output_info.append({'shape': None, 'dtype': None, 'numel': 0, 'memory_mb': 0})
            
            # 自动检测阶段
            input_shapes = [info['shape'] for info in input_info if info['shape']]
            detected_phase = self.detect_prefill_decode_transition(input_shapes)
            
            # 智能推断子阶段
            substage = self.infer_substage(op_name, input_shapes)
            if substage != self.substage:
                self.substage = substage
            
            # 计算算子复杂度（FLOPs估算）
            flops = self.estimate_flops(op_name, input_info, output_info)
            
            record = {
                'timestamp': time.time() - self.start_time,
                'phase': detected_phase,
                'substage': substage,
                'operation': op_name,
                'current_layer': self.current_layer,
                'input_info': input_info,
                'output_info': output_info,
                'total_input_elements': total_input_elements,
                'total_output_elements': total_output_elements,
                'estimated_flops': flops,
                'execution_time_ms': execution_time * 1000 if execution_time else None,
                'extra_info': extra_info or {}
            }
            
            self.operation_logs.append(record)
            
            # 更新性能统计
            op_key = f"{detected_phase}_{substage}_{op_name}"
            if op_key not in self.performance_stats:
                self.performance_stats[op_key] = {
                    'count': 0,
                    'total_flops': 0,
                    'total_time_ms': 0,
                    'total_input_elements': 0,
                    'total_output_elements': 0,
                    'shapes': []
                }
            
            stats = self.performance_stats[op_key]
            stats['count'] += 1
            stats['total_flops'] += flops
            stats['total_input_elements'] += total_input_elements
            stats['total_output_elements'] += total_output_elements
            if execution_time:
                stats['total_time_ms'] += execution_time * 1000
            
            # 记录shape模式（避免重复）
            shape_pattern = {
                'input_shapes': [info['shape'] for info in input_info],
                'output_shapes': [info['shape'] for info in output_info]
            }
            if shape_pattern not in stats['shapes']:
                stats['shapes'].append(shape_pattern)
            
            # 实时打印关键信息
            layer_info = f"Layer: {self.current_layer['name'] if self.current_layer else 'Unknown'}"
            if self.current_layer and self.current_layer.get('id') is not None:
                layer_info += f"[{self.current_layer['id']}]"
                
            print(f"[{detected_phase}:{substage}] [{op_name}] {layer_info}")
            print(f"  输入: {[info['shape'] for info in input_info]} -> 输出: {[info['shape'] for info in output_info]}")
            print(f"  FLOPs: {flops:,}, 内存: {sum(info['memory_mb'] for info in input_info):.2f}MB")
            if execution_time:
                print(f"  执行时间: {execution_time*1000:.3f}ms")
                
    def infer_substage(self, op_name: str, input_shapes: List[Tuple]) -> str:
        """根据算子名称和输入shape推断子阶段"""
        # 根据算子类型和特征推断
        if 'attention' in op_name.lower() or any(['wq' in op_name.lower(), 'wk' in op_name.lower(), 'wv' in op_name.lower()]):
            return "attention"
        elif any(['up' in op_name.lower(), 'gate' in op_name.lower(), 'down' in op_name.lower(), 'mlp' in op_name.lower()]):
            return "mlp"
        elif 'embed' in op_name.lower():
            return "embedding"
        elif 'norm' in op_name.lower():
            return "normalization"
        elif op_name in ['bmm', 'matmul']:
            # 根据shape特征进一步推断
            if input_shapes:
                for shape in input_shapes:
                    if len(shape) == 3 and shape[1] > 1:  # batch_size, seq_len, hidden
                        return "attention_compute"
                    elif len(shape) == 2:
                        return "linear_projection"
            return "general_compute"
        else:
            return "other"
            
    def estimate_flops(self, op_name: str, input_info: List[Dict], output_info: List[Dict]) -> int:
        """估算算子的FLOPs"""
        if not input_info or not output_info:
            return 0
            
        # 获取有效的input和output信息
        valid_inputs = [info for info in input_info if info['shape'] is not None]
        valid_outputs = [info for info in output_info if info['shape'] is not None]
        
        if not valid_inputs or not valid_outputs:
            return 0
            
        if op_name in ['linear', 'addmm']:
            # 线性层: 2 * input_features * output_features * batch_size
            if len(valid_inputs) >= 2:
                input_shape = valid_inputs[0]['shape']
                weight_shape = valid_inputs[1]['shape']
                if len(input_shape) >= 2 and len(weight_shape) >= 2:
                    batch_size = np.prod(input_shape[:-1])
                    input_features = input_shape[-1]
                    output_features = weight_shape[0] if weight_shape[1] == input_features else weight_shape[1]
                    return 2 * batch_size * input_features * output_features
                    
        elif op_name in ['matmul', 'bmm']:
            # 矩阵乘法: 2 * M * N * K
            if len(valid_inputs) >= 2:
                shape1 = valid_inputs[0]['shape']
                shape2 = valid_inputs[1]['shape']
                if len(shape1) >= 2 and len(shape2) >= 2:
                    if len(shape1) == 3 and len(shape2) == 3:  # bmm
                        return 2 * shape1[0] * shape1[1] * shape1[2] * shape2[2]
                    else:  # matmul
                        return 2 * shape1[-2] * shape1[-1] * shape2[-1]
                        
        elif op_name == 'int8_scaled_mm':
            # INT8量化矩阵乘法，理论上FLOPs相同但实际计算复杂度不同
            if len(valid_inputs) >= 2:
                shape1 = valid_inputs[0]['shape']
                shape2 = valid_inputs[1]['shape']
                if len(shape1) >= 2 and len(shape2) >= 2:
                    flops = 2 * shape1[-2] * shape1[-1] * shape2[-1]
                    # INT8的计算效率更高，但这里记录理论FLOPs
                    return flops
                    
        # 默认基于输入输出元素数量的简单估算
        total_elements = sum(info['numel'] for info in valid_inputs)
        return total_elements
        
    def analyze_and_save_comprehensive_report(self, filename: str):
        """生成并保存全面的性能分析报告"""
        
        # 按阶段和算子类型统计
        phase_analysis = defaultdict(lambda: {
            'operations': defaultdict(lambda: {'count': 0, 'total_flops': 0, 'avg_time_ms': 0, 'shapes': set()}),
            'total_time_ms': 0,
            'total_flops': 0
        })
        
        substage_analysis = defaultdict(lambda: {
            'operations': defaultdict(lambda: {'count': 0, 'total_flops': 0, 'shapes': set()}),
            'total_flops': 0
        })
        
        for log in self.operation_logs:
            phase = log['phase']
            substage = log['substage']
            op_name = log['operation']
            flops = log['estimated_flops']
            exec_time = log['execution_time_ms'] or 0
            
            # 阶段级统计
            phase_analysis[phase]['operations'][op_name]['count'] += 1
            phase_analysis[phase]['operations'][op_name]['total_flops'] += flops
            phase_analysis[phase]['operations'][op_name]['avg_time_ms'] += exec_time
            phase_analysis[phase]['total_time_ms'] += exec_time
            phase_analysis[phase]['total_flops'] += flops
            
            # 子阶段级统计
            substage_key = f"{phase}_{substage}"
            substage_analysis[substage_key]['operations'][op_name]['count'] += 1
            substage_analysis[substage_key]['operations'][op_name]['total_flops'] += flops
            substage_analysis[substage_key]['total_flops'] += flops
            
            # 记录shape模式
            input_shapes = tuple(tuple(info['shape']) if info['shape'] else None for info in log['input_info'])
            output_shapes = tuple(tuple(info['shape']) if info['shape'] else None for info in log['output_info'])
            shape_pattern = (input_shapes, output_shapes)
            phase_analysis[phase]['operations'][op_name]['shapes'].add(shape_pattern)
            substage_analysis[substage_key]['operations'][op_name]['shapes'].add(shape_pattern)
        
        # 转换set为list以便JSON序列化
        for phase_data in phase_analysis.values():
            for op_data in phase_data['operations'].values():
                op_data['shapes'] = list(op_data['shapes'])
                if op_data['count'] > 0:
                    op_data['avg_time_ms'] /= op_data['count']
                    
        for substage_data in substage_analysis.values():
            for op_data in substage_data['operations'].values():
                op_data['shapes'] = list(op_data['shapes'])
        
        # 性能对比分析
        performance_comparison = self.analyze_prefill_vs_decode()
        
        # 内存使用分析
        memory_analysis = self.analyze_memory_patterns()
        
        # 算子效率分析
        efficiency_analysis = self.analyze_operator_efficiency()
        
        # 生成完整报告
        report = {
            'summary': {
                'total_operations': len(self.operation_logs),
                'execution_time_seconds': time.time() - self.start_time,
                'phases': {phase: len([log for log in self.operation_logs if log['phase'] == phase]) 
                          for phase in set(log['phase'] for log in self.operation_logs)},
                'total_estimated_flops': sum(log['estimated_flops'] for log in self.operation_logs),
                'gpu_count': 2,  # TP=2
                'model_path': os.environ.get("MODEL_PATH", "/home/<USER>/deepseek-int8")
            },
            'phase_analysis': dict(phase_analysis),
            'substage_analysis': dict(substage_analysis),
            'performance_comparison': performance_comparison,
            'memory_analysis': memory_analysis,
            'efficiency_analysis': efficiency_analysis,
            'detailed_logs': self.operation_logs[:100],  # 限制详细日志数量以避免文件过大
            'operator_stats': self.performance_stats
        }
        
        # 保存报告
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False, default=str)
            
        print(f"\n=== 全面性能分析报告已保存: {filename} ===")
        
        # 打印关键统计信息
        self.print_summary_stats(phase_analysis, substage_analysis, performance_comparison)
        
    def analyze_prefill_vs_decode(self) -> Dict:
        """对比分析prefill和decode阶段的性能"""
        prefill_ops = [log for log in self.operation_logs if log['phase'] == 'prefill']
        decode_ops = [log for log in self.operation_logs if log['phase'] == 'decode']
        
        def analyze_phase_ops(ops, phase_name):
            if not ops:
                return {}
                
            total_flops = sum(log['estimated_flops'] for log in ops)
            total_time = sum(log['execution_time_ms'] or 0 for log in ops)
            op_counts = defaultdict(int)
            
            for log in ops:
                op_counts[log['operation']] += 1
                
            return {
                'operation_count': len(ops),
                'total_flops': total_flops,
                'total_time_ms': total_time,
                'avg_flops_per_op': total_flops / len(ops) if ops else 0,
                'throughput_flops_per_ms': total_flops / total_time if total_time > 0 else 0,
                'operation_distribution': dict(op_counts),
                'unique_operations': len(op_counts)
            }
        
        prefill_analysis = analyze_phase_ops(prefill_ops, 'prefill')
        decode_analysis = analyze_phase_ops(decode_ops, 'decode')
        
        return {
            'prefill': prefill_analysis,
            'decode': decode_analysis,
            'comparison': {
                'flops_ratio_prefill_to_decode': (prefill_analysis.get('total_flops', 0) / 
                                                decode_analysis.get('total_flops', 1)) if decode_analysis.get('total_flops', 0) > 0 else 0,
                'time_ratio_prefill_to_decode': (prefill_analysis.get('total_time_ms', 0) / 
                                               decode_analysis.get('total_time_ms', 1)) if decode_analysis.get('total_time_ms', 0) > 0 else 0
            }
        }
        
    def analyze_memory_patterns(self) -> Dict:
        """分析内存使用模式"""
        memory_by_phase = defaultdict(lambda: {'peak_mb': 0, 'total_mb': 0, 'operations': 0})
        memory_by_operator = defaultdict(lambda: {'total_mb': 0, 'count': 0, 'avg_mb': 0})
        
        for log in self.operation_logs:
            phase = log['phase']
            op_name = log['operation']
            
            # 计算这个操作的总内存使用
            total_input_memory = sum(info['memory_mb'] for info in log['input_info'])
            total_output_memory = sum(info['memory_mb'] for info in log['output_info'])
            total_memory = total_input_memory + total_output_memory
            
            # 更新统计
            memory_by_phase[phase]['peak_mb'] = max(memory_by_phase[phase]['peak_mb'], total_memory)
            memory_by_phase[phase]['total_mb'] += total_memory
            memory_by_phase[phase]['operations'] += 1
            
            memory_by_operator[op_name]['total_mb'] += total_memory
            memory_by_operator[op_name]['count'] += 1
            
        # 计算平均值
        for op_data in memory_by_operator.values():
            op_data['avg_mb'] = op_data['total_mb'] / op_data['count']
            
        return {
            'by_phase': dict(memory_by_phase),
            'by_operator': dict(memory_by_operator)
        }
        
    def analyze_operator_efficiency(self) -> Dict:
        """分析算子执行效率"""
        efficiency_by_op = {}
        
        for op_key, stats in self.performance_stats.items():
            if stats['count'] > 0 and stats['total_time_ms'] > 0:
                efficiency_by_op[op_key] = {
                    'avg_flops_per_ms': stats['total_flops'] / stats['total_time_ms'],
                    'avg_elements_per_ms': (stats['total_input_elements'] + stats['total_output_elements']) / stats['total_time_ms'],
                    'avg_time_per_op_ms': stats['total_time_ms'] / stats['count'],
                    'total_operations': stats['count']
                }
                
        return efficiency_by_op
        
    def print_summary_stats(self, phase_analysis, substage_analysis, performance_comparison):
        """打印关键统计信息"""
        print("\n" + "="*80)
        print("算子性能分析总结")
        print("="*80)
        
        # 阶段对比
        print(f"\n【阶段对比】")
        for phase, data in phase_analysis.items():
            print(f"\n{phase.upper()} 阶段:")
            print(f"  总操作数: {sum(op['count'] for op in data['operations'].values())}")
            print(f"  总FLOPs: {data['total_flops']:,}")
            print(f"  总时间: {data['total_time_ms']:.2f}ms")
            print(f"  主要算子:")
            for op_name, op_data in sorted(data['operations'].items(), 
                                         key=lambda x: x[1]['total_flops'], reverse=True)[:5]:
                print(f"    {op_name}: {op_data['count']}次, {op_data['total_flops']:,} FLOPs")
        
        # Prefill vs Decode对比
        prefill_data = performance_comparison.get('prefill', {})
        decode_data = performance_comparison.get('decode', {})
        comparison = performance_comparison.get('comparison', {})
        
        print(f"\n【Prefill vs Decode 对比】")
        print(f"Prefill - 操作数: {prefill_data.get('operation_count', 0)}, "
              f"FLOPs: {prefill_data.get('total_flops', 0):,}, "
              f"时间: {prefill_data.get('total_time_ms', 0):.2f}ms")
        print(f"Decode  - 操作数: {decode_data.get('operation_count', 0)}, "
              f"FLOPs: {decode_data.get('total_flops', 0):,}, "
              f"时间: {decode_data.get('total_time_ms', 0):.2f}ms")
        print(f"FLOPs比例 (Prefill/Decode): {comparison.get('flops_ratio_prefill_to_decode', 0):.2f}")
        print(f"时间比例 (Prefill/Decode): {comparison.get('time_ratio_prefill_to_decode', 0):.2f}")

# 全局分析器
analyzer = ComprehensiveOperatorAnalyzer()

# 保存原始函数
original_functions = {}

def create_timing_wrapper(func_name: str, module, func_path: str):
    """创建带时间测量的函数包装器"""
    original_func = getattr(module, func_path.split('.')[-1])
    original_functions[func_path] = original_func
    
    @wraps(original_func)
    def timed_wrapper(*args, **kwargs):
        # 提取tensor参数
        input_tensors = []
        for arg in args:
            if isinstance(arg, torch.Tensor):
                input_tensors.append(arg)
        for value in kwargs.values():
            if isinstance(value, torch.Tensor):
                input_tensors.append(value)
                
        # 测量执行时间
        start_time = time.perf_counter()
        result = original_func(*args, **kwargs)
        end_time = time.perf_counter()
        execution_time = end_time - start_time
        
        # 提取输出tensor
        output_tensors = []
        if isinstance(result, torch.Tensor):
            output_tensors.append(result)
        elif isinstance(result, (tuple, list)):
            for item in result:
                if isinstance(item, torch.Tensor):
                    output_tensors.append(item)
                    
        # 记录操作
        extra_info = {'function_path': func_path}
        if kwargs:
            extra_info['kwargs'] = {k: str(v) for k, v in kwargs.items() if not isinstance(v, torch.Tensor)}
            
        analyzer.log_operation(func_name, input_tensors, output_tensors, extra_info, execution_time)
        
        return result
        
    setattr(module, func_path.split('.')[-1], timed_wrapper)

# 监控更多的PyTorch函数
functions_to_monitor = [
    ('linear', torch.nn.functional, 'torch.nn.functional.linear'),
    ('matmul', torch, 'torch.matmul'),
    ('bmm', torch, 'torch.bmm'),
    ('addmm', torch, 'torch.addmm'),
    ('mm', torch, 'torch.mm'),
    ('conv2d', torch.nn.functional, 'torch.nn.functional.conv2d'),
    ('conv1d', torch.nn.functional, 'torch.nn.functional.conv1d'),
    ('embedding', torch.nn.functional, 'torch.nn.functional.embedding'),
    ('layer_norm', torch.nn.functional, 'torch.nn.functional.layer_norm'),
    ('group_norm', torch.nn.functional, 'torch.nn.functional.group_norm'),
    ('softmax', torch.nn.functional, 'torch.nn.functional.softmax'),
    ('gelu', torch.nn.functional, 'torch.nn.functional.gelu'),
    ('relu', torch.nn.functional, 'torch.nn.functional.relu'),
    ('silu', torch.nn.functional, 'torch.nn.functional.silu'),
]

for func_name, module, func_path in functions_to_monitor:
    try:
        create_timing_wrapper(func_name, module, func_path)
    except AttributeError:
        print(f"警告: 无法监控 {func_path}")

# 尝试监控量化相关函数
try:
    import sgl_kernel
    if hasattr(sgl_kernel, 'int8_scaled_mm'):
        create_timing_wrapper('int8_scaled_mm', sgl_kernel, 'sgl_kernel.int8_scaled_mm')
except ImportError:
    print("sgl_kernel 不可用，跳过量化内核监控")

# 监控Triton kernels（如果可用）
try:
    import triton
    print("Triton 可用，但kernel监控需要更复杂的hook")
except ImportError:
    print("Triton 不可用")

import sglang as sgl
from sglang import function, gen, set_default_backend, LLM, SamplingParams

MODEL_PATH = os.environ.get("MODEL_PATH", "/home/<USER>/deepseek-int8")

def run_comprehensive_test():
    """运行真实的SGLang离线推理测试"""
    llm = None
    try:
        print("[INFO] 开始初始化SGLang离线推理引擎 (TP=2, GPU 0,1)...")
        analyzer.set_phase("engine_initialization")
        
        # 使用SGLang的离线推理LLM接口
        llm = LLM(
            model_path=MODEL_PATH,
            tokenizer_path=MODEL_PATH,
            tp_size=2,  # 使用GPU 0,1进行张量并行
            trust_remote_code=True,
            disable_cuda_graph=True,
            log_level="info",
        )
        
        print("[INFO] 引擎初始化完成，开始真实离线推理测试...")
        
        # 测试多种场景来触发不同的prefill和decode模式
        test_prompts = [
            "深度学习是什么？",
            "请详细解释什么是Transformer架构，包括注意力机制的工作原理和多头注意力的优势。",
            "人工智能技术的发展历程可以追溯到20世纪50年代，从早期的专家系统到现在的深度学习和大语言模型，每一个阶段都有重要的技术突破。深度学习特别是Transformer架构的出现，为自然语言处理带来了革命性的变化。请分析这个发展趋势。"
        ]
        
        # 不同的采样参数配置
        sampling_configs = [
            {"max_new_tokens": 32, "temperature": 0.1, "top_p": 0.9},
            {"max_new_tokens": 16, "temperature": 0.0, "top_p": 1.0},
            {"max_new_tokens": 8, "temperature": 0.2, "top_p": 0.8}
        ]
        
        for i, (prompt, config) in enumerate(zip(test_prompts, sampling_configs)):
            print(f"\n{'='*60}")
            print(f"真实推理测试 {i+1}")
            print(f"输入长度: {len(prompt)} 字符")
            print(f"采样配置: {config}")
            print(f"{'='*60}")
            
            analyzer.set_phase("prefill", f"real_inference_{i+1}")
            
            # 创建采样参数
            sampling_params = SamplingParams(
                max_new_tokens=config["max_new_tokens"],
                temperature=config["temperature"],
                top_p=config["top_p"]
            )
            
            print(f"输入: {prompt[:100]}{'...' if len(prompt) > 100 else ''}")
            
            # 执行真实的离线推理
            start_time = time.time()
            
            # 使用generate方法进行推理
            outputs = llm.generate([prompt], sampling_params)
            
            end_time = time.time()
            
            # 输出结果
            for output in outputs:
                generated_text = output.outputs[0].text
                print(f"输出: {generated_text}")
                print(f"总耗时: {(end_time - start_time)*1000:.2f}ms")
                print(f"输出token数: {len(output.outputs[0].token_ids) if output.outputs[0].token_ids else 'N/A'}")
            
            # 重置prefill标志以便下个测试
            analyzer.prefill_complete = False
            time.sleep(1.0)  # 等待一秒确保统计完整
            
        analyzer.set_phase("completed")
        
        # 生成真实推理的全面报告
        report_filename = f"real_inference_operator_analysis_{int(time.time())}.json"
        analyzer.analyze_and_save_comprehensive_report(report_filename)
        
        return 0
        
    except Exception as e:
        print(f"[ERROR] 真实推理执行出错: {e}")
        print(traceback.format_exc())
        return 1
    finally:
        # 恢复原始函数
        for func_path, original_func in original_functions.items():
            try:
                module_parts = func_path.split('.')
                if len(module_parts) >= 2:
                    if module_parts[0] == 'torch':
                        if module_parts[1] == 'nn':
                            setattr(torch.nn.functional, module_parts[-1], original_func)
                        else:
                            setattr(torch, module_parts[-1], original_func)
                    elif module_parts[0] == 'sgl_kernel':
                        setattr(sgl_kernel, module_parts[-1], original_func)
            except Exception as restore_error:
                print(f"恢复函数 {func_path} 时出错: {restore_error}")
                
        if llm is not None:
            try:
                # 正确的shutdown方法
                del llm
                torch.cuda.empty_cache()
                gc.collect()
            except Exception as cleanup_error:
                print(f"清理资源时出错: {cleanup_error}")

if __name__ == "__main__":
    sys.exit(run_comprehensive_test())
