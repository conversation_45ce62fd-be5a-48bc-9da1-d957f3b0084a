{"execution_summary": {"total_operations": 29, "execution_time_seconds": 6.661328077316284, "gpu_count": 8, "prefill_ops": 22, "decode_ops": 7}, "module_statistics": {"operator_counts": {"embedding": {"total": 0, "prefill": 0, "decode": 0}, "attn": {"query": {"total": 9, "prefill": 7, "decode": 2}, "key": {"total": 0, "prefill": 0, "decode": 0}, "value": {"total": 0, "prefill": 0, "decode": 0}, "output": {"total": 2, "prefill": 1, "decode": 1}, "total": {"total": 17, "prefill": 12, "decode": 5}}, "ffn": {"gate": {"total": 0, "prefill": 0, "decode": 0}, "up": {"total": 0, "prefill": 0, "decode": 0}, "down": {"total": 0, "prefill": 0, "decode": 0}, "total": {"total": 0, "prefill": 0, "decode": 0}}, "expert": {"gate": {"total": 4, "prefill": 4, "decode": 0}, "up": {"total": 0, "prefill": 0, "decode": 0}, "down": {"total": 2, "prefill": 2, "decode": 0}, "router": {"total": 2, "prefill": 2, "decode": 0}, "total": {"total": 8, "prefill": 8, "decode": 0}}, "output": {"total": 0, "prefill": 0, "decode": 0}, "other": {"total": 0, "prefill": 0, "decode": 0}}, "flops_distribution": {"embedding": {"total": 0.0, "prefill": 0.0, "decode": 0.0}, "attn": {"query": {"total": 120326193152.0, "prefill": 120259084288.0, "decode": 67108864.0}, "key": {"total": 0.0, "prefill": 0.0, "decode": 0.0}, "value": {"total": 0.0, "prefill": 0.0, "decode": 0.0}, "output": {"total": 17213423616.0, "prefill": 17179869184.0, "decode": 33554432.0}, "matmul": {"total": 268435968.0, "prefill": 268435456.0, "decode": 512.0}, "total": {"total": 137808052736.0, "prefill": 137707388928.0, "decode": 100663808.0}}, "ffn": {"gate": {"total": 0.0, "prefill": 0.0, "decode": 0.0}, "up": {"total": 0.0, "prefill": 0.0, "decode": 0.0}, "down": {"total": 0.0, "prefill": 0.0, "decode": 0.0}, "total": {"total": 0.0, "prefill": 0.0, "decode": 0.0}}, "expert": {"gate": {"total": 68719476736.0, "prefill": 68719476736.0, "decode": 0.0}, "up": {"total": 0.0, "prefill": 0.0, "decode": 0.0}, "down": {"total": 420906795008.0, "prefill": 420906795008.0, "decode": 0.0}, "router": {"total": 34359738368.0, "prefill": 34359738368.0, "decode": 0.0}, "total": {"total": 523986010112.0, "prefill": 523986010112.0, "decode": 0.0}}, "output": {"total": 0.0, "prefill": 0.0, "decode": 0.0}, "other": {"total": 0.0, "prefill": 0.0, "decode": 0.0}}}, "phase_analysis": {"prefill": {"module_stats": {"embedding": {"count": 1, "flops": 51200.0, "time": 0.022008657455444336}, "attn": {"count": 12, "flops": 137707388928.0, "time": 2.916771173477173}, "expert": {"count": 8, "flops": 523986010112.0, "time": 0.009983062744140625}, "output": {"count": 1, "flops": 17179869184.0, "time": 0.0002448558807373047}}, "operation_stats": {"embedding.lookup": {"count": 1, "flops": 51200.0, "time": 0.022008657455444336}, "attn.output": {"count": 1, "flops": 17179869184.0, "time": 2.910172939300537}, "attn.query": {"count": 7, "flops": 120259084288.0, "time": 0.0017695426940917969}, "attn.matmul": {"count": 4, "flops": 268435456.0, "time": 0.004828691482543945}, "expert.router": {"count": 2, "flops": 34359738368.0, "time": 0.008511543273925781}, "expert.gate": {"count": 4, "flops": 68719476736.0, "time": 0.0012798309326171875}, "expert.down": {"count": 2, "flops": 420906795008.0, "time": 0.00019168853759765625}, "output.projection": {"count": 1, "flops": 17179869184.0, "time": 0.0002448558807373047}}, "total_flops": 678873319424.0, "total_time": 2.949007749557495, "operation_count": 22}, "decode": {"module_stats": {"embedding": {"count": 1, "flops": 100.0, "time": 0.038950204849243164}, "attn": {"count": 5, "flops": 100663808.0, "time": 0.01458740234375}, "output": {"count": 1, "flops": 33554432.0, "time": 9.489059448242188e-05}}, "operation_stats": {"embedding.lookup": {"count": 1, "flops": 100.0, "time": 0.038950204849243164}, "attn.output": {"count": 1, "flops": 33554432.0, "time": 0.0001876354217529297}, "attn.query": {"count": 2, "flops": 67108864.0, "time": 0.0001266002655029297}, "attn.matmul": {"count": 2, "flops": 512.0, "time": 0.01427316665649414}, "output.projection": {"count": 1, "flops": 33554432.0, "time": 9.489059448242188e-05}}, "total_flops": 134218340.0, "total_time": 0.053632497787475586, "operation_count": 7}}, "module_breakdown": {"prefill": {"embedding.lookup": {"count": 1, "total_flops": 51200.0, "avg_flops": 51200.0, "total_time": 0.022008657455444336, "avg_time": 0.022008657455444336, "typical_shapes": {"input": [[1, 512], [128256, 4096]], "output": [[1, 512, 4096]]}, "layers": [0]}, "attn.output": {"count": 1, "total_flops": 17179869184.0, "avg_flops": 17179869184.0, "total_time": 2.910172939300537, "avg_time": 2.910172939300537, "typical_shapes": {"input": [[1, 512, 4096], [4096, 4096]], "output": [[1, 512, 4096]]}, "layers": [0]}, "attn.query": {"count": 7, "total_flops": 120259084288.0, "avg_flops": 17179869184.0, "total_time": 0.0017695426940917969, "avg_time": 0.00025279181344168525, "typical_shapes": {"input": [[1, 512, 4096], [4096, 4096]], "output": [[1, 512, 4096]]}, "layers": [0, 1]}, "attn.matmul": {"count": 4, "total_flops": 268435456.0, "avg_flops": 67108864.0, "total_time": 0.004828691482543945, "avg_time": 0.0012071728706359863, "typical_shapes": {"input": [[1, 32, 512, 128], [1, 32, 128, 512]], "output": [[1, 32, 512, 512]]}, "layers": [0, 1]}, "expert.router": {"count": 2, "total_flops": 34359738368.0, "avg_flops": 17179869184.0, "total_time": 0.008511543273925781, "avg_time": 0.004255771636962891, "typical_shapes": {"input": [[1, 512, 4096], [8, 4096]], "output": [[1, 512, 8]]}, "layers": [0, 1]}, "expert.gate": {"count": 4, "total_flops": 68719476736.0, "avg_flops": 17179869184.0, "total_time": 0.0012798309326171875, "avg_time": 0.0003199577331542969, "typical_shapes": {"input": [[1, 512, 4096], [14336, 4096]], "output": [[1, 512, 14336]]}, "layers": [0, 1]}, "expert.down": {"count": 2, "total_flops": 420906795008.0, "avg_flops": 210453397504.0, "total_time": 0.00019168853759765625, "avg_time": 9.584426879882812e-05, "typical_shapes": {"input": [[1, 512, 14336], [4096, 14336]], "output": [[1, 512, 4096]]}, "layers": [1, 2]}, "output.projection": {"count": 1, "total_flops": 17179869184.0, "avg_flops": 17179869184.0, "total_time": 0.0002448558807373047, "avg_time": 0.0002448558807373047, "typical_shapes": {"input": [[1, 512, 4096], [128256, 4096]], "output": [[1, 512, 128256]]}, "layers": [2]}}, "decode": {"embedding.lookup": {"count": 1, "total_flops": 100.0, "avg_flops": 100.0, "total_time": 0.038950204849243164, "avg_time": 0.038950204849243164, "typical_shapes": {"input": [[1, 1], [128256, 4096]], "output": [[1, 1, 4096]]}, "layers": [2]}, "attn.output": {"count": 1, "total_flops": 33554432.0, "avg_flops": 33554432.0, "total_time": 0.0001876354217529297, "avg_time": 0.0001876354217529297, "typical_shapes": {"input": [[1, 1, 4096], [4096, 4096]], "output": [[1, 1, 4096]]}, "layers": [2]}, "attn.query": {"count": 2, "total_flops": 67108864.0, "avg_flops": 33554432.0, "total_time": 0.0001266002655029297, "avg_time": 6.330013275146484e-05, "typical_shapes": {"input": [[1, 1, 4096], [4096, 4096]], "output": [[1, 1, 4096]]}, "layers": [2]}, "attn.matmul": {"count": 2, "total_flops": 512.0, "avg_flops": 256.0, "total_time": 0.01427316665649414, "avg_time": 0.00713658332824707, "typical_shapes": {"input": [[1, 32, 1, 128], [1, 32, 128, 1]], "output": [[1, 32, 1, 1]]}, "layers": [2]}, "output.projection": {"count": 1, "total_flops": 33554432.0, "avg_flops": 33554432.0, "total_time": 9.489059448242188e-05, "avg_time": 9.489059448242188e-05, "typical_shapes": {"input": [[1, 1, 4096], [128256, 4096]], "output": [[1, 1, 128256]]}, "layers": [2]}}}, "detailed_timeline": [{"name": "torch.nn.functional.embedding", "input_shapes": [[1, 512], [128256, 4096]], "output_shapes": [[1, 512, 4096]], "dtypes": ["torch.int64", "torch.float16"], "device": "cuda:0", "timestamp": 3.5722522735595703, "phase": "prefill", "module_type": "embedding", "operation_type": "lookup", "layer_id": 0, "expert_id": null, "memory_before": 0.9824256896972656, "memory_after": 0.9824256896972656, "flops": 51200.0, "execution_time": 0.022008657455444336}, {"name": "torch.nn.functional.linear", "input_shapes": [[1, 512, 4096], [4096, 4096]], "output_shapes": [[1, 512, 4096]], "dtypes": ["torch.float16", "torch.float16"], "device": "cuda:0", "timestamp": 6.4869043827056885, "phase": "prefill", "module_type": "attn", "operation_type": "output", "layer_id": 0, "expert_id": null, "memory_before": 1.0880165100097656, "memory_after": 1.0880165100097656, "flops": 17179869184.0, "execution_time": 2.910172939300537}, {"name": "torch.nn.functional.linear", "input_shapes": [[1, 512, 4096], [4096, 4096]], "output_shapes": [[1, 512, 4096]], "dtypes": ["torch.float16", "torch.float16"], "device": "cuda:0", "timestamp": 6.488569736480713, "phase": "prefill", "module_type": "attn", "operation_type": "query", "layer_id": 0, "expert_id": null, "memory_before": 1.0919227600097656, "memory_after": 1.0919227600097656, "flops": 17179869184.0, "execution_time": 0.0012810230255126953}, {"name": "torch.nn.functional.linear", "input_shapes": [[1, 512, 4096], [4096, 4096]], "output_shapes": [[1, 512, 4096]], "dtypes": ["torch.float16", "torch.float16"], "device": "cuda:0", "timestamp": 6.4893341064453125, "phase": "prefill", "module_type": "attn", "operation_type": "query", "layer_id": 0, "expert_id": null, "memory_before": 1.0958290100097656, "memory_after": 1.0958290100097656, "flops": 17179869184.0, "execution_time": 6.67572021484375e-05}, {"name": "torch.matmul", "input_shapes": [[1, 32, 512, 128], [1, 32, 128, 512]], "output_shapes": [[1, 32, 512, 512]], "dtypes": ["torch.float16", "torch.float16"], "device": "cuda:0", "timestamp": 6.494236946105957, "phase": "prefill", "module_type": "attn", "operation_type": "<PERSON><PERSON>l", "layer_id": 0, "expert_id": null, "memory_before": 1.1114540100097656, "memory_after": 1.1114540100097656, "flops": 67108864.0, "execution_time": 0.004587888717651367}, {"name": "torch.matmul", "input_shapes": [[1, 32, 512, 512], [1, 32, 512, 128]], "output_shapes": [[1, 32, 512, 128]], "dtypes": ["torch.float16", "torch.float16"], "device": "cuda:0", "timestamp": 6.513691663742065, "phase": "prefill", "module_type": "attn", "operation_type": "<PERSON><PERSON>l", "layer_id": 0, "expert_id": null, "memory_before": 1.1309852600097656, "memory_after": 1.1309852600097656, "flops": 67108864.0, "execution_time": 0.00012969970703125}, {"name": "torch.nn.functional.linear", "input_shapes": [[1, 512, 4096], [4096, 4096]], "output_shapes": [[1, 512, 4096]], "dtypes": ["torch.float16", "torch.float16"], "device": "cuda:0", "timestamp": 6.5239598751068115, "phase": "prefill", "module_type": "attn", "operation_type": "query", "layer_id": 0, "expert_id": null, "memory_before": 1.1661415100097656, "memory_after": 1.1661415100097656, "flops": 17179869184.0, "execution_time": 0.00010848045349121094}, {"name": "torch.nn.functional.linear", "input_shapes": [[1, 512, 4096], [8, 4096]], "output_shapes": [[1, 512, 8]], "dtypes": ["torch.float16", "torch.float16"], "device": "cuda:0", "timestamp": 6.53281569480896, "phase": "prefill", "module_type": "expert", "operation_type": "router", "layer_id": 0, "expert_id": null, "memory_before": 1.1623039245605469, "memory_after": 1.1623039245605469, "flops": 17179869184.0, "execution_time": 0.008461952209472656}, {"name": "torch.nn.functional.linear", "input_shapes": [[1, 512, 4096], [14336, 4096]], "output_shapes": [[1, 512, 14336]], "dtypes": ["torch.float16", "torch.float16"], "device": "cuda:0", "timestamp": 6.5635600090026855, "phase": "prefill", "module_type": "expert", "operation_type": "gate", "layer_id": 0, "expert_id": null, "memory_before": 1.5041179656982422, "memory_after": 1.5041179656982422, "flops": 17179869184.0, "execution_time": 0.0008337497711181641}, {"name": "torch.nn.functional.linear", "input_shapes": [[1, 512, 4096], [14336, 4096]], "output_shapes": [[1, 512, 14336]], "dtypes": ["torch.float16", "torch.float16"], "device": "cuda:0", "timestamp": 6.564244747161865, "phase": "prefill", "module_type": "expert", "operation_type": "gate", "layer_id": 0, "expert_id": null, "memory_before": 1.5177898406982422, "memory_after": 1.5177898406982422, "flops": 17179869184.0, "execution_time": 0.0003399848937988281}, {"name": "torch.nn.functional.linear", "input_shapes": [[1, 512, 14336], [4096, 14336]], "output_shapes": [[1, 512, 4096]], "dtypes": ["torch.float16", "torch.float16"], "device": "cuda:0", "timestamp": 6.582253456115723, "phase": "prefill", "module_type": "expert", "operation_type": "down", "layer_id": 1, "expert_id": null, "memory_before": 1.5353679656982422, "memory_after": 1.5353679656982422, "flops": 210453397504.0, "execution_time": 0.000148773193359375}, {"name": "torch.nn.functional.linear", "input_shapes": [[1, 512, 4096], [4096, 4096]], "output_shapes": [[1, 512, 4096]], "dtypes": ["torch.float16", "torch.float16"], "device": "cuda:0", "timestamp": 6.5988147258758545, "phase": "prefill", "module_type": "attn", "operation_type": "query", "layer_id": 1, "expert_id": null, "memory_before": 1.5392742156982422, "memory_after": 1.5392742156982422, "flops": 17179869184.0, "execution_time": 0.0001068115234375}, {"name": "torch.nn.functional.linear", "input_shapes": [[1, 512, 4096], [4096, 4096]], "output_shapes": [[1, 512, 4096]], "dtypes": ["torch.float16", "torch.float16"], "device": "cuda:0", "timestamp": 6.599202871322632, "phase": "prefill", "module_type": "attn", "operation_type": "query", "layer_id": 1, "expert_id": null, "memory_before": 1.5392742156982422, "memory_after": 1.5392742156982422, "flops": 17179869184.0, "execution_time": 9.870529174804688e-05}, {"name": "torch.nn.functional.linear", "input_shapes": [[1, 512, 4096], [4096, 4096]], "output_shapes": [[1, 512, 4096]], "dtypes": ["torch.float16", "torch.float16"], "device": "cuda:0", "timestamp": 6.599468231201172, "phase": "prefill", "module_type": "attn", "operation_type": "query", "layer_id": 1, "expert_id": null, "memory_before": 1.5392742156982422, "memory_after": 1.5392742156982422, "flops": 17179869184.0, "execution_time": 5.7220458984375e-05}, {"name": "torch.matmul", "input_shapes": [[1, 32, 512, 128], [1, 32, 128, 512]], "output_shapes": [[1, 32, 512, 512]], "dtypes": ["torch.float16", "torch.float16"], "device": "cuda:0", "timestamp": 6.5997490882873535, "phase": "prefill", "module_type": "attn", "operation_type": "<PERSON><PERSON>l", "layer_id": 1, "expert_id": null, "memory_before": 1.5509929656982422, "memory_after": 1.5509929656982422, "flops": 67108864.0, "execution_time": 6.556510925292969e-05}, {"name": "torch.matmul", "input_shapes": [[1, 32, 512, 512], [1, 32, 512, 128]], "output_shapes": [[1, 32, 512, 128]], "dtypes": ["torch.float16", "torch.float16"], "device": "cuda:0", "timestamp": 6.600050687789917, "phase": "prefill", "module_type": "attn", "operation_type": "<PERSON><PERSON>l", "layer_id": 1, "expert_id": null, "memory_before": 1.5392742156982422, "memory_after": 1.5392742156982422, "flops": 67108864.0, "execution_time": 4.553794860839844e-05}, {"name": "torch.nn.functional.linear", "input_shapes": [[1, 512, 4096], [4096, 4096]], "output_shapes": [[1, 512, 4096]], "dtypes": ["torch.float16", "torch.float16"], "device": "cuda:0", "timestamp": 6.600602626800537, "phase": "prefill", "module_type": "attn", "operation_type": "query", "layer_id": 1, "expert_id": null, "memory_before": 1.5392742156982422, "memory_after": 1.5392742156982422, "flops": 17179869184.0, "execution_time": 5.054473876953125e-05}, {"name": "torch.nn.functional.linear", "input_shapes": [[1, 512, 4096], [8, 4096]], "output_shapes": [[1, 512, 8]], "dtypes": ["torch.float16", "torch.float16"], "device": "cuda:0", "timestamp": 6.600900173187256, "phase": "prefill", "module_type": "expert", "operation_type": "router", "layer_id": 1, "expert_id": null, "memory_before": 1.5353755950927734, "memory_after": 1.5353755950927734, "flops": 17179869184.0, "execution_time": 4.9591064453125e-05}, {"name": "torch.nn.functional.linear", "input_shapes": [[1, 512, 4096], [14336, 4096]], "output_shapes": [[1, 512, 14336]], "dtypes": ["torch.float16", "torch.float16"], "device": "cuda:0", "timestamp": 6.60144829750061, "phase": "prefill", "module_type": "expert", "operation_type": "gate", "layer_id": 1, "expert_id": null, "memory_before": 1.5490398406982422, "memory_after": 1.5490398406982422, "flops": 17179869184.0, "execution_time": 5.245208740234375e-05}, {"name": "torch.nn.functional.linear", "input_shapes": [[1, 512, 4096], [14336, 4096]], "output_shapes": [[1, 512, 14336]], "dtypes": ["torch.float16", "torch.float16"], "device": "cuda:0", "timestamp": 6.601701498031616, "phase": "prefill", "module_type": "expert", "operation_type": "gate", "layer_id": 1, "expert_id": null, "memory_before": 1.5490398406982422, "memory_after": 1.5490398406982422, "flops": 17179869184.0, "execution_time": 5.364418029785156e-05}, {"name": "torch.nn.functional.linear", "input_shapes": [[1, 512, 14336], [4096, 14336]], "output_shapes": [[1, 512, 4096]], "dtypes": ["torch.float16", "torch.float16"], "device": "cuda:0", "timestamp": 6.601994752883911, "phase": "prefill", "module_type": "expert", "operation_type": "down", "layer_id": 2, "expert_id": null, "memory_before": 1.5392742156982422, "memory_after": 1.5392742156982422, "flops": 210453397504.0, "execution_time": 4.291534423828125e-05}, {"name": "torch.nn.functional.linear", "input_shapes": [[1, 512, 4096], [128256, 4096]], "output_shapes": [[1, 512, 128256]], "dtypes": ["torch.float16", "torch.float16"], "device": "cuda:0", "timestamp": 6.602874994277954, "phase": "prefill", "module_type": "output", "operation_type": "projection", "layer_id": 2, "expert_id": null, "memory_before": 2.605680465698242, "memory_after": 2.605680465698242, "flops": 17179869184.0, "execution_time": 0.0002448558807373047}, {"name": "torch.nn.functional.embedding", "input_shapes": [[1, 1], [128256, 4096]], "output_shapes": [[1, 1, 4096]], "dtypes": ["torch.int64", "torch.float16"], "device": "cuda:0", "timestamp": 6.642292499542236, "phase": "decode", "module_type": "embedding", "operation_type": "lookup", "layer_id": 2, "expert_id": null, "memory_before": 2.6056885719299316, "memory_after": 2.6056885719299316, "flops": 100.0, "execution_time": 0.038950204849243164}, {"name": "torch.nn.functional.linear", "input_shapes": [[1, 1, 4096], [4096, 4096]], "output_shapes": [[1, 1, 4096]], "dtypes": ["torch.float16", "torch.float16"], "device": "cuda:0", "timestamp": 6.642912149429321, "phase": "decode", "module_type": "attn", "operation_type": "output", "layer_id": 2, "expert_id": null, "memory_before": 2.605696201324463, "memory_after": 2.605696201324463, "flops": 33554432.0, "execution_time": 0.0001876354217529297}, {"name": "torch.nn.functional.linear", "input_shapes": [[1, 1, 4096], [4096, 4096]], "output_shapes": [[1, 1, 4096]], "dtypes": ["torch.float16", "torch.float16"], "device": "cuda:0", "timestamp": 6.643422842025757, "phase": "decode", "module_type": "attn", "operation_type": "query", "layer_id": 2, "expert_id": null, "memory_before": 2.605703830718994, "memory_after": 2.605703830718994, "flops": 33554432.0, "execution_time": 6.67572021484375e-05}, {"name": "torch.nn.functional.linear", "input_shapes": [[1, 1, 4096], [4096, 4096]], "output_shapes": [[1, 1, 4096]], "dtypes": ["torch.float16", "torch.float16"], "device": "cuda:0", "timestamp": 6.643730163574219, "phase": "decode", "module_type": "attn", "operation_type": "query", "layer_id": 2, "expert_id": null, "memory_before": 2.6057114601135254, "memory_after": 2.6057114601135254, "flops": 33554432.0, "execution_time": 5.984306335449219e-05}, {"name": "torch.matmul", "input_shapes": [[1, 32, 1, 128], [1, 32, 128, 1]], "output_shapes": [[1, 32, 1, 1]], "dtypes": ["torch.float16", "torch.float16"], "device": "cuda:0", "timestamp": 6.656292676925659, "phase": "decode", "module_type": "attn", "operation_type": "<PERSON><PERSON>l", "layer_id": 2, "expert_id": null, "memory_before": 2.6057119369506836, "memory_after": 2.6057119369506836, "flops": 256.0, "execution_time": 0.012269973754882812}, {"name": "torch.matmul", "input_shapes": [[1, 32, 1, 1], [1, 32, 1, 128]], "output_shapes": [[1, 32, 1, 128]], "dtypes": ["torch.float16", "torch.float16"], "device": "cuda:0", "timestamp": 6.658584117889404, "phase": "decode", "module_type": "attn", "operation_type": "<PERSON><PERSON>l", "layer_id": 2, "expert_id": null, "memory_before": 2.590094566345215, "memory_after": 2.590094566345215, "flops": 256.0, "execution_time": 0.002003192901611328}, {"name": "torch.nn.functional.linear", "input_shapes": [[1, 1, 4096], [128256, 4096]], "output_shapes": [[1, 1, 128256]], "dtypes": ["torch.float16", "torch.float16"], "device": "cuda:0", "timestamp": 6.6589531898498535, "phase": "decode", "module_type": "output", "operation_type": "projection", "layer_id": 2, "expert_id": null, "memory_before": 2.5864272117614746, "memory_after": 2.5864272117614746, "flops": 33554432.0, "execution_time": 9.489059448242188e-05}], "performance_metrics": {"total_flops": 679007537764.0, "total_execution_time": 3.0026402473449707, "average_flops_per_op": 23414053026.344826, "flops_per_second": 226136826869.08627, "prefill_decode_flops_ratio": 5057.977318330714, "prefill_percentage": 99.98023315905417, "decode_percentage": 0.01976684094582905}}