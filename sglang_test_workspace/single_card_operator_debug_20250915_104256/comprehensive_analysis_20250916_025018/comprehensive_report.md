# SGLang DeepSeek V3 INT8 全面算子分析报告

## 🎯 执行概述
- 总算子调用次数: 29
- Prefill阶段算子: 22
- Decode阶段算子: 7
- 总执行时间: 6.66秒

## 📊 模块级别统计

### 算子计数分布

#### EMBEDDING模块
- 总调用次数: 0
- Prefill阶段: 0
- Decode阶段: 0

#### ATTN模块
- 总调用次数: 17
- Prefill阶段: 12
- Decode阶段: 5
  - query: 9 (Prefill: 7, Decode: 2)
  - key: 0 (Prefill: 0, Decode: 0)
  - value: 0 (Prefill: 0, Decode: 0)
  - output: 2 (Prefill: 1, Decode: 1)

#### FFN模块
- 总调用次数: 0
- Prefill阶段: 0
- Decode阶段: 0
  - gate: 0 (Prefill: 0, Decode: 0)
  - up: 0 (Prefill: 0, Decode: 0)
  - down: 0 (Prefill: 0, Decode: 0)

#### EXPERT模块
- 总调用次数: 8
- Prefill阶段: 8
- Decode阶段: 0
  - gate: 4 (Prefill: 4, Decode: 0)
  - up: 0 (Prefill: 0, Decode: 0)
  - down: 2 (Prefill: 2, Decode: 0)
  - router: 2 (Prefill: 2, Decode: 0)

#### OUTPUT模块
- 总调用次数: 0
- Prefill阶段: 0
- Decode阶段: 0

#### OTHER模块
- 总调用次数: 0
- Prefill阶段: 0
- Decode阶段: 0


## 🔍 阶段详细分析

### Prefill阶段
- 算子数量: 22
- 总FLOPs: 6.79e+11
- 总执行时间: 2.9490秒

#### 模块分解:
- **embedding**: 1次调用, 5.12e+04 FLOPs
- **attn**: 12次调用, 1.38e+11 FLOPs
- **expert**: 8次调用, 5.24e+11 FLOPs
- **output**: 1次调用, 1.72e+10 FLOPs


### Decode阶段
- 算子数量: 7
- 总FLOPs: 1.34e+08
- 总执行时间: 0.0536秒

#### 模块分解:
- **embedding**: 1次调用, 1.00e+02 FLOPs
- **attn**: 5次调用, 1.01e+08 FLOPs
- **output**: 1次调用, 3.36e+07 FLOPs


## ⚡ 性能指标
- 总计算量: 6.79e+11 FLOPs
- 平均每算子FLOPs: 2.34e+10
- 计算效率: 2.26e+11 FLOP/s
- Prefill/Decode计算比: 5058.0:1
- Prefill占比: 100.0%
- Decode占比: 0.0%


## 🔬 技术洞察
1. **模块计算分布**: 展示了不同模块的计算负载
2. **阶段特征对比**: Prefill与Decode的显著差异
3. **操作类型分析**: Gate/Up/Down等具体操作的性能特征
4. **层级分布**: 不同层的计算模式
