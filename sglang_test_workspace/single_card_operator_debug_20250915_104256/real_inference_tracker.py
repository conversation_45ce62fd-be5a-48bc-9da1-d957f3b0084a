#!/usr/bin/env python3
"""
真实SGLang离线推理算子追踪脚本
从实际的推理过程中获取真实的模型运算信息
"""

import os
import sys
import time
import json
import threading
import traceback
from collections import defaultdict, Counter
from typing import Dict, List, Any, Optional
from functools import wraps

import torch
import torch.nn.functional as F
import numpy as np

# 设置环境变量
os.environ["CUDA_VISIBLE_DEVICES"] = "0,1"  # 使用GPU 0,1
os.environ["SGLANG_IS_FLASHINFER_AVAILABLE"] = "false"

class RealInferenceTracker:
    """真实推理过程的算子追踪器"""
    
    def __init__(self):
        self.operations = []
        self.start_time = time.time()
        self.lock = threading.Lock()
        self.phase = "initialization"
        self.output_dir = f"real_inference_analysis_{int(time.time())}"
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 阶段计数器
        self.phase_counters = {
            'embedding': {'prefill': 0, 'decode': 0, 'unknown': 0},
            'linear': {'prefill': 0, 'decode': 0, 'unknown': 0},
            'matmul': {'prefill': 0, 'decode': 0, 'unknown': 0},
            'bmm': {'prefill': 0, 'decode': 0, 'unknown': 0},
            'int8_ops': {'prefill': 0, 'decode': 0, 'unknown': 0},
            'other': {'prefill': 0, 'decode': 0, 'unknown': 0}
        }
        
        print(f"真实推理追踪器初始化，输出目录: {self.output_dir}")
    
    def detect_phase(self, input_shapes: List[tuple]) -> str:
        """根据输入形状检测推理阶段"""
        for shape in input_shapes:
            if len(shape) >= 2:
                seq_len = shape[-2] if len(shape) > 2 else shape[-1]
                if isinstance(seq_len, int):
                    if seq_len > 50:  # 长序列，很可能是prefill
                        return "prefill"
                    elif seq_len == 1:  # 单token，很可能是decode
                        return "decode"
        return "unknown"
    
    def classify_operation(self, op_name: str, input_shapes: List[tuple]) -> tuple:
        """分类算子操作"""
        op_lower = op_name.lower()
        
        # 基本分类
        if 'embedding' in op_lower:
            op_type = 'embedding'
            op_subtype = 'lookup'
        elif 'linear' in op_lower:
            op_type = 'linear'
            op_subtype = self._classify_linear_subtype(input_shapes)
        elif 'matmul' in op_lower:
            op_type = 'matmul'
            op_subtype = self._classify_matmul_subtype(input_shapes)
        elif 'bmm' in op_lower:
            op_type = 'bmm'
            op_subtype = 'batch_matmul'
        elif 'int8' in op_lower or 'quantized' in op_lower:
            op_type = 'int8_ops'
            op_subtype = 'quantized_compute'
        else:
            op_type = 'other'
            op_subtype = 'unknown'
        
        return op_type, op_subtype
    
    def _classify_linear_subtype(self, input_shapes: List[tuple]) -> str:
        """分类Linear算子的子类型"""
        if not input_shapes or len(input_shapes) < 2:
            return 'unknown'
        
        input_shape = input_shapes[0]
        weight_shape = input_shapes[1]
        
        if len(input_shape) >= 2 and len(weight_shape) >= 2:
            input_dim = input_shape[-1]
            output_dim = weight_shape[0] if weight_shape[1] == input_dim else weight_shape[1]
            
            # 根据维度特征推断
            if input_dim == 4096 and output_dim == 4096:
                return 'attention_proj'  # 注意力投影
            elif input_dim == 4096 and output_dim == 14336:
                return 'ffn_up_gate'     # FFN上投影/门控
            elif input_dim == 14336 and output_dim == 4096:
                return 'ffn_down'        # FFN下投影
            elif output_dim > 100000:
                return 'vocab_proj'      # 词表投影
            else:
                return 'other_linear'
        
        return 'unknown'
    
    def _classify_matmul_subtype(self, input_shapes: List[tuple]) -> str:
        """分类MatMul算子的子类型"""
        if len(input_shapes) >= 2:
            shape1, shape2 = input_shapes[0], input_shapes[1]
            if len(shape1) == 4 and len(shape2) == 4:
                # 4D tensor通常是注意力计算
                if shape1[-1] == shape2[-2]:  # Q @ K^T
                    return 'attention_scores'
                elif shape1[-1] == shape2[-1]:  # Attn @ V
                    return 'attention_output'
            elif len(shape1) == 2 and len(shape2) == 2:
                return 'matrix_mult'
        return 'unknown'
    
    def estimate_flops(self, op_name: str, input_shapes: List[tuple]) -> float:
        """估算FLOPs"""
        if not input_shapes:
            return 0.0
        
        try:
            if 'linear' in op_name.lower() and len(input_shapes) >= 2:
                input_shape, weight_shape = input_shapes[0], input_shapes[1]
                if len(input_shape) >= 2 and len(weight_shape) >= 2:
                    batch_size = np.prod(input_shape[:-1])
                    input_features = input_shape[-1]
                    output_features = weight_shape[0] if weight_shape[1] == input_features else weight_shape[1]
                    return 2.0 * batch_size * input_features * output_features
            
            elif 'matmul' in op_name.lower() or 'bmm' in op_name.lower():
                if len(input_shapes) >= 2:
                    shape1, shape2 = input_shapes[0], input_shapes[1]
                    if len(shape1) >= 2 and len(shape2) >= 2:
                        if len(shape1) == 4:  # 注意力计算
                            b, h, s1, d = shape1
                            s2 = shape2[-1]
                            return 2.0 * b * h * s1 * d * s2
                        else:  # 普通矩阵乘法
                            m, k = shape1[-2], shape1[-1]
                            n = shape2[-1]
                            return 2.0 * m * k * n
            
            elif 'embedding' in op_name.lower():
                if input_shapes:
                    return float(np.prod(input_shapes[0]) * 100)  # 估算值
                    
        except Exception:
            pass
        
        return 0.0
    
    def record_operation(self, op_name: str, input_tensors: List[torch.Tensor], 
                        output_tensors: List[torch.Tensor], execution_time: float = 0.0):
        """记录算子调用"""
        try:
            # 提取形状信息
            input_shapes = []
            input_dtypes = []
            device = "unknown"
            
            for tensor in input_tensors:
                if hasattr(tensor, 'shape') and hasattr(tensor, 'dtype'):
                    input_shapes.append(tuple(tensor.shape))
                    input_dtypes.append(str(tensor.dtype))
                    if hasattr(tensor, 'device'):
                        device = str(tensor.device)
            
            output_shapes = []
            output_dtypes = []
            
            for tensor in output_tensors:
                if hasattr(tensor, 'shape') and hasattr(tensor, 'dtype'):
                    output_shapes.append(tuple(tensor.shape))
                    output_dtypes.append(str(tensor.dtype))
            
            # 检测阶段
            phase = self.detect_phase(input_shapes)
            
            # 分类操作
            op_type, op_subtype = self.classify_operation(op_name, input_shapes)
            
            # 估算FLOPs
            flops = self.estimate_flops(op_name, input_shapes)
            
            # 创建记录
            record = {
                'timestamp': time.time() - self.start_time,
                'operation': op_name,
                'op_type': op_type,
                'op_subtype': op_subtype,
                'phase': phase,
                'input_shapes': input_shapes,
                'output_shapes': output_shapes,
                'input_dtypes': input_dtypes,
                'output_dtypes': output_dtypes,
                'device': device,
                'estimated_flops': flops,
                'execution_time_ms': execution_time * 1000 if execution_time else 0.0
            }
            
            with self.lock:
                self.operations.append(record)
                
                # 更新计数器
                if op_type in self.phase_counters:
                    self.phase_counters[op_type][phase] += 1
            
            # 实时输出
            print(f"[{phase}][{op_type}.{op_subtype}] {op_name}")
            print(f"  Input: {input_shapes} -> Output: {output_shapes}")
            print(f"  FLOPs: {flops:.2e}, Time: {execution_time*1000:.3f}ms")
            
        except Exception as e:
            print(f"记录算子时出错: {e}")
    
    def save_analysis(self):
        """保存分析结果"""
        print(f"\n保存真实推理分析结果...")
        
        # 按阶段统计
        prefill_ops = [op for op in self.operations if op['phase'] == 'prefill']
        decode_ops = [op for op in self.operations if op['phase'] == 'decode']
        unknown_ops = [op for op in self.operations if op['phase'] == 'unknown']
        
        # 按类型统计
        type_stats = defaultdict(lambda: {'count': 0, 'flops': 0.0, 'time': 0.0})
        subtype_stats = defaultdict(lambda: {'count': 0, 'flops': 0.0, 'time': 0.0})
        
        for op in self.operations:
            op_type = op['op_type']
            op_subtype = f"{op['op_type']}.{op['op_subtype']}"
            phase = op['phase']
            flops = op['estimated_flops']
            time_ms = op['execution_time_ms']
            
            type_stats[f"{phase}_{op_type}"]['count'] += 1
            type_stats[f"{phase}_{op_type}"]['flops'] += flops
            type_stats[f"{phase}_{op_type}"]['time'] += time_ms
            
            subtype_stats[f"{phase}_{op_subtype}"]['count'] += 1
            subtype_stats[f"{phase}_{op_subtype}"]['flops'] += flops
            subtype_stats[f"{phase}_{op_subtype}"]['time'] += time_ms
        
        # 生成分析报告
        analysis = {
            'summary': {
                'total_operations': len(self.operations),
                'execution_time_seconds': time.time() - self.start_time,
                'prefill_ops': len(prefill_ops),
                'decode_ops': len(decode_ops),
                'unknown_ops': len(unknown_ops),
                'phase_counters': self.phase_counters
            },
            'phase_analysis': {
                'prefill': self._analyze_phase_ops(prefill_ops),
                'decode': self._analyze_phase_ops(decode_ops),
                'unknown': self._analyze_phase_ops(unknown_ops)
            },
            'type_statistics': dict(type_stats),
            'subtype_statistics': dict(subtype_stats),
            'operation_timeline': self.operations[-50:],  # 最后50个操作
            'all_operations': self.operations
        }
        
        # 保存JSON
        json_file = os.path.join(self.output_dir, "real_inference_analysis.json")
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(analysis, f, indent=2, ensure_ascii=False)
        
        # 创建Markdown报告
        self._create_markdown_report(analysis)
        
        print(f"分析结果已保存到: {self.output_dir}")
        return analysis
    
    def _analyze_phase_ops(self, ops: List[Dict]) -> Dict:
        """分析特定阶段的操作"""
        if not ops:
            return {'count': 0, 'total_flops': 0.0, 'total_time': 0.0}
        
        op_counts = Counter(op['operation'] for op in ops)
        type_counts = Counter(f"{op['op_type']}.{op['op_subtype']}" for op in ops)
        total_flops = sum(op['estimated_flops'] for op in ops)
        total_time = sum(op['execution_time_ms'] for op in ops)
        
        return {
            'count': len(ops),
            'total_flops': total_flops,
            'total_time_ms': total_time,
            'operation_counts': dict(op_counts.most_common(10)),
            'type_counts': dict(type_counts.most_common(10)),
            'avg_flops_per_op': total_flops / len(ops),
            'typical_shapes': [(op['input_shapes'], op['output_shapes']) for op in ops[:5]]
        }
    
    def _create_markdown_report(self, analysis: Dict):
        """创建Markdown报告"""
        report_file = os.path.join(self.output_dir, "real_inference_report.md")
        
        summary = analysis['summary']
        prefill = analysis['phase_analysis']['prefill']
        decode = analysis['phase_analysis']['decode']
        
        content = f"""# SGLang 真实离线推理算子分析报告

## 执行摘要
- 总算子调用: {summary['total_operations']}个
- Prefill阶段: {summary['prefill_ops']}个
- Decode阶段: {summary['decode_ops']}个
- 未分类: {summary['unknown_ops']}个
- 总执行时间: {summary['execution_time_seconds']:.2f}秒

## 阶段对比分析

### Prefill阶段
- 操作数量: {prefill['count']}
- 总计算量: {prefill['total_flops']:.2e} FLOPs
- 总时间: {prefill['total_time_ms']:.2f}ms
- 主要操作类型: {prefill['type_counts']}

### Decode阶段  
- 操作数量: {decode['count']}
- 总计算量: {decode['total_flops']:.2e} FLOPs
- 总时间: {decode['total_time_ms']:.2f}ms
- 主要操作类型: {decode['type_counts']}

## 性能对比
- 计算量比 (Prefill:Decode): {prefill['total_flops']/decode['total_flops'] if decode['total_flops'] > 0 else 'N/A'}
- 时间比 (Prefill:Decode): {prefill['total_time_ms']/decode['total_time_ms'] if decode['total_time_ms'] > 0 else 'N/A'}

## 算子类型分布
"""
        
        for phase_name, phase_data in analysis['phase_analysis'].items():
            if phase_data['count'] > 0:
                content += f"\n### {phase_name.upper()}阶段算子分布\n"
                for op_type, count in phase_data['type_counts'].items():
                    content += f"- {op_type}: {count}次\n"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"Markdown报告已保存到: {report_file}")

# 创建全局追踪器
tracker = RealInferenceTracker()

# Hook函数
def create_hook(func_name: str, module, attr_name: str):
    """创建函数Hook"""
    original_func = getattr(module, attr_name)
    
    @wraps(original_func)
    def hooked_func(*args, **kwargs):
        # 提取tensor参数
        input_tensors = []
        for arg in args:
            if isinstance(arg, torch.Tensor):
                input_tensors.append(arg)
        
        # 执行原函数并测量时间
        start_time = time.perf_counter()
        result = original_func(*args, **kwargs)
        end_time = time.perf_counter()
        execution_time = end_time - start_time
        
        # 提取输出tensor
        output_tensors = []
        if isinstance(result, torch.Tensor):
            output_tensors.append(result)
        elif isinstance(result, (tuple, list)):
            for item in result:
                if isinstance(item, torch.Tensor):
                    output_tensors.append(item)
        
        # 记录操作
        tracker.record_operation(func_name, input_tensors, output_tensors, execution_time)
        
        return result
    
    setattr(module, attr_name, hooked_func)
    return original_func

# 注册Hooks
original_functions = {}
hooks_to_register = [
    ('linear', torch.nn.functional, 'linear'),
    ('matmul', torch, 'matmul'),
    ('bmm', torch, 'bmm'),
    ('addmm', torch, 'addmm'),
    ('mm', torch, 'mm'),
    ('embedding', torch.nn.functional, 'embedding'),
]

print("注册算子Hooks...")
for func_name, module, attr_name in hooks_to_register:
    try:
        original_functions[f"{module.__name__}.{attr_name}"] = create_hook(func_name, module, attr_name)
        print(f"已注册: {func_name}")
    except AttributeError:
        print(f"跳过: {func_name} (不存在)")

def main():
    """主函数：运行真实的SGLang离线推理"""
    try:
        print("开始真实SGLang离线推理测试...")
        
        # 导入SGLang
        from sglang import LLM, SamplingParams
        
        print("初始化SGLang LLM...")
        llm = LLM(
            model_path="/home/<USER>/deepseek-int8",
            tp_size=2,
            trust_remote_code=True,
        )
        
        print("LLM初始化完成，开始推理...")
        
        # 测试用例
        test_prompts = [
            "深度学习是什么？",
            "请简单解释Transformer架构的工作原理。",
            "人工智能的发展历程包括哪些重要阶段？"
        ]
        
        sampling_params = SamplingParams(
            max_new_tokens=20,
            temperature=0.1
        )
        
        for i, prompt in enumerate(test_prompts):
            print(f"\n{'='*50}")
            print(f"测试 {i+1}: {prompt[:50]}...")
            print(f"{'='*50}")
            
            start_time = time.time()
            outputs = llm.generate([prompt], sampling_params)
            end_time = time.time()
            
            for output in outputs:
                print(f"输出: {output.outputs[0].text}")
                print(f"耗时: {(end_time-start_time)*1000:.2f}ms")
            
            time.sleep(1)  # 等待统计完成
        
        print("\n推理完成，保存分析结果...")
        analysis = tracker.save_analysis()
        
        # 输出简要统计
        print(f"\n=== 统计摘要 ===")
        print(f"总算子: {len(tracker.operations)}")
        print(f"Prefill: {len([op for op in tracker.operations if op['phase'] == 'prefill'])}")
        print(f"Decode: {len([op for op in tracker.operations if op['phase'] == 'decode'])}")
        
        # 按类型统计
        type_counts = Counter(op['op_type'] for op in tracker.operations)
        print(f"算子类型: {dict(type_counts)}")
        
    except Exception as e:
        print(f"执行出错: {e}")
        traceback.print_exc()
    
    finally:
        # 恢复原函数
        print("恢复原始函数...")
        for func_path, original_func in original_functions.items():
            try:
                module_name, attr_name = func_path.rsplit('.', 1)
                if 'torch.nn.functional' in module_name:
                    setattr(torch.nn.functional, attr_name, original_func)
                elif 'torch' in module_name:
                    setattr(torch, attr_name, original_func)
            except Exception as e:
                print(f"恢复函数 {func_path} 失败: {e}")

if __name__ == "__main__":
    main()
