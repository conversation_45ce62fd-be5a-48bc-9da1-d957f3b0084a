#!/usr/bin/env python3
"""
全面的算子追踪与模块分类分析脚本
按照prefill/decode阶段 + expert/attn/ffn/mlp模块 + gate/up/down操作进行详细分类
"""

import os
import sys
import torch
import torch.nn.functional as F
import json
import time
import logging
import threading
import re
from datetime import datetime
from collections import defaultdict, Counter
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict

# 设置日志
log_file = f"comprehensive_operator_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class DetailedOperatorCall:
    """详细的算子调用记录"""
    name: str
    input_shapes: List[List[int]]
    output_shapes: List[List[int]]
    dtypes: List[str]
    device: str
    timestamp: float
    phase: str  # prefill/decode
    module_type: str  # attn/ffn/mlp/expert/embedding/output
    operation_type: str  # query/key/value/gate/up/down/output/lookup
    layer_id: Optional[int]  # 层编号
    expert_id: Optional[int]  # 专家编号
    memory_before: float
    memory_after: float
    flops: float
    execution_time: float
    
    def to_dict(self):
        return asdict(self)

class ComprehensiveOperatorTracker:
    """全面的算子追踪器 - 支持模块级别的详细分类"""
    
    def __init__(self):
        self.operations = []
        self.phase = "unknown"
        self.start_time = time.time()
        self.lock = threading.Lock()
        self.output_dir = f"comprehensive_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 当前追踪状态
        self.current_layer = 0
        self.current_expert = None
        self.call_stack = []  # 调用栈，用于推断模块类型
        
        # 算子计数器 - 按模块分类
        self.module_counters = {
            'embedding': {'total': 0, 'prefill': 0, 'decode': 0},
            'attn': {
                'query': {'total': 0, 'prefill': 0, 'decode': 0},
                'key': {'total': 0, 'prefill': 0, 'decode': 0},
                'value': {'total': 0, 'prefill': 0, 'decode': 0},
                'output': {'total': 0, 'prefill': 0, 'decode': 0},
                'total': {'total': 0, 'prefill': 0, 'decode': 0}
            },
            'ffn': {
                'gate': {'total': 0, 'prefill': 0, 'decode': 0},
                'up': {'total': 0, 'prefill': 0, 'decode': 0},
                'down': {'total': 0, 'prefill': 0, 'decode': 0},
                'total': {'total': 0, 'prefill': 0, 'decode': 0}
            },
            'expert': {
                'gate': {'total': 0, 'prefill': 0, 'decode': 0},
                'up': {'total': 0, 'prefill': 0, 'decode': 0},
                'down': {'total': 0, 'prefill': 0, 'decode': 0},
                'router': {'total': 0, 'prefill': 0, 'decode': 0},
                'total': {'total': 0, 'prefill': 0, 'decode': 0}
            },
            'output': {'total': 0, 'prefill': 0, 'decode': 0},
            'other': {'total': 0, 'prefill': 0, 'decode': 0}
        }
        
        # FLOPs统计 - 按模块分类
        self.flops_counters = {
            'embedding': {'total': 0.0, 'prefill': 0.0, 'decode': 0.0},
            'attn': {
                'query': {'total': 0.0, 'prefill': 0.0, 'decode': 0.0},
                'key': {'total': 0.0, 'prefill': 0.0, 'decode': 0.0},
                'value': {'total': 0.0, 'prefill': 0.0, 'decode': 0.0},
                'output': {'total': 0.0, 'prefill': 0.0, 'decode': 0.0},
                'matmul': {'total': 0.0, 'prefill': 0.0, 'decode': 0.0},
                'total': {'total': 0.0, 'prefill': 0.0, 'decode': 0.0}
            },
            'ffn': {
                'gate': {'total': 0.0, 'prefill': 0.0, 'decode': 0.0},
                'up': {'total': 0.0, 'prefill': 0.0, 'decode': 0.0},
                'down': {'total': 0.0, 'prefill': 0.0, 'decode': 0.0},
                'total': {'total': 0.0, 'prefill': 0.0, 'decode': 0.0}
            },
            'expert': {
                'gate': {'total': 0.0, 'prefill': 0.0, 'decode': 0.0},
                'up': {'total': 0.0, 'prefill': 0.0, 'decode': 0.0},
                'down': {'total': 0.0, 'prefill': 0.0, 'decode': 0.0},
                'router': {'total': 0.0, 'prefill': 0.0, 'decode': 0.0},
                'total': {'total': 0.0, 'prefill': 0.0, 'decode': 0.0}
            },
            'output': {'total': 0.0, 'prefill': 0.0, 'decode': 0.0},
            'other': {'total': 0.0, 'prefill': 0.0, 'decode': 0.0}
        }
        
        logger.info(f"初始化ComprehensiveOperatorTracker，输出目录: {self.output_dir}")
        
    def get_memory_info(self):
        """获取GPU内存使用情况"""
        try:
            if torch.cuda.is_available():
                return torch.cuda.memory_allocated() / 1024**3  # GB
            return 0.0
        except:
            return 0.0
    
    def detect_phase(self, shapes, token_count=None):
        """检测当前是prefill还是decode阶段"""
        if token_count is not None:
            if token_count == 1:
                return "decode"
            elif token_count > 1:
                return "prefill"
        
        # 通过形状判断
        for shape in shapes:
            if len(shape) >= 2:
                seq_len = shape[-2] if len(shape) > 2 else shape[-1]
                if seq_len > 50:
                    return "prefill"
                elif seq_len == 1:
                    return "decode"
        return "unknown"
    
    def classify_module_and_operation(self, op_name: str, input_shapes: List[List[int]], output_shapes: List[List[int]]) -> Tuple[str, str, Optional[int], Optional[int]]:
        """
        根据算子名称和形状分类模块类型和操作类型
        返回: (module_type, operation_type, layer_id, expert_id)
        """
        op_lower = op_name.lower()
        
        # 推断层编号
        layer_id = self._infer_layer_id(input_shapes, output_shapes)
        expert_id = None
        
        # Embedding层
        if 'embedding' in op_lower:
            return 'embedding', 'lookup', layer_id, expert_id
        
        # 根据形状特征和上下文推断模块类型
        if len(input_shapes) >= 2 and len(input_shapes[0]) >= 2 and len(input_shapes[1]) >= 2:
            input_dim = input_shapes[0][-1]
            weight_shape = input_shapes[1]
            output_dim = output_shapes[0][-1] if output_shapes and len(output_shapes[0]) >= 1 else None
            
            # 隐藏层维度 (通常4096)
            hidden_size = 4096
            # 中间层维度 (通常14336)
            intermediate_size = 14336
            # 注意力头数 (通常32)
            num_heads = 32
            head_dim = hidden_size // num_heads
            
            # 注意力模块识别
            if (input_dim == hidden_size and 
                len(weight_shape) == 2 and weight_shape[0] == hidden_size and weight_shape[1] == hidden_size):
                # Self-attention的Q/K/V投影
                if self._is_attention_context():
                    if self.current_layer % 3 == 0:
                        return 'attn', 'query', layer_id, expert_id
                    elif self.current_layer % 3 == 1:
                        return 'attn', 'key', layer_id, expert_id
                    else:
                        return 'attn', 'value', layer_id, expert_id
                else:
                    return 'attn', 'output', layer_id, expert_id
            
            # FFN模块识别
            elif (input_dim == hidden_size and 
                  len(weight_shape) == 2 and weight_shape[0] == intermediate_size and weight_shape[1] == hidden_size):
                # Gate/Up投影
                if self._is_ffn_context():
                    if self.current_layer % 2 == 0:
                        return 'ffn', 'gate', layer_id, expert_id
                    else:
                        return 'ffn', 'up', layer_id, expert_id
                else:
                    return 'expert', 'gate', layer_id, expert_id
            
            elif (input_dim == intermediate_size and 
                  len(weight_shape) == 2 and weight_shape[0] == hidden_size and weight_shape[1] == intermediate_size):
                # Down投影
                if self._is_ffn_context():
                    return 'ffn', 'down', layer_id, expert_id
                else:
                    return 'expert', 'down', layer_id, expert_id
            
            # 专家路由识别
            elif (input_dim == hidden_size and output_dim and output_dim < hidden_size):
                return 'expert', 'router', layer_id, expert_id
        
        # MatMul操作的特殊处理
        if 'matmul' in op_lower or 'bmm' in op_lower:
            if self._is_attention_matmul(input_shapes):
                return 'attn', 'matmul', layer_id, expert_id
        
        # 输出层
        if output_shapes and len(output_shapes[0]) >= 2:
            vocab_size = 128256  # DeepSeek词表大小
            if output_shapes[0][-1] == vocab_size:
                return 'output', 'projection', layer_id, expert_id
        
        return 'other', 'unknown', layer_id, expert_id
    
    def _infer_layer_id(self, input_shapes: List[List[int]], output_shapes: List[List[int]]) -> Optional[int]:
        """推断当前层编号"""
        # 简化版本：基于操作计数推断
        total_ops = len(self.operations)
        # 假设每层有固定数量的主要操作
        ops_per_layer = 10  # 估算值
        return total_ops // ops_per_layer
    
    def _is_attention_context(self) -> bool:
        """判断是否在注意力计算上下文中"""
        # 简化版本：基于最近的操作模式
        recent_ops = [op.module_type for op in self.operations[-5:]]
        return recent_ops.count('attn') > recent_ops.count('ffn')
    
    def _is_ffn_context(self) -> bool:
        """判断是否在FFN计算上下文中"""
        # 简化版本：基于最近的操作模式
        recent_ops = [op.module_type for op in self.operations[-5:]]
        return recent_ops.count('ffn') > recent_ops.count('attn')
    
    def _is_attention_matmul(self, input_shapes: List[List[int]]) -> bool:
        """判断是否为注意力机制中的矩阵乘法"""
        if len(input_shapes) >= 2:
            shape1, shape2 = input_shapes[0], input_shapes[1]
            # 注意力分数计算: [batch, heads, seq, head_dim] × [batch, heads, head_dim, seq]
            # 注意力输出计算: [batch, heads, seq, seq] × [batch, heads, seq, head_dim]
            if (len(shape1) == 4 and len(shape2) == 4 and 
                shape1[0] == shape2[0] and shape1[1] == shape2[1]):
                return True
        return False
    
    def estimate_flops(self, op_name: str, shapes: List[List[int]]) -> float:
        """估算FLOPs"""
        if not shapes or len(shapes) < 2:
            return 0.0
            
        try:
            if 'linear' in op_name.lower() or 'mm' in op_name.lower() or 'matmul' in op_name.lower():
                # 矩阵乘法: M*K * K*N = M*N*K FLOPs  
                if len(shapes) >= 2:
                    shape1, shape2 = shapes[0], shapes[1]
                    if len(shape1) >= 2 and len(shape2) >= 2:
                        # 处理batch维度
                        batch_size = 1
                        if len(shape1) > 2:
                            batch_size = shape1[0]
                        
                        m = shape1[-2] if len(shape1) > 1 else shape1[-1]
                        k = shape1[-1]
                        n = shape2[-1] if len(shape2) > 1 else shape2[0]
                        return float(batch_size * m * k * n * 2)  # 乘法+加法
            elif 'embedding' in op_name.lower():
                if shapes and len(shapes[0]) >= 1:
                    return float(shapes[0][-1] * 100)  # 估算
            elif 'bmm' in op_name.lower():
                if len(shapes) >= 2:
                    shape1, shape2 = shapes[0], shapes[1]
                    if len(shape1) >= 3 and len(shape2) >= 3:
                        b, m, k = shape1[-3], shape1[-2], shape1[-1]
                        n = shape2[-1]
                        return float(b * m * k * n * 2)
        except:
            pass
        return 0.0
    
    def record_op(self, op_name: str, input_shapes: List[List[int]], output_shapes: List[List[int]], 
                  dtypes: List[str], device: str, execution_time: float = 0.0):
        """记录算子调用"""
        try:
            # 检测阶段
            phase = self.detect_phase(input_shapes)
            
            # 分类模块和操作
            module_type, operation_type, layer_id, expert_id = self.classify_module_and_operation(
                op_name, input_shapes, output_shapes
            )
            
            # 计算性能指标
            memory_before = self.get_memory_info()
            flops = self.estimate_flops(op_name, input_shapes)
            
            # 创建详细记录
            op_call = DetailedOperatorCall(
                name=op_name,
                input_shapes=input_shapes,
                output_shapes=output_shapes,
                dtypes=dtypes,
                device=device,
                timestamp=time.time() - self.start_time,
                phase=phase,
                module_type=module_type,
                operation_type=operation_type,
                layer_id=layer_id,
                expert_id=expert_id,
                memory_before=memory_before,
                memory_after=memory_before,
                flops=flops,
                execution_time=execution_time
            )
            
            with self.lock:
                self.operations.append(op_call)
                
                # 更新计数器
                self._update_counters(module_type, operation_type, phase, flops)
                
            # 实时输出关键算子
            logger.info(f"[{phase}][{module_type}.{operation_type}] {op_name} | "
                       f"Layer:{layer_id} | Input:{input_shapes} | Output:{output_shapes} | "
                       f"FLOPs:{flops:.2e}")
                
        except Exception as e:
            logger.error(f"记录算子错误: {e}")
    
    def _update_counters(self, module_type: str, operation_type: str, phase: str, flops: float):
        """更新计数器"""
        try:
            # 更新算子计数
            if module_type in self.module_counters:
                if isinstance(self.module_counters[module_type], dict):
                    if operation_type in self.module_counters[module_type]:
                        self.module_counters[module_type][operation_type]['total'] += 1
                        self.module_counters[module_type][operation_type][phase] += 1
                    if 'total' in self.module_counters[module_type]:
                        self.module_counters[module_type]['total']['total'] += 1
                        self.module_counters[module_type]['total'][phase] += 1
                else:
                    self.module_counters[module_type]['total'] += 1
                    self.module_counters[module_type][phase] += 1
            
            # 更新FLOPs计数
            if module_type in self.flops_counters:
                if isinstance(self.flops_counters[module_type], dict):
                    if operation_type in self.flops_counters[module_type]:
                        self.flops_counters[module_type][operation_type]['total'] += flops
                        self.flops_counters[module_type][operation_type][phase] += flops
                    if 'total' in self.flops_counters[module_type]:
                        self.flops_counters[module_type]['total']['total'] += flops
                        self.flops_counters[module_type]['total'][phase] += flops
                else:
                    self.flops_counters[module_type]['total'] += flops
                    self.flops_counters[module_type][phase] += flops
        except Exception as e:
            logger.error(f"更新计数器错误: {e}")
    
    def hook_linear(self, original_linear):
        """Hook torch.nn.functional.linear"""
        def wrapped_linear(*args, **kwargs):
            start_time = time.time()
            result = original_linear(*args, **kwargs)
            execution_time = time.time() - start_time
            
            try:
                input_shapes = []
                output_shapes = []
                dtypes = []
                device = "unknown"
                
                # 处理输入
                for arg in args:
                    if hasattr(arg, 'shape') and hasattr(arg, 'dtype'):
                        input_shapes.append(list(arg.shape))
                        dtypes.append(str(arg.dtype))
                        device = str(arg.device) if hasattr(arg, 'device') else device
                
                # 处理输出
                if hasattr(result, 'shape'):
                    output_shapes.append(list(result.shape))
                
                self.record_op("torch.nn.functional.linear", input_shapes, output_shapes, 
                              dtypes, device, execution_time)
            except:
                pass
            return result
        return wrapped_linear
    
    def hook_embedding(self, original_embedding):
        """Hook torch.nn.functional.embedding"""
        def wrapped_embedding(*args, **kwargs):
            start_time = time.time()
            result = original_embedding(*args, **kwargs)
            execution_time = time.time() - start_time
            
            try:
                input_shapes = []
                output_shapes = []
                dtypes = []
                device = "unknown"
                
                # 处理输入
                for arg in args:
                    if hasattr(arg, 'shape') and hasattr(arg, 'dtype'):
                        input_shapes.append(list(arg.shape))
                        dtypes.append(str(arg.dtype))
                        device = str(arg.device) if hasattr(arg, 'device') else device
                
                # 处理输出
                if hasattr(result, 'shape'):
                    output_shapes.append(list(result.shape))
                
                self.record_op("torch.nn.functional.embedding", input_shapes, output_shapes, 
                              dtypes, device, execution_time)
            except:
                pass
            return result
        return wrapped_embedding
    
    def hook_matmul(self, original_matmul):
        """Hook torch.matmul"""
        def wrapped_matmul(*args, **kwargs):
            start_time = time.time()
            result = original_matmul(*args, **kwargs)
            execution_time = time.time() - start_time
            
            try:
                input_shapes = []
                output_shapes = []
                dtypes = []
                device = "unknown"
                
                # 处理输入
                for arg in args:
                    if hasattr(arg, 'shape') and hasattr(arg, 'dtype'):
                        input_shapes.append(list(arg.shape))
                        dtypes.append(str(arg.dtype))
                        device = str(arg.device) if hasattr(arg, 'device') else device
                
                # 处理输出
                if hasattr(result, 'shape'):
                    output_shapes.append(list(result.shape))
                
                self.record_op("torch.matmul", input_shapes, output_shapes, dtypes, device, execution_time)
            except:
                pass
            return result
        return wrapped_matmul
    
    def hook_bmm(self, original_bmm):
        """Hook torch.bmm"""
        def wrapped_bmm(*args, **kwargs):
            start_time = time.time()
            result = original_bmm(*args, **kwargs)
            execution_time = time.time() - start_time
            
            try:
                input_shapes = []
                output_shapes = []
                dtypes = []
                device = "unknown"
                
                # 处理输入
                for arg in args:
                    if hasattr(arg, 'shape') and hasattr(arg, 'dtype'):
                        input_shapes.append(list(arg.shape))
                        dtypes.append(str(arg.dtype))
                        device = str(arg.device) if hasattr(arg, 'device') else device
                
                # 处理输出
                if hasattr(result, 'shape'):
                    output_shapes.append(list(result.shape))
                
                self.record_op("torch.bmm", input_shapes, output_shapes, dtypes, device, execution_time)
            except:
                pass
            return result
        return wrapped_bmm
    
    def register_hooks(self):
        """注册所有hooks"""
        logger.info("注册算子hooks...")
        
        # 保存原始函数
        self.original_linear = F.linear
        self.original_embedding = F.embedding
        self.original_matmul = torch.matmul
        self.original_bmm = torch.bmm
        
        # 替换为hooked版本
        F.linear = self.hook_linear(self.original_linear)
        F.embedding = self.hook_embedding(self.original_embedding)
        torch.matmul = self.hook_matmul(self.original_matmul)
        torch.bmm = self.hook_bmm(self.original_bmm)
        
        logger.info("算子hooks注册完成")
    
    def restore_hooks(self):
        """恢复原始函数"""
        F.linear = self.original_linear
        F.embedding = self.original_embedding
        torch.matmul = self.original_matmul
        torch.bmm = self.original_bmm
    
    def save_comprehensive_analysis(self):
        """保存全面的分析结果"""
        logger.info("保存全面分析结果...")
        
        try:
            # 按阶段分组
            prefill_ops = [op for op in self.operations if op.phase == "prefill"]
            decode_ops = [op for op in self.operations if op.phase == "decode"]
            
            # 按模块分组
            module_groups = defaultdict(lambda: defaultdict(list))
            for op in self.operations:
                module_groups[op.phase][f"{op.module_type}.{op.operation_type}"].append(op)
            
            # 生成全面分析
            analysis = {
                "execution_summary": {
                    "total_operations": len(self.operations),
                    "execution_time_seconds": time.time() - self.start_time,
                    "gpu_count": torch.cuda.device_count() if torch.cuda.is_available() else 0,
                    "prefill_ops": len(prefill_ops),
                    "decode_ops": len(decode_ops)
                },
                "module_statistics": {
                    "operator_counts": self.module_counters,
                    "flops_distribution": self.flops_counters
                },
                "phase_analysis": {
                    "prefill": self._analyze_phase_by_modules(prefill_ops),
                    "decode": self._analyze_phase_by_modules(decode_ops)
                },
                "module_breakdown": {
                    "prefill": self._breakdown_by_modules(module_groups["prefill"]),
                    "decode": self._breakdown_by_modules(module_groups["decode"])
                },
                "detailed_timeline": [op.to_dict() for op in self.operations],
                "performance_metrics": self._calculate_performance_metrics()
            }
            
            # 保存JSON
            json_file = os.path.join(self.output_dir, "comprehensive_analysis.json")
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(analysis, f, indent=2, ensure_ascii=False)
            
            logger.info(f"全面分析结果已保存到: {json_file}")
            
            # 创建详细报告
            self._create_comprehensive_report(analysis)
            
            return analysis
            
        except Exception as e:
            logger.error(f"保存全面分析结果错误: {e}")
            return None
    
    def _analyze_phase_by_modules(self, ops: List[DetailedOperatorCall]) -> Dict:
        """按模块分析阶段"""
        if not ops:
            return {}
        
        module_stats = defaultdict(lambda: {'count': 0, 'flops': 0.0, 'time': 0.0})
        operation_stats = defaultdict(lambda: {'count': 0, 'flops': 0.0, 'time': 0.0})
        
        for op in ops:
            module_key = op.module_type
            operation_key = f"{op.module_type}.{op.operation_type}"
            
            module_stats[module_key]['count'] += 1
            module_stats[module_key]['flops'] += op.flops
            module_stats[module_key]['time'] += op.execution_time
            
            operation_stats[operation_key]['count'] += 1
            operation_stats[operation_key]['flops'] += op.flops
            operation_stats[operation_key]['time'] += op.execution_time
        
        return {
            'module_stats': dict(module_stats),
            'operation_stats': dict(operation_stats),
            'total_flops': sum(op.flops for op in ops),
            'total_time': sum(op.execution_time for op in ops),
            'operation_count': len(ops)
        }
    
    def _breakdown_by_modules(self, module_groups: Dict) -> Dict:
        """按模块详细分解"""
        breakdown = {}
        for module_op, ops in module_groups.items():
            if ops:
                breakdown[module_op] = {
                    'count': len(ops),
                    'total_flops': sum(op.flops for op in ops),
                    'avg_flops': sum(op.flops for op in ops) / len(ops),
                    'total_time': sum(op.execution_time for op in ops),
                    'avg_time': sum(op.execution_time for op in ops) / len(ops),
                    'typical_shapes': {
                        'input': ops[0].input_shapes if ops else [],
                        'output': ops[0].output_shapes if ops else []
                    },
                    'layers': list(set(op.layer_id for op in ops if op.layer_id is not None))
                }
        return breakdown
    
    def _calculate_performance_metrics(self) -> Dict:
        """计算性能指标"""
        if not self.operations:
            return {}
        
        total_flops = sum(op.flops for op in self.operations)
        total_time = sum(op.execution_time for op in self.operations)
        
        prefill_ops = [op for op in self.operations if op.phase == "prefill"]
        decode_ops = [op for op in self.operations if op.phase == "decode"]
        
        prefill_flops = sum(op.flops for op in prefill_ops)
        decode_flops = sum(op.flops for op in decode_ops)
        
        return {
            'total_flops': total_flops,
            'total_execution_time': total_time,
            'average_flops_per_op': total_flops / len(self.operations),
            'flops_per_second': total_flops / total_time if total_time > 0 else 0,
            'prefill_decode_flops_ratio': prefill_flops / decode_flops if decode_flops > 0 else float('inf'),
            'prefill_percentage': (prefill_flops / total_flops * 100) if total_flops > 0 else 0,
            'decode_percentage': (decode_flops / total_flops * 100) if total_flops > 0 else 0
        }
    
    def _create_comprehensive_report(self, analysis: Dict):
        """创建全面的报告"""
        report_file = os.path.join(self.output_dir, "comprehensive_report.md")
        
        content = f"""# SGLang DeepSeek V3 INT8 全面算子分析报告

## 🎯 执行概述
- 总算子调用次数: {analysis['execution_summary']['total_operations']}
- Prefill阶段算子: {analysis['execution_summary']['prefill_ops']}
- Decode阶段算子: {analysis['execution_summary']['decode_ops']}
- 总执行时间: {analysis['execution_summary']['execution_time_seconds']:.2f}秒

## 📊 模块级别统计

### 算子计数分布
"""
        
        # 添加模块统计
        module_counts = analysis['module_statistics']['operator_counts']
        for module, stats in module_counts.items():
            if isinstance(stats, dict) and 'total' in stats:
                if isinstance(stats['total'], dict):
                    total_count = stats['total']['total']
                    prefill_count = stats['total']['prefill']
                    decode_count = stats['total']['decode']
                else:
                    total_count = stats['total']
                    prefill_count = stats['prefill']
                    decode_count = stats['decode']
                
                content += f"\n#### {module.upper()}模块\n"
                content += f"- 总调用次数: {total_count}\n"
                content += f"- Prefill阶段: {prefill_count}\n"
                content += f"- Decode阶段: {decode_count}\n"
                
                # 添加子操作统计
                if isinstance(stats, dict):
                    for op_type, op_stats in stats.items():
                        if op_type != 'total' and isinstance(op_stats, dict):
                            if 'total' in op_stats:
                                content += f"  - {op_type}: {op_stats['total']} (Prefill: {op_stats['prefill']}, Decode: {op_stats['decode']})\n"
        
        content += f"""

## 🔍 阶段详细分析

### Prefill阶段
"""
        
        # 添加Prefill分析
        if 'prefill' in analysis['phase_analysis']:
            prefill_analysis = analysis['phase_analysis']['prefill']
            content += f"- 算子数量: {prefill_analysis.get('operation_count', 0)}\n"
            content += f"- 总FLOPs: {prefill_analysis.get('total_flops', 0):.2e}\n"
            content += f"- 总执行时间: {prefill_analysis.get('total_time', 0):.4f}秒\n\n"
            
            content += "#### 模块分解:\n"
            for module, stats in prefill_analysis.get('module_stats', {}).items():
                content += f"- **{module}**: {stats['count']}次调用, {stats['flops']:.2e} FLOPs\n"
        
        content += f"""

### Decode阶段
"""
        
        # 添加Decode分析
        if 'decode' in analysis['phase_analysis']:
            decode_analysis = analysis['phase_analysis']['decode']
            content += f"- 算子数量: {decode_analysis.get('operation_count', 0)}\n"
            content += f"- 总FLOPs: {decode_analysis.get('total_flops', 0):.2e}\n"
            content += f"- 总执行时间: {decode_analysis.get('total_time', 0):.4f}秒\n\n"
            
            content += "#### 模块分解:\n"
            for module, stats in decode_analysis.get('module_stats', {}).items():
                content += f"- **{module}**: {stats['count']}次调用, {stats['flops']:.2e} FLOPs\n"
        
        # 添加性能指标
        if 'performance_metrics' in analysis:
            metrics = analysis['performance_metrics']
            content += f"""

## ⚡ 性能指标
- 总计算量: {metrics.get('total_flops', 0):.2e} FLOPs
- 平均每算子FLOPs: {metrics.get('average_flops_per_op', 0):.2e}
- 计算效率: {metrics.get('flops_per_second', 0):.2e} FLOP/s
- Prefill/Decode计算比: {metrics.get('prefill_decode_flops_ratio', 0):.1f}:1
- Prefill占比: {metrics.get('prefill_percentage', 0):.1f}%
- Decode占比: {metrics.get('decode_percentage', 0):.1f}%
"""
        
        content += """

## 🔬 技术洞察
1. **模块计算分布**: 展示了不同模块的计算负载
2. **阶段特征对比**: Prefill与Decode的显著差异
3. **操作类型分析**: Gate/Up/Down等具体操作的性能特征
4. **层级分布**: 不同层的计算模式
"""
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        logger.info(f"全面报告已保存到: {report_file}")

def simulate_comprehensive_model_inference():
    """模拟全面的模型推理过程 - 包含更多模块类型"""
    logger.info("开始模拟全面的模型推理...")
    
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    
    # 模型参数
    vocab_size = 128256
    hidden_size = 4096
    intermediate_size = 14336
    num_layers = 2  # 简化为2层
    num_heads = 32
    head_dim = hidden_size // num_heads
    num_experts = 8  # MoE专家数量
    
    # 模拟Prefill阶段
    logger.info("=== 模拟Prefill阶段 ===")
    batch_size = 1
    seq_len = 512
    
    # 1. Embedding
    input_ids = torch.randint(0, vocab_size, (batch_size, seq_len), device=device)
    embedding_weight = torch.randn(vocab_size, hidden_size, device=device, dtype=torch.float16)
    hidden_states = F.embedding(input_ids, embedding_weight)
    
    for layer_idx in range(num_layers):
        logger.info(f"--- Layer {layer_idx} ---")
        
        # 2. Self-Attention
        # Q, K, V projections
        query_weight = torch.randn(hidden_size, hidden_size, device=device, dtype=torch.float16)
        key_weight = torch.randn(hidden_size, hidden_size, device=device, dtype=torch.float16)
        value_weight = torch.randn(hidden_size, hidden_size, device=device, dtype=torch.float16)
        
        query = F.linear(hidden_states, query_weight)
        key = F.linear(hidden_states, key_weight)
        value = F.linear(hidden_states, value_weight)
        
        # Reshape for multi-head attention
        query = query.view(batch_size, seq_len, num_heads, head_dim).transpose(1, 2)
        key = key.view(batch_size, seq_len, num_heads, head_dim).transpose(1, 2)
        value = value.view(batch_size, seq_len, num_heads, head_dim).transpose(1, 2)
        
        # Attention computation
        scores = torch.matmul(query, key.transpose(-2, -1)) / (head_dim ** 0.5)
        attn_weights = torch.softmax(scores, dim=-1)
        attn_output = torch.matmul(attn_weights, value)
        
        # Output projection
        attn_output = attn_output.transpose(1, 2).contiguous().view(batch_size, seq_len, hidden_size)
        output_weight = torch.randn(hidden_size, hidden_size, device=device, dtype=torch.float16)
        attn_output = F.linear(attn_output, output_weight)
        
        # 3. FFN with MoE
        # Router (专家选择)
        router_weight = torch.randn(num_experts, hidden_size, device=device, dtype=torch.float16)
        router_logits = F.linear(attn_output, router_weight)
        expert_weights = torch.softmax(router_logits, dim=-1)
        
        # 模拟选择top-2专家
        top_experts = torch.topk(expert_weights, 2, dim=-1)
        
        # 专家计算 (简化为普通FFN)
        gate_weight = torch.randn(intermediate_size, hidden_size, device=device, dtype=torch.float16)
        up_weight = torch.randn(intermediate_size, hidden_size, device=device, dtype=torch.float16)
        down_weight = torch.randn(hidden_size, intermediate_size, device=device, dtype=torch.float16)
        
        # Gate和Up投影
        gate_proj = F.linear(attn_output, gate_weight)
        up_proj = F.linear(attn_output, up_weight)
        
        # SiLU激活
        gate_proj = torch.nn.functional.silu(gate_proj)
        intermediate = gate_proj * up_proj
        
        # Down投影
        ffn_output = F.linear(intermediate, down_weight)
        
        # 更新hidden_states
        hidden_states = attn_output + ffn_output
    
    # 4. 输出层
    output_weight = torch.randn(vocab_size, hidden_size, device=device, dtype=torch.float16)
    logits = F.linear(hidden_states, output_weight)
    
    logger.info("Prefill阶段模拟完成")
    
    # 模拟Decode阶段
    logger.info("=== 模拟Decode阶段 ===")
    seq_len = 1  # 单token生成
    
    # 1. 新token的Embedding
    new_input_ids = torch.randint(0, vocab_size, (batch_size, seq_len), device=device)
    new_hidden_states = F.embedding(new_input_ids, embedding_weight)
    
    for layer_idx in range(num_layers):
        logger.info(f"--- Decode Layer {layer_idx} ---")
        
        # 2. Self-Attention (使用KV Cache)
        new_query = F.linear(new_hidden_states, query_weight)
        new_key = F.linear(new_hidden_states, key_weight)
        new_value = F.linear(new_hidden_states, value_weight)
        
        # Reshape
        new_query = new_query.view(batch_size, seq_len, num_heads, head_dim).transpose(1, 2)
        new_key = new_key.view(batch_size, seq_len, num_heads, head_dim).transpose(1, 2)
        new_value = new_value.view(batch_size, seq_len, num_heads, head_dim).transpose(1, 2)
        
        # 简化的attention计算
        scores = torch.matmul(new_query, new_key.transpose(-2, -1))
        attn_output = torch.matmul(scores, new_value)
        
        # Output projection
        attn_output = attn_output.transpose(1, 2).contiguous().view(batch_size, seq_len, hidden_size)
        attn_output = F.linear(attn_output, output_weight)
        
        # 3. FFN
        gate_proj = F.linear(attn_output, gate_weight)
        up_proj = F.linear(attn_output, up_weight)
        gate_proj = torch.nn.functional.silu(gate_proj)
        intermediate = gate_proj * up_proj
        ffn_output = F.linear(intermediate, down_weight)
        
        new_hidden_states = attn_output + ffn_output
    
    # 输出层
    new_logits = F.linear(new_hidden_states, output_weight)
    
    logger.info("Decode阶段模拟完成")
    
    # 额外的操作
    logger.info("=== 额外计算操作 ===")
    for i in range(3):
        a = torch.randn(batch_size, 256, 512, device=device, dtype=torch.float16)
        b = torch.randn(batch_size, 512, 1024, device=device, dtype=torch.float16)
        c = torch.bmm(a, b)
    
    logger.info("全面模拟推理完成")

def main():
    """主函数"""
    logger.info("开始全面的算子分析...")
    
    # 创建追踪器
    tracker = ComprehensiveOperatorTracker()
    
    try:
        # 注册hooks
        tracker.register_hooks()
        
        # 模拟全面的模型推理
        simulate_comprehensive_model_inference()
        
        # 等待所有算子调用完成
        time.sleep(1)
        
    except Exception as e:
        logger.error(f"执行过程中出错: {e}")
    
    finally:
        # 恢复原始函数
        tracker.restore_hooks()
        
        # 保存全面分析结果
        analysis = tracker.save_comprehensive_analysis()
        
        if analysis:
            logger.info(f"全面分析完成！结果保存在: {tracker.output_dir}")
            
            # 输出简要统计
            logger.info(f"捕获到 {len(tracker.operations)} 个算子调用")
            
            # 按阶段统计
            prefill_count = len([op for op in tracker.operations if op.phase == "prefill"])
            decode_count = len([op for op in tracker.operations if op.phase == "decode"])
            logger.info(f"Prefill阶段: {prefill_count} 个算子")
            logger.info(f"Decode阶段: {decode_count} 个算子")
            
            # 按模块统计
            module_stats = defaultdict(int)
            for op in tracker.operations:
                module_stats[f"{op.module_type}.{op.operation_type}"] += 1
            
            logger.info("模块操作统计:")
            for module_op, count in sorted(module_stats.items(), key=lambda x: x[1], reverse=True):
                logger.info(f"  {module_op}: {count}")

if __name__ == "__main__":
    main()
