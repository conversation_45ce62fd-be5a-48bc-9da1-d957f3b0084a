{"execution_summary": {"total_operations": 24, "execution_time_seconds": 2.9581494331359863, "gpu_count": 8, "prefill_ops": 15, "decode_ops": 9, "unknown_ops": 0, "operator_counters": {"embedding": 2, "linear": 13, "matmul": 4, "bmm": 5, "int8_mm": 0, "attention": 0}}, "prefill_analysis": {"operation_count": 15, "total_flops": 324404299776.0, "operation_types": {"torch.nn.functional.linear": 7, "torch.bmm": 5, "torch.matmul": 2, "torch.nn.functional.embedding": 1}, "avg_memory_usage": 1.3295220057169597, "typical_shapes": {"input_shapes": [[[1, 512], [128256, 4096]], [[1, 512, 4096], [4096, 4096]], [[1, 512, 4096], [4096, 4096]], [[1, 512, 4096], [4096, 4096]], [[1, 32, 512, 128], [1, 32, 128, 512]]], "output_shapes": [[[1, 512, 4096]], [[1, 512, 4096]], [[1, 512, 4096]], [[1, 512, 4096]], [[1, 32, 512, 512]]]}}, "decode_analysis": {"operation_count": 9, "total_flops": 578814564.0, "operation_types": {"torch.nn.functional.linear": 6, "torch.matmul": 2, "torch.nn.functional.embedding": 1}, "avg_memory_usage": 1.5353303485446506, "typical_shapes": {"input_shapes": [[[1, 1], [128256, 4096]], [[1, 1, 4096], [4096, 4096]], [[1, 1, 4096], [4096, 4096]], [[1, 1, 4096], [4096, 4096]], [[1, 32, 1, 128], [1, 32, 128, 1]]], "output_shapes": [[[1, 1, 4096]], [[1, 1, 4096]], [[1, 1, 4096]], [[1, 1, 4096]], [[1, 32, 1, 1]]]}}, "unknown_analysis": {"operation_count": 0}, "operator_timeline": [[0.639885663986206, "torch.nn.functional.embedding", "prefill", [[1, 512], [128256, 4096]]], [0.7913589477539062, "torch.nn.functional.linear", "prefill", [[1, 512, 4096], [4096, 4096]]], [0.7924182415008545, "torch.nn.functional.linear", "prefill", [[1, 512, 4096], [4096, 4096]]], [0.7927489280700684, "torch.nn.functional.linear", "prefill", [[1, 512, 4096], [4096, 4096]]], [0.793816089630127, "torch.matmul", "prefill", [[1, 32, 512, 128], [1, 32, 128, 512]]], [0.8123855590820312, "torch.matmul", "prefill", [[1, 32, 512, 512], [1, 32, 512, 128]]], [0.8209800720214844, "torch.nn.functional.linear", "prefill", [[1, 512, 4096], [4096, 4096]]], [0.8226959705352783, "torch.nn.functional.linear", "prefill", [[1, 512, 4096], [14336, 4096]]], [0.8234453201293945, "torch.nn.functional.linear", "prefill", [[1, 512, 4096], [14336, 4096]]], [0.8392655849456787, "torch.nn.functional.linear", "prefill", [[1, 512, 14336], [4096, 14336]]], [0.8787829875946045, "torch.nn.functional.embedding", "decode", [[1, 1], [128256, 4096]]], [0.8793380260467529, "torch.nn.functional.linear", "decode", [[1, 1, 4096], [4096, 4096]]], [0.8796191215515137, "torch.nn.functional.linear", "decode", [[1, 1, 4096], [4096, 4096]]], [0.8818025588989258, "torch.nn.functional.linear", "decode", [[1, 1, 4096], [4096, 4096]]], [0.9002535343170166, "torch.matmul", "decode", [[1, 32, 1, 128], [1, 32, 128, 1]]], [0.9056224822998047, "torch.matmul", "decode", [[1, 32, 1, 1], [1, 32, 1, 128]]], [0.9140400886535645, "torch.nn.functional.linear", "decode", [[1, 1, 4096], [14336, 4096]]], [0.9143788814544678, "torch.nn.functional.linear", "decode", [[1, 1, 4096], [14336, 4096]]], [0.914750337600708, "torch.nn.functional.linear", "decode", [[1, 1, 14336], [4096, 14336]]], [0.9264369010925293, "torch.bmm", "prefill", [[1, 512, 1024], [1, 1024, 2048]]], [1.9534451961517334, "torch.bmm", "prefill", [[1, 512, 1024], [1, 1024, 2048]]], [1.9545924663543701, "torch.bmm", "prefill", [[1, 512, 1024], [1, 1024, 2048]]], [1.9554862976074219, "torch.bmm", "prefill", [[1, 512, 1024], [1, 1024, 2048]]], [1.956352949142456, "torch.bmm", "prefill", [[1, 512, 1024], [1, 1024, 2048]]]], "sample_operations": [{"name": "torch.matmul", "input_shapes": [[1, 32, 512, 128], [1, 32, 128, 512]], "output_shapes": [[1, 32, 512, 512]], "dtypes": ["torch.float16", "torch.float16"], "device": "cuda:0", "timestamp": 0.793816089630127, "phase": "prefill", "memory_before": 1.1114540100097656, "memory_after": 1.1114540100097656, "flops": 67108864.0}, {"name": "torch.matmul", "input_shapes": [[1, 32, 512, 512], [1, 32, 512, 128]], "output_shapes": [[1, 32, 512, 128]], "dtypes": ["torch.float16", "torch.float16"], "device": "cuda:0", "timestamp": 0.8123855590820312, "phase": "prefill", "memory_before": 1.1309852600097656, "memory_after": 1.1309852600097656, "flops": 67108864.0}, {"name": "torch.nn.functional.linear", "input_shapes": [[1, 512, 4096], [4096, 4096]], "output_shapes": [[1, 512, 4096]], "dtypes": ["torch.float16", "torch.float16"], "device": "cuda:0", "timestamp": 0.8209800720214844, "phase": "prefill", "memory_before": 1.1661415100097656, "memory_after": 1.1661415100097656, "flops": 17179869184.0}, {"name": "torch.nn.functional.linear", "input_shapes": [[1, 512, 4096], [14336, 4096]], "output_shapes": [[1, 512, 14336]], "dtypes": ["torch.float16", "torch.float16"], "device": "cuda:0", "timestamp": 0.8226959705352783, "phase": "prefill", "memory_before": 1.5040321350097656, "memory_after": 1.5040321350097656, "flops": 17179869184.0}, {"name": "torch.nn.functional.linear", "input_shapes": [[1, 512, 4096], [14336, 4096]], "output_shapes": [[1, 512, 14336]], "dtypes": ["torch.float16", "torch.float16"], "device": "cuda:0", "timestamp": 0.8234453201293945, "phase": "prefill", "memory_before": 1.5177040100097656, "memory_after": 1.5177040100097656, "flops": 17179869184.0}, {"name": "torch.nn.functional.linear", "input_shapes": [[1, 512, 14336], [4096, 14336]], "output_shapes": [[1, 512, 4096]], "dtypes": ["torch.float16", "torch.float16"], "device": "cuda:0", "timestamp": 0.8392655849456787, "phase": "prefill", "memory_before": 1.5352821350097656, "memory_after": 1.5352821350097656, "flops": 210453397504.0}, {"name": "torch.nn.functional.embedding", "input_shapes": [[1, 1], [128256, 4096]], "output_shapes": [[1, 1, 4096]], "dtypes": ["torch.int64", "torch.float16"], "device": "cuda:0", "timestamp": 0.8787829875946045, "phase": "decode", "memory_before": 1.535290241241455, "memory_after": 1.535290241241455, "flops": 100.0}, {"name": "torch.nn.functional.linear", "input_shapes": [[1, 1, 4096], [4096, 4096]], "output_shapes": [[1, 1, 4096]], "dtypes": ["torch.float16", "torch.float16"], "device": "cuda:0", "timestamp": 0.8793380260467529, "phase": "decode", "memory_before": 1.5352978706359863, "memory_after": 1.5352978706359863, "flops": 33554432.0}, {"name": "torch.nn.functional.linear", "input_shapes": [[1, 1, 4096], [4096, 4096]], "output_shapes": [[1, 1, 4096]], "dtypes": ["torch.float16", "torch.float16"], "device": "cuda:0", "timestamp": 0.8796191215515137, "phase": "decode", "memory_before": 1.5353055000305176, "memory_after": 1.5353055000305176, "flops": 33554432.0}, {"name": "torch.nn.functional.linear", "input_shapes": [[1, 1, 4096], [4096, 4096]], "output_shapes": [[1, 1, 4096]], "dtypes": ["torch.float16", "torch.float16"], "device": "cuda:0", "timestamp": 0.8818025588989258, "phase": "decode", "memory_before": 1.5353131294250488, "memory_after": 1.5353131294250488, "flops": 33554432.0}, {"name": "torch.matmul", "input_shapes": [[1, 32, 1, 128], [1, 32, 128, 1]], "output_shapes": [[1, 32, 1, 1]], "dtypes": ["torch.float16", "torch.float16"], "device": "cuda:0", "timestamp": 0.9002535343170166, "phase": "decode", "memory_before": 1.535313606262207, "memory_after": 1.535313606262207, "flops": 256.0}, {"name": "torch.matmul", "input_shapes": [[1, 32, 1, 1], [1, 32, 1, 128]], "output_shapes": [[1, 32, 1, 128]], "dtypes": ["torch.float16", "torch.float16"], "device": "cuda:0", "timestamp": 0.9056224822998047, "phase": "decode", "memory_before": 1.5353212356567383, "memory_after": 1.5353212356567383, "flops": 256.0}, {"name": "torch.nn.functional.linear", "input_shapes": [[1, 1, 4096], [14336, 4096]], "output_shapes": [[1, 1, 14336]], "dtypes": ["torch.float16", "torch.float16"], "device": "cuda:0", "timestamp": 0.9140400886535645, "phase": "decode", "memory_before": 1.5353479385375977, "memory_after": 1.5353479385375977, "flops": 33554432.0}, {"name": "torch.nn.functional.linear", "input_shapes": [[1, 1, 4096], [14336, 4096]], "output_shapes": [[1, 1, 14336]], "dtypes": ["torch.float16", "torch.float16"], "device": "cuda:0", "timestamp": 0.9143788814544678, "phase": "decode", "memory_before": 1.535374641418457, "memory_after": 1.535374641418457, "flops": 33554432.0}, {"name": "torch.nn.functional.linear", "input_shapes": [[1, 1, 14336], [4096, 14336]], "output_shapes": [[1, 1, 4096]], "dtypes": ["torch.float16", "torch.float16"], "device": "cuda:0", "timestamp": 0.914750337600708, "phase": "decode", "memory_before": 1.5354089736938477, "memory_after": 1.5354089736938477, "flops": 411041792.0}, {"name": "torch.bmm", "input_shapes": [[1, 512, 1024], [1, 1024, 2048]], "output_shapes": [[1, 512, 2048]], "dtypes": ["torch.float16", "torch.float16"], "device": "cuda:0", "timestamp": 0.9264369010925293, "phase": "prefill", "memory_before": 1.5422449111938477, "memory_after": 1.5422449111938477, "flops": 2147483648.0}, {"name": "torch.bmm", "input_shapes": [[1, 512, 1024], [1, 1024, 2048]], "output_shapes": [[1, 512, 2048]], "dtypes": ["torch.float16", "torch.float16"], "device": "cuda:0", "timestamp": 1.9534451961517334, "phase": "prefill", "memory_before": 1.5441980361938477, "memory_after": 1.5441980361938477, "flops": 2147483648.0}, {"name": "torch.bmm", "input_shapes": [[1, 512, 1024], [1, 1024, 2048]], "output_shapes": [[1, 512, 2048]], "dtypes": ["torch.float16", "torch.float16"], "device": "cuda:0", "timestamp": 1.9545924663543701, "phase": "prefill", "memory_before": 1.5441980361938477, "memory_after": 1.5441980361938477, "flops": 2147483648.0}, {"name": "torch.bmm", "input_shapes": [[1, 512, 1024], [1, 1024, 2048]], "output_shapes": [[1, 512, 2048]], "dtypes": ["torch.float16", "torch.float16"], "device": "cuda:0", "timestamp": 1.9554862976074219, "phase": "prefill", "memory_before": 1.5441980361938477, "memory_after": 1.5441980361938477, "flops": 2147483648.0}, {"name": "torch.bmm", "input_shapes": [[1, 512, 1024], [1, 1024, 2048]], "output_shapes": [[1, 512, 2048]], "dtypes": ["torch.float16", "torch.float16"], "device": "cuda:0", "timestamp": 1.956352949142456, "phase": "prefill", "memory_before": 1.5441980361938477, "memory_after": 1.5441980361938477, "flops": 2147483648.0}]}