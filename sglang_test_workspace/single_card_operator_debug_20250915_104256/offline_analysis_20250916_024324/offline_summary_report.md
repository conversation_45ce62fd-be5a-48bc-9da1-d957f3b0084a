# SGLang 离线算子分析报告

## 执行摘要
- 总算子调用次数: 24
- Prefill阶段算子: 15
- Decode阶段算子: 9
- 未分类算子: 0
- 执行时间: 2.97秒

## 算子类型统计
- Embedding操作: 2
- Linear操作: 13
- MatMul操作: 4
- BMM操作: 5
- INT8 MM操作: 0

## Prefill阶段分析

- 算子调用次数: 15
- 主要算子类型: {'torch.nn.functional.linear': 7, 'torch.bmm': 5, 'torch.matmul': 2}
- 总FLOPs: 3.24e+11
- 平均内存使用: 1.33 GB


## Decode阶段分析

- 算子调用次数: 9
- 主要算子类型: {'torch.nn.functional.linear': 6, 'torch.matmul': 2, 'torch.nn.functional.embedding': 1}
- 总FLOPs: 5.79e+08
- 平均内存使用: 1.54 GB


## 技术发现
1. 主要计算算子类型及调用次数
2. 不同阶段的算子分布差异
3. 内存使用模式
4. 计算复杂度估算

## 详细算子时间线
- 0.90s: torch.matmul (decode) [[1, 32, 1, 128], [1, 32, 128, 1]]
- 0.91s: torch.matmul (decode) [[1, 32, 1, 1], [1, 32, 1, 128]]
- 0.91s: torch.nn.functional.linear (decode) [[1, 1, 4096], [14336, 4096]]
- 0.91s: torch.nn.functional.linear (decode) [[1, 1, 4096], [14336, 4096]]
- 0.91s: torch.nn.functional.linear (decode) [[1, 1, 14336], [4096, 14336]]
- 0.93s: torch.bmm (prefill) [[1, 512, 1024], [1, 1024, 2048]]
- 1.95s: torch.bmm (prefill) [[1, 512, 1024], [1, 1024, 2048]]
- 1.95s: torch.bmm (prefill) [[1, 512, 1024], [1, 1024, 2048]]
- 1.96s: torch.bmm (prefill) [[1, 512, 1024], [1, 1024, 2048]]
- 1.96s: torch.bmm (prefill) [[1, 512, 1024], [1, 1024, 2048]]
