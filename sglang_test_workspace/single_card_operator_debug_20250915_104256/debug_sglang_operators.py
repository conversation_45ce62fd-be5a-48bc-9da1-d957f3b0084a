#!/usr/bin/env python3
"""
SGLang算子调试工具 - 深度分析模型加载和推理过程中的所有算子与shape信息
基于offline_sglang_generate.py进行增强，添加详细的算子监控和shape记录功能
"""

import os
import sys
import traceback
import json
import torch
import numpy as np
from typing import Dict, List, Tuple, Any
from collections import defaultdict
import time
from contextlib import contextmanager

# 必须在导入 sglang 前设置分布式/通信相关环境
os.environ.setdefault("SGL_DISABLE_TP_MEMORY_INBALANCE_CHECK", "1")
os.environ.setdefault("UCX_VFS_ENABLE", "n")
os.environ.setdefault("NCCL_P2P_DISABLE", "1")
os.environ.setdefault("NCCL_IB_DISABLE", "1")
os.environ.setdefault("SGLANG_IS_FLASHINFER_AVAILABLE", "false")

# 算子监控器
class OperatorMonitor:
    def __init__(self):
        self.operator_logs = []
        self.shape_records = {}
        self.layer_info = {}
        self.start_time = time.time()
        
    def log_operator(self, op_name: str, input_shapes: List[Tuple], 
                    output_shapes: List[Tuple], layer_id: int = None, 
                    extra_info: Dict = None):
        """记录算子调用信息"""
        record = {
            'timestamp': time.time() - self.start_time,
            'operator': op_name,
            'input_shapes': input_shapes,
            'output_shapes': output_shapes,
            'layer_id': layer_id,
            'extra_info': extra_info or {}
        }
        self.operator_logs.append(record)
        print(f"[OP] {op_name} | Layer: {layer_id} | In: {input_shapes} | Out: {output_shapes}")
        
    def log_layer_info(self, layer_name: str, layer_id: int, config_info: Dict):
        """记录层级信息"""
        self.layer_info[f"{layer_name}_{layer_id}"] = config_info
        
    def save_report(self, filename: str):
        """保存详细报告"""
        report = {
            'summary': {
                'total_operators': len(self.operator_logs),
                'execution_time': time.time() - self.start_time,
                'layer_count': len(self.layer_info)
            },
            'operator_logs': self.operator_logs,
            'layer_info': self.layer_info,
            'shape_analysis': self.analyze_shapes()
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
            
    def analyze_shapes(self) -> Dict:
        """分析算子shape模式"""
        shape_stats = defaultdict(list)
        for log in self.operator_logs:
            op_name = log['operator']
            for i, shape in enumerate(log['input_shapes']):
                shape_stats[f"{op_name}_input_{i}"].append(shape)
            for i, shape in enumerate(log['output_shapes']):
                shape_stats[f"{op_name}_output_{i}"].append(shape)
        return dict(shape_stats)

# 全局监控器
monitor = OperatorMonitor()

# 修改torch.nn.functional和torch的关键函数以记录算子调用
original_linear = torch.nn.functional.linear
original_matmul = torch.matmul
original_bmm = torch.bmm
original_addmm = torch.addmm

def monitored_linear(input, weight, bias=None):
    input_shape = tuple(input.shape) if hasattr(input, 'shape') else None
    weight_shape = tuple(weight.shape) if hasattr(weight, 'shape') else None
    result = original_linear(input, weight, bias)
    output_shape = tuple(result.shape) if hasattr(result, 'shape') else None
    
    monitor.log_operator(
        'linear',
        [input_shape, weight_shape],
        [output_shape],
        extra_info={'has_bias': bias is not None}
    )
    return result

def monitored_matmul(input, other):
    input_shape = tuple(input.shape) if hasattr(input, 'shape') else None
    other_shape = tuple(other.shape) if hasattr(other, 'shape') else None
    result = original_matmul(input, other)
    output_shape = tuple(result.shape) if hasattr(result, 'shape') else None
    
    monitor.log_operator(
        'matmul',
        [input_shape, other_shape],
        [output_shape]
    )
    return result

def monitored_bmm(input, mat2):
    input_shape = tuple(input.shape) if hasattr(input, 'shape') else None
    mat2_shape = tuple(mat2.shape) if hasattr(mat2, 'shape') else None
    result = original_bmm(input, mat2)
    output_shape = tuple(result.shape) if hasattr(result, 'shape') else None
    
    monitor.log_operator(
        'bmm',
        [input_shape, mat2_shape],
        [output_shape]
    )
    return result

def monitored_addmm(bias, input, mat2, *, beta=1, alpha=1):
    bias_shape = tuple(bias.shape) if hasattr(bias, 'shape') else None
    input_shape = tuple(input.shape) if hasattr(input, 'shape') else None
    mat2_shape = tuple(mat2.shape) if hasattr(mat2, 'shape') else None
    result = original_addmm(bias, input, mat2, beta=beta, alpha=alpha)
    output_shape = tuple(result.shape) if hasattr(result, 'shape') else None
    
    monitor.log_operator(
        'addmm',
        [bias_shape, input_shape, mat2_shape],
        [output_shape],
        extra_info={'beta': beta, 'alpha': alpha}
    )
    return result

# 应用监控补丁
torch.nn.functional.linear = monitored_linear
torch.matmul = monitored_matmul
torch.bmm = monitored_bmm
torch.addmm = monitored_addmm

import sglang as sgl

MODEL_PATH = os.environ.get("MODEL_PATH", "/home/<USER>/deepseek-int8")

def analyze_model_structure(model):
    """分析模型结构"""
    print("=== 模型结构分析 ===")
    total_params = 0
    
    for name, module in model.named_modules():
        if hasattr(module, 'weight') and module.weight is not None:
            weight_shape = tuple(module.weight.shape)
            param_count = module.weight.numel()
            total_params += param_count
            
            print(f"模块: {name}")
            print(f"  类型: {type(module).__name__}")
            print(f"  权重形状: {weight_shape}")
            print(f"  参数数量: {param_count:,}")
            
            # 记录关键层信息
            if any(key in name for key in ['qkv_proj', 'o_proj', 'gate_up_proj', 'down_proj']):
                layer_id = None
                if 'layers.' in name:
                    try:
                        layer_id = int(name.split('layers.')[1].split('.')[0])
                    except:
                        pass
                        
                monitor.log_layer_info(
                    type(module).__name__, 
                    layer_id or 0,
                    {
                        'name': name,
                        'weight_shape': weight_shape,
                        'param_count': param_count
                    }
                )
    
    print(f"\n总参数数量: {total_params:,}")
    return total_params

def main():
    llm = None
    try:
        print("[DEBUG] 开始初始化SGLang引擎...")
        monitor.log_operator('engine_init', [], [], extra_info={'model_path': MODEL_PATH})
        
        llm = sgl.Engine(
            model_path=MODEL_PATH,
            tp_size=4,
            quantization="w8a8_int8",
            trust_remote_code=True,
            dist_init_addr="127.0.0.1:8001",
            disable_cuda_graph=True,
            log_level="info",
        )
        
        print("[DEBUG] 引擎初始化完成，开始分析模型结构...")
        # 分析模型结构
        if hasattr(llm, 'model_runner') and hasattr(llm.model_runner, 'model'):
            analyze_model_structure(llm.model_runner.model)
        
        print("[DEBUG] 开始推理测试...")
        prompt = "用一句话介绍你自己。"
        sampling_params = {"max_new_tokens": 32, "temperature": 0.7}
        
        monitor.log_operator('inference_start', [], [], extra_info={
            'prompt': prompt,
            'sampling_params': sampling_params
        })
        
        print(f"[DEBUG] 输入prompt: {prompt}")
        out = llm.generate(prompt=prompt, sampling_params=sampling_params)
        
        monitor.log_operator('inference_end', [], [], extra_info={
            'output_length': len(str(out.get("text", "")))
        })
        
        print("推理完成！")
        print("---")
        print(f"输出: {out.get('text', out)}")
        
        # 保存详细报告
        report_file = f"operator_analysis_report_{int(time.time())}.json"
        monitor.save_report(report_file)
        print(f"\n详细报告已保存至: {report_file}")
        
        # 打印算子统计
        print("\n=== 算子统计 ===")
        op_counts = defaultdict(int)
        for log in monitor.operator_logs:
            op_counts[log['operator']] += 1
            
        for op_name, count in sorted(op_counts.items()):
            print(f"{op_name}: {count} 次调用")
            
    except Exception:
        print("[ERROR] 执行过程中出现错误:")
        print(traceback.format_exc())
        sys.exit(1)
    finally:
        if llm is not None:
            try:
                llm.shutdown()
            except Exception:
                pass

if __name__ == "__main__":
    main()
