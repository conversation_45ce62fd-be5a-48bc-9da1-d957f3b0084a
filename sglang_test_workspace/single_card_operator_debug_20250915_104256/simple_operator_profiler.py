#!/usr/bin/env python3
"""
SGLang DeepSeek 简化算子分析 - 专注prefill/decode两阶段
使用保守的内存配置
"""

import os
import sys
import torch
import time
import json
import traceback
from collections import defaultdict

# 设置环境变量
os.environ["SGL_DISABLE_TP_MEMORY_INBALANCE_CHECK"] = "1"
os.environ["CUDA_LAUNCH_BLOCKING"] = "1"
os.environ["CUDA_VISIBLE_DEVICES"] = "0,1"  # 只使用前两张GPU

class SimpleOperatorProfiler:
    def __init__(self):
        self.operation_logs = []
        self.phase_data = {
            'prefill': defaultdict(list),
            'decode': defaultdict(list)
        }
        self.current_phase = 'unknown'
        self.call_count = 0
        
    def detect_phase(self, shapes):
        """简单的阶段检测"""
        if not shapes:
            return self.current_phase
            
        first_shape = shapes[0]
        if len(first_shape) >= 2:
            batch_size = first_shape[0]
            
            if batch_size > 1:
                new_phase = 'prefill'
            else:
                new_phase = 'decode'
                
            if new_phase != self.current_phase:
                print(f"[PHASE_CHANGE] {self.current_phase} -> {new_phase}")
                self.current_phase = new_phase
                
        return self.current_phase
    
    def log_operation(self, func_name, input_shapes, output_shapes, duration):
        """记录操作"""
        self.call_count += 1
        
        phase = self.detect_phase(input_shapes)
        
        record = {
            'call_id': self.call_count,
            'function': func_name,
            'phase': phase,
            'input_shapes': input_shapes,
            'output_shapes': output_shapes,
            'duration_ms': duration,
            'timestamp': time.time()
        }
        
        self.operation_logs.append(record)
        self.phase_data[phase][func_name].append(record)
        
        # 打印关键信息
        if input_shapes and output_shapes:
            print(f"[{phase.upper()}] {func_name}: {input_shapes[0]} -> {output_shapes[0]} | {duration:.3f}ms")
    
    def save_results(self, filename):
        """保存结果"""
        results = {
            'summary': {
                'total_calls': self.call_count,
                'prefill_calls': sum(len(ops) for ops in self.phase_data['prefill'].values()),
                'decode_calls': sum(len(ops) for ops in self.phase_data['decode'].values())
            },
            'phase_data': dict(self.phase_data),
            'operation_logs': self.operation_logs
        }
        
        with open(filename, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        print(f"[INFO] 结果保存到: {filename}")
        return results

# 全局profiler实例
profiler = SimpleOperatorProfiler()

# 钩子函数
def hook_linear(original_func):
    def wrapper(*args, **kwargs):
        start_time = time.perf_counter()
        
        input_shapes = []
        for arg in args:
            if isinstance(arg, torch.Tensor):
                input_shapes.append(tuple(arg.shape))
        
        result = original_func(*args, **kwargs)
        
        end_time = time.perf_counter()
        duration = (end_time - start_time) * 1000
        
        output_shapes = []
        if isinstance(result, torch.Tensor):
            output_shapes.append(tuple(result.shape))
        
        profiler.log_operation('torch.nn.functional.linear', input_shapes, output_shapes, duration)
        
        return result
    return wrapper

def hook_matmul(original_func):
    def wrapper(*args, **kwargs):
        start_time = time.perf_counter()
        
        input_shapes = []
        for arg in args:
            if isinstance(arg, torch.Tensor):
                input_shapes.append(tuple(arg.shape))
        
        result = original_func(*args, **kwargs)
        
        end_time = time.perf_counter()
        duration = (end_time - start_time) * 1000
        
        output_shapes = []
        if isinstance(result, torch.Tensor):
            output_shapes.append(tuple(result.shape))
        
        profiler.log_operation('torch.matmul', input_shapes, output_shapes, duration)
        
        return result
    return wrapper

def hook_bmm(original_func):
    def wrapper(*args, **kwargs):
        start_time = time.perf_counter()
        
        input_shapes = []
        for arg in args:
            if isinstance(arg, torch.Tensor):
                input_shapes.append(tuple(arg.shape))
        
        result = original_func(*args, **kwargs)
        
        end_time = time.perf_counter()
        duration = (end_time - start_time) * 1000
        
        output_shapes = []
        if isinstance(result, torch.Tensor):
            output_shapes.append(tuple(result.shape))
        
        profiler.log_operation('torch.bmm', input_shapes, output_shapes, duration)
        
        return result
    return wrapper


def install_hooks():
    """安装监控钩子"""
    print("[INFO] 安装算子监控钩子...")
    
    # 备份原函数
    global original_linear, original_matmul, original_bmm
    original_linear = torch.nn.functional.linear
    original_matmul = torch.matmul
    original_bmm = torch.bmm
    
    # 安装钩子
    torch.nn.functional.linear = hook_linear(original_linear)
    torch.matmul = hook_matmul(original_matmul)
    torch.bmm = hook_bmm(original_bmm)
    
    print("[INFO] 钩子安装完成")

def remove_hooks():
    """移除监控钩子"""
    print("[INFO] 移除监控钩子...")
    
    torch.nn.functional.linear = original_linear
    torch.matmul = original_matmul
    torch.bmm = original_bmm
    
    print("[INFO] 钩子移除完成")


def main():
    print("="*80)
    print("SGLang DeepSeek 简化算子分析")
    print("="*80)
    
    try:
        # 安装钩子
        install_hooks()
        
        print("\n[INFO] 启动SGLang引擎...")
        
        sys.path.append('/workspace/sglang_test/lib/python3.10/site-packages')
        import sglang as sgl
        
        # 使用前两张GPU的配置
        engine = sgl.Engine(
            model_path="/home/<USER>/deepseek-int8",
            tokenizer_path="/home/<USER>/deepseek-int8",
            tp_size=2,  # 使用2张GPU
            trust_remote_code=True,
            quantization="fp8",
            mem_fraction_static=0.6  # 适当提高内存使用
        )
        
        print("[INFO] 引擎启动成功，开始测试...")
        
        # 简单测试用例
        test_cases = [
            "你好",
            "解释AI",
            "深度学习的基本原理是什么"
        ]
        
        for i, prompt in enumerate(test_cases):
            print(f"\n[TEST {i+1}] 输入: {prompt}")
            
            profiler.current_phase = 'unknown'
            
            response = engine.generate(
                prompt,
                max_new_tokens=30,
                temperature=0.1
            )
            
            print(f"输出: {response['text'][:50]}...")
            
        print("\n[INFO] 测试完成")
        
    except Exception as e:
        print(f"[ERROR] 运行错误: {str(e)}")
        traceback.print_exc()
        
    finally:
        # 清理
        remove_hooks()
        
        # 保存结果
        timestamp = int(time.time())
        filename = f"simple_operator_analysis_{timestamp}.json"
        results = profiler.save_results(filename)
        
        # 打印摘要
        print("\n" + "="*80)
        print("算子分析摘要")
        print("="*80)
        
        summary = results['summary']
        print(f"总调用: {summary['total_calls']}")
        print(f"Prefill调用: {summary['prefill_calls']}")
        print(f"Decode调用: {summary['decode_calls']}")
        
        for phase in ['prefill', 'decode']:
            if phase in results['phase_data'] and results['phase_data'][phase]:
                print(f"\n{phase.capitalize()}阶段主要算子:")
                for op_name, calls in results['phase_data'][phase].items():
                    total_time = sum(call['duration_ms'] for call in calls)
                    print(f"  {op_name}: {len(calls)}次调用, {total_time:.2f}ms")


if __name__ == "__main__":
    main()
