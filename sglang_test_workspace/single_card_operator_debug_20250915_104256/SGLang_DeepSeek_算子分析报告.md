# SGLang DeepSeek W8A8量化算子分析报告

## 执行环境
- **模型**: DeepSeek V3 INT8量化版本
- **量化方案**: W8A8 INT8 (权重8bit + 激活8bit)
- **框架**: SGLang
- **并行配置**: TP=4 (Tensor Parallel)
- **后端**: Triton attention backend

## 关键发现

### 1. 主要算子类型

基于运行输出，SGLang在DeepSeek W8A8推理中使用了以下核心算子：

#### 1.1 量化线性算子
- **算子名称**: `int8_scaled_mm`
- **功能**: INT8量化矩阵乘法，支持动态缩放
- **输入格式**: 
  - 激活张量 (INT8, 动态量化)
  - 权重张量 (INT8, 静态量化)
  - 激活缩放因子 (FP32)
  - 权重缩放因子 (FP32)
- **输出格式**: BF16

#### 1.2 非量化线性算子
- **算子名称**: `linear` (torch.nn.functional.linear)
- **功能**: 标准FP16/BF16线性变换
- **用途**: 
  - Embedding层
  - LM head层
  - 部分小规模线性层

#### 1.3 注意力机制算子
- **算子名称**: `bmm` (batch matrix multiply)
- **功能**: 批量矩阵乘法，用于注意力计算
- **模式**: QK^T → Attention weights, Attention weights × V → Output

#### 1.4 通用矩阵算子
- **算子名称**: `matmul`
- **功能**: 通用矩阵乘法

### 2. 观察到的算子Shape模式

#### 2.1 注意力相关Shape

基于观察到的实际输出：

```python
# QKV投影相关
int8_scaled_mm: (7, 7168) × (7168, 2112) → (7, 2112)  # Q投影
int8_scaled_mm: (1, 7168) × (7168, 2112) → (1, 2112)  # decode阶段

int8_scaled_mm: (7, 1536) × (1536, 6144) → (7, 6144)  # K/V投影 
int8_scaled_mm: (1, 1536) × (1536, 6144) → (1, 6144)  # decode阶段

int8_scaled_mm: (7, 512) × (512, 8192) → (7, 8192)    # V投影
int8_scaled_mm: (1, 512) × (512, 8192) → (1, 8192)    # decode阶段

# 注意力输出投影
int8_scaled_mm: (7, 4096) × (4096, 7168) → (7, 7168)  # O投影
int8_scaled_mm: (1, 4096) × (4096, 7168) → (1, 7168)  # decode阶段

# 注意力计算BMM
bmm: (32, 1, 128) × (32, 128, 512) → (32, 1, 512)     # QK^T
bmm: (32, 1, 512) × (32, 512, 128) → (32, 1, 128)     # Attn×V
```

#### 2.2 MLP/FFN相关Shape

```python
# Embedding投影
linear: (7, 14336) × (7168, 14336) → (7, 7168)        # prefill
linear: (1, 14336) × (7168, 14336) → (1, 7168)        # decode

# MoE门控
linear: (7, 7168) × (256, 7168) → (7, 256)            # 专家选择
linear: (1, 7168) × (256, 7168) → (1, 256)            # decode阶段

# LM Head
matmul: (1, 7168) × (7168, 32320) → (1, 32320)        # 词汇表投影
```

### 3. Shape模式分析

#### 3.1 序列长度变化
- **Prefill阶段**: batch_size=7 (输入序列长度)
- **Decode阶段**: batch_size=1 (单token生成)

#### 3.2 隐藏维度
- **主隐藏维度**: 7168
- **中间FFN维度**: 18432 (推测，基于14336 embedding维度)

#### 3.3 注意力头配置
- **总注意力头数**: 推测112个 (7168 ÷ 64)
- **TP分割后每卡头数**: 28个 (112 ÷ 4)
- **KV头数**: 更少，使用GQA (Grouped Query Attention)

#### 3.4 MoE配置
- **专家数量**: 256个专家
- **激活专家数**: 根据门控选择

### 4. 量化策略分析

#### 4.1 W8A8量化特点
- **权重量化**: 静态INT8，channel-wise量化
- **激活量化**: 动态INT8，per-token量化  
- **缩放因子**: FP32精度，保证数值稳定性
- **输出精度**: BF16，保持训练精度

#### 4.2 算子融合
- 量化+矩阵乘法+反量化融合为单个kernel
- 避免中间结果的内存读写
- 提升计算效率

### 5. 与期望Shape的对比

期望的算子shape (来自文档):
```python
'attn_wqa': (1, 128, 1536, 7168),
'attn_wqb': (1, 128, 24576, 1536), 
'attn_wkv_a': (1, 128, 576, 7168),
'attn_wkv_b': (1, 128, 32768, 512),
'dense_up': (1, 128, 18432, 7168),
'dense_gate': (1, 128, 18432, 7168),
'dense_down': (1, 128, 7168, 18432)
```

实际观察到的shape模式与期望不完全一致，主要差异：
1. 实际的batch维度是序列长度 (7 for prefill, 1 for decode)
2. 隐藏维度7168与期望一致
3. 中间维度存在差异，可能由于TP分割和模型配置差异

### 6. 性能优化要点

#### 6.1 算子选择
- 关键算子使用专门的INT8 kernel (`int8_scaled_mm`)
- 非关键路径保持FP16/BF16精度
- 注意力使用优化的BMM实现

#### 6.2 内存优化
- 动态量化减少激活内存占用
- 静态权重量化减少模型大小
- KV缓存使用优化的内存布局

#### 6.3 计算优化
- Tensor并行4卡分布
- MoE专家并行计算
- 注意力机制使用Triton后端优化

## 总结

SGLang在DeepSeek V3的W8A8量化推理中，采用了以下关键技术：

1. **混合精度策略**: 核心计算使用INT8，关键路径保持BF16
2. **动态量化**: 激活使用per-token动态量化，适应不同输入分布
3. **算子融合**: 量化+计算+反量化融合，减少内存带宽
4. **并行优化**: TP4分布，充分利用多卡资源
5. **后端选择**: Triton注意力后端，针对GPU优化

这种设计在保持模型精度的同时，显著降低了内存占用和计算开销，实现了高效的大模型推理。

## 建议

1. **进一步优化**: 可考虑使用更高效的attention实现如FlashAttention
2. **精度监控**: 建议添加精度监控，确保量化不影响模型效果
3. **性能测试**: 建议进行详细的性能基准测试，对比不同量化方案
4. **内存分析**: 建议分析内存使用模式，进一步优化KV缓存策略
