#!/usr/bin/env python3
"""
SGLang 算子深度监控脚本
深入分析DeepSeek模型在W8A8量化下的所有算子调用，包括shape变化和计算流程
"""

import os
import sys
import traceback
import json
import torch
import torch.nn.functional as F
import numpy as np
from typing import Dict, List, Tuple, Any, Optional
from collections import defaultdict
import time
import threading
from functools import wraps

# 必须在导入 sglang 前设置分布式/通信相关环境
os.environ.setdefault("SGL_DISABLE_TP_MEMORY_INBALANCE_CHECK", "1")
os.environ.setdefault("UCX_VFS_ENABLE", "n") 
os.environ.setdefault("NCCL_P2P_DISABLE", "1")
os.environ.setdefault("NCCL_IB_DISABLE", "1")
os.environ.setdefault("SGLANG_IS_FLASHINFER_AVAILABLE", "false")

class DetailedOperatorMonitor:
    """详细的算子监控器"""
    
    def __init__(self):
        self.operation_logs = []
        self.layer_info = {}
        self.shape_records = {}
        self.start_time = time.time()
        self.current_layer = None
        self.inference_phase = "model_loading"
        self.lock = threading.Lock()
        
    def set_phase(self, phase: str):
        """设置当前推理阶段"""
        self.inference_phase = phase
        print(f"\n[PHASE] 切换到阶段: {phase}")
        
    def set_current_layer(self, layer_name: str, layer_id: int = None):
        """设置当前层信息"""
        self.current_layer = {
            'name': layer_name,
            'id': layer_id,
            'timestamp': time.time() - self.start_time
        }
        
    def log_operation(self, op_name: str, input_tensors: List[torch.Tensor], 
                     output_tensors: List[torch.Tensor], extra_info: Dict = None):
        """记录算子操作"""
        with self.lock:
            # 提取tensor的shape信息
            input_shapes = []
            input_dtypes = []
            for tensor in input_tensors:
                if hasattr(tensor, 'shape') and hasattr(tensor, 'dtype'):
                    input_shapes.append(tuple(tensor.shape))
                    input_dtypes.append(str(tensor.dtype))
                else:
                    input_shapes.append(None)
                    input_dtypes.append(None)
                    
            output_shapes = []
            output_dtypes = []
            for tensor in output_tensors:
                if hasattr(tensor, 'shape') and hasattr(tensor, 'dtype'):
                    output_shapes.append(tuple(tensor.shape))
                    output_dtypes.append(str(tensor.dtype))
                else:
                    output_shapes.append(None)
                    output_dtypes.append(None)
            
            record = {
                'timestamp': time.time() - self.start_time,
                'phase': self.inference_phase,
                'operation': op_name,
                'current_layer': self.current_layer,
                'input_shapes': input_shapes,
                'input_dtypes': input_dtypes,
                'output_shapes': output_shapes,
                'output_dtypes': output_dtypes,
                'extra_info': extra_info or {}
            }
            
            self.operation_logs.append(record)
            
            # 实时打印关键信息
            layer_info = f"Layer: {self.current_layer['name'] if self.current_layer else 'Unknown'}"
            if self.current_layer and self.current_layer.get('id') is not None:
                layer_info += f"[{self.current_layer['id']}]"
                
            print(f"[{self.inference_phase}] [{op_name}] {layer_info}")
            print(f"  输入: {input_shapes} ({input_dtypes})")
            print(f"  输出: {output_shapes} ({output_dtypes})")
            if extra_info:
                print(f"  额外信息: {extra_info}")
                
    def analyze_and_save_report(self, filename: str):
        """分析并保存详细报告"""
        # 按阶段分组统计
        phase_stats = defaultdict(lambda: {'count': 0, 'operations': defaultdict(int)})
        
        for log in self.operation_logs:
            phase = log['phase']
            op_name = log['operation']
            phase_stats[phase]['count'] += 1
            phase_stats[phase]['operations'][op_name] += 1
            
        # 分析算子的shape模式
        shape_analysis = self._analyze_shapes()
        
        # 生成报告
        report = {
            'summary': {
                'total_operations': len(self.operation_logs),
                'execution_time': time.time() - self.start_time,
                'phases': dict(phase_stats)
            },
            'detailed_logs': self.operation_logs,
            'shape_analysis': shape_analysis,
            'layer_info': self.layer_info
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
            
        print(f"\n详细报告已保存至: {filename}")
        
        # 打印汇总信息
        print("\n=== 算子执行统计 ===")
        for phase, stats in phase_stats.items():
            print(f"\n阶段: {phase}")
            print(f"  总操作数: {stats['count']}")
            print("  算子分布:")
            for op_name, count in sorted(stats['operations'].items(), key=lambda x: x[1], reverse=True):
                print(f"    {op_name}: {count}次")
                
    def _analyze_shapes(self) -> Dict:
        """分析算子的shape模式"""
        shape_patterns = defaultdict(list)
        
        for log in self.operation_logs:
            op_name = log['operation']
            input_shapes = log['input_shapes']
            output_shapes = log['output_shapes']
            
            # 记录输入输出shape的模式
            pattern_key = f"{op_name}_pattern"
            pattern = {
                'input_shapes': input_shapes,
                'output_shapes': output_shapes,
                'phase': log['phase']
            }
            shape_patterns[pattern_key].append(pattern)
            
        return dict(shape_patterns)

# 全局监控器
monitor = DetailedOperatorMonitor()

# 保存原始函数
original_functions = {}

def monitor_function(func_name: str, module, func_path: str):
    """通用函数监控装饰器"""
    original_func = getattr(module, func_name)
    original_functions[func_path] = original_func
    
    @wraps(original_func)
    def monitored_func(*args, **kwargs):
        # 提取tensor参数
        input_tensors = []
        for arg in args:
            if isinstance(arg, torch.Tensor):
                input_tensors.append(arg)
        for value in kwargs.values():
            if isinstance(value, torch.Tensor):
                input_tensors.append(value)
                
        # 调用原始函数
        result = original_func(*args, **kwargs)
        
        # 提取输出tensor
        output_tensors = []
        if isinstance(result, torch.Tensor):
            output_tensors.append(result)
        elif isinstance(result, (tuple, list)):
            for item in result:
                if isinstance(item, torch.Tensor):
                    output_tensors.append(item)
                    
        # 记录操作
        extra_info = {}
        if kwargs:
            extra_info['kwargs'] = {k: str(v) for k, v in kwargs.items() if not isinstance(v, torch.Tensor)}
            
        monitor.log_operation(func_name, input_tensors, output_tensors, extra_info)
        
        return result
        
    setattr(module, func_name, monitored_func)

# 监控关键的torch函数
monitor_function('linear', torch.nn.functional, 'torch.nn.functional.linear')
monitor_function('matmul', torch, 'torch.matmul')
monitor_function('bmm', torch, 'torch.bmm')
monitor_function('addmm', torch, 'torch.addmm')

# 尝试监控量化相关函数
try:
    import sgl_kernel
    if hasattr(sgl_kernel, 'int8_scaled_mm'):
        monitor_function('int8_scaled_mm', sgl_kernel, 'sgl_kernel.int8_scaled_mm')
except ImportError:
    print("sgl_kernel not available, skipping quantization kernel monitoring")

import sglang as sgl

MODEL_PATH = os.environ.get("MODEL_PATH", "/home/<USER>/deepseek-int8")

def analyze_model_layers(model):
    """分析模型层级结构"""
    print("\n=== 模型层级结构分析 ===")
    
    layer_count = 0
    param_count = 0
    
    for name, module in model.named_modules():
        if 'layers.' in name and len(name.split('.')) == 3:  # 主要的transformer层
            layer_count += 1
            layer_id = int(name.split('.')[1])
            
            print(f"\nLayer {layer_id}: {name}")
            print(f"  类型: {type(module).__name__}")
            
            # 记录层信息
            monitor.layer_info[name] = {
                'layer_id': layer_id,
                'module_type': type(module).__name__,
                'parameters': {}
            }
            
            # 分析子模块
            for subname, submodule in module.named_children():
                if hasattr(submodule, 'weight'):
                    weight_shape = tuple(submodule.weight.shape)
                    param_count += submodule.weight.numel()
                    print(f"    {subname}: {weight_shape}")
                    
                    monitor.layer_info[name]['parameters'][subname] = {
                        'shape': weight_shape,
                        'param_count': submodule.weight.numel()
                    }
    
    print(f"\n总计: {layer_count} 层, {param_count:,} 参数")
    return layer_count, param_count

def main():
    """主函数"""
    llm = None
    try:
        print("[DEBUG] 开始初始化SGLang引擎...")
        monitor.set_phase("engine_initialization")
        
        llm = sgl.Engine(
            model_path=MODEL_PATH,
            tp_size=2,  # 改为使用0,1两张卡
            quantization="w8a8_int8",
            trust_remote_code=True,
            dist_init_addr="127.0.0.1:8001",
            disable_cuda_graph=True,
            log_level="info",
        )
        
        print("[DEBUG] 引擎初始化完成")
        monitor.set_phase("model_analysis")
        
        # 分析模型结构
        if hasattr(llm, 'model_runner') and hasattr(llm.model_runner, 'model'):
            analyze_model_layers(llm.model_runner.model)
        
        print("[DEBUG] 开始推理...")
        monitor.set_phase("inference")
        
        prompt = "请用一句话介绍深度学习。"
        sampling_params = {"max_new_tokens": 16, "temperature": 0.1}
        
        print(f"输入: {prompt}")
        
        # 执行推理
        output = llm.generate(prompt=prompt, sampling_params=sampling_params)
        
        print(f"输出: {output.get('text', output)}")
        
        monitor.set_phase("completed")
        
        # 保存详细报告
        report_filename = f"deepseek_operator_analysis_{int(time.time())}.json"
        monitor.analyze_and_save_report(report_filename)
        
        # 对比期望的算子shape
        print("\n=== 对比预期的算子shape ===")
        expected_shapes = {
            'attn_wqa': (1, 128, 1536, 7168),
            'attn_wqb': (1, 128, 24576, 1536),
            'attn_wkv_a': (1, 128, 576, 7168),
            'attn_wkv_b': (1, 128, 32768, 512),
            'dense_up': (1, 128, 18432, 7168),
            'dense_gate': (1, 128, 18432, 7168),
            'dense_down': (1, 128, 7168, 18432)
        }
        
        for op_name, expected_shape in expected_shapes.items():
            print(f"期望 {op_name}: {expected_shape}")
            
    except Exception as e:
        print(f"[ERROR] 执行出错: {e}")
        print(traceback.format_exc())
        return 1
    finally:
        if llm is not None:
            try:
                llm.shutdown()
            except Exception:
                pass
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
