#!/usr/bin/env python3
"""
SGLang 简化算子分析脚本 - 专门用于Prefill和Decode阶段分析
使用 TP=2 (GPU 0,1) 进行测试，全面捕获所有计算算子
"""

import os
import sys
import traceback
import json
import torch
import torch.nn.functional as F
import numpy as np
from typing import Dict, List, Tuple, Any, Optional
from collections import defaultdict
import time
import threading
from functools import wraps

# 设置环境变量
os.environ.setdefault("SGL_DISABLE_TP_MEMORY_INBALANCE_CHECK", "1")
os.environ.setdefault("UCX_VFS_ENABLE", "n") 
os.environ.setdefault("NCCL_P2P_DISABLE", "1")
os.environ.setdefault("NCCL_IB_DISABLE", "1")
os.environ.setdefault("SGLANG_IS_FLASHINFER_AVAILABLE", "false")
os.environ.setdefault("CUDA_VISIBLE_DEVICES", "0,1")

class SimpleOperatorTracker:
    """简化的算子追踪器"""
    
    def __init__(self):
        self.operations = []
        self.phase = "initialization"
        self.token_count = 0
        self.sequence_lengths = []
        self.start_time = time.time()
        self.is_prefill_phase = True
        
    def set_phase(self, phase_name: str):
        self.phase = phase_name
        print(f"\n[PHASE] 进入阶段: {phase_name}")
        
    def detect_phase_from_shapes(self, input_shapes: List[Tuple]) -> str:
        """从输入shape自动检测当前是prefill还是decode阶段"""
        max_seq_len = 1
        for shape in input_shapes:
            if len(shape) >= 2:
                # 假设第二个维度是序列长度
                seq_len = shape[1] if len(shape) > 1 else shape[0]
                if isinstance(seq_len, int):
                    max_seq_len = max(max_seq_len, seq_len)
        
        # 记录序列长度变化
        if max_seq_len not in self.sequence_lengths:
            self.sequence_lengths.append(max_seq_len)
            
        # 判断阶段：序列长度>1为prefill，=1为decode
        if max_seq_len > 1:
            if not self.is_prefill_phase:
                print(f"[AUTO-DETECT] 序列长度 {max_seq_len} -> Prefill阶段")
                self.is_prefill_phase = True
            return "prefill"
        else:
            if self.is_prefill_phase and len(self.sequence_lengths) > 1:
                print(f"[AUTO-DETECT] 序列长度 {max_seq_len} -> Decode阶段")
                self.is_prefill_phase = False
            return "decode" if len(self.sequence_lengths) > 1 else "prefill"
            
    def log_operation(self, op_name: str, input_tensors: List[torch.Tensor], 
                     output_tensors: List[torch.Tensor], execution_time: float = None):
        """记录算子操作"""
        input_shapes = []
        input_dtypes = []
        total_input_elements = 0
        
        for tensor in input_tensors:
            if hasattr(tensor, 'shape') and hasattr(tensor, 'dtype'):
                shape = tuple(tensor.shape)
                dtype = str(tensor.dtype)
                elements = tensor.numel()
                input_shapes.append(shape)
                input_dtypes.append(dtype)
                total_input_elements += elements
            else:
                input_shapes.append(None)
                input_dtypes.append(None)
                
        output_shapes = []
        output_dtypes = []
        total_output_elements = 0
        
        for tensor in output_tensors:
            if hasattr(tensor, 'shape') and hasattr(tensor, 'dtype'):
                shape = tuple(tensor.shape)
                dtype = str(tensor.dtype)
                elements = tensor.numel()
                output_shapes.append(shape)
                output_dtypes.append(dtype)
                total_output_elements += elements
            else:
                output_shapes.append(None)
                output_dtypes.append(None)
                
        # 自动检测阶段
        detected_phase = self.detect_phase_from_shapes(input_shapes)
        
        # 估算FLOPs
        estimated_flops = self.estimate_flops(op_name, input_shapes, output_shapes)
        
        operation_record = {
            'timestamp': time.time() - self.start_time,
            'phase': detected_phase,
            'operation': op_name,
            'input_shapes': input_shapes,
            'input_dtypes': input_dtypes,
            'output_shapes': output_shapes,
            'output_dtypes': output_dtypes,
            'total_input_elements': total_input_elements,
            'total_output_elements': total_output_elements,
            'estimated_flops': estimated_flops,
            'execution_time_ms': execution_time * 1000 if execution_time else None
        }
        
        self.operations.append(operation_record)
        
        # 实时打印
        print(f"[{detected_phase.upper()}] {op_name}")
        print(f"  输入: {input_shapes} -> 输出: {output_shapes}")
        print(f"  FLOPs: {estimated_flops:,}, 元素: {total_input_elements:,} -> {total_output_elements:,}")
        if execution_time:
            print(f"  耗时: {execution_time*1000:.3f}ms")
            
    def estimate_flops(self, op_name: str, input_shapes: List[Tuple], output_shapes: List[Tuple]) -> int:
        """估算算子FLOPs"""
        valid_inputs = [shape for shape in input_shapes if shape is not None]
        valid_outputs = [shape for shape in output_shapes if shape is not None]
        
        if not valid_inputs:
            return 0
            
        if op_name in ['linear', 'addmm']:
            if len(valid_inputs) >= 2:
                input_shape = valid_inputs[0]
                weight_shape = valid_inputs[1]
                if len(input_shape) >= 2 and len(weight_shape) >= 2:
                    batch_size = np.prod(input_shape[:-1]) if len(input_shape) > 2 else input_shape[0]
                    input_features = input_shape[-1]
                    output_features = weight_shape[0] if weight_shape[1] == input_features else weight_shape[1]
                    return 2 * batch_size * input_features * output_features
                    
        elif op_name in ['matmul', 'bmm']:
            if len(valid_inputs) >= 2:
                shape1, shape2 = valid_inputs[0], valid_inputs[1]
                if len(shape1) >= 2 and len(shape2) >= 2:
                    if len(shape1) == 3 and len(shape2) == 3:  # bmm
                        return 2 * shape1[0] * shape1[1] * shape1[2] * shape2[2]
                    else:  # matmul
                        return 2 * shape1[-2] * shape1[-1] * shape2[-1]
                        
        elif op_name == 'int8_scaled_mm':
            if len(valid_inputs) >= 2:
                shape1, shape2 = valid_inputs[0], valid_inputs[1]
                if len(shape1) >= 2 and len(shape2) >= 2:
                    return 2 * shape1[-2] * shape1[-1] * shape2[-1]
                    
        elif op_name == 'embedding':
            if len(valid_inputs) >= 2:
                indices_shape, weight_shape = valid_inputs[0], valid_inputs[1]
                if indices_shape and weight_shape:
                    num_indices = np.prod(indices_shape)
                    embedding_dim = weight_shape[-1] if weight_shape else 0
                    return num_indices * embedding_dim
                    
        # 默认估算
        return sum(np.prod(shape) for shape in valid_inputs)
        
    def save_analysis_report(self, filename: str):
        """保存分析报告"""
        # 按阶段分组
        prefill_ops = [op for op in self.operations if op['phase'] == 'prefill']
        decode_ops = [op for op in self.operations if op['phase'] == 'decode']
        
        # 统计各阶段的算子分布
        def analyze_phase_ops(ops, phase_name):
            if not ops:
                return {}
                
            op_stats = defaultdict(lambda: {'count': 0, 'total_flops': 0, 'total_time': 0, 'shapes': []})
            total_flops = 0
            total_time = 0
            
            for op in ops:
                op_name = op['operation']
                flops = op['estimated_flops']
                exec_time = op['execution_time_ms'] or 0
                
                op_stats[op_name]['count'] += 1
                op_stats[op_name]['total_flops'] += flops
                op_stats[op_name]['total_time'] += exec_time
                
                shape_info = {
                    'input': op['input_shapes'],
                    'output': op['output_shapes']
                }
                if shape_info not in op_stats[op_name]['shapes']:
                    op_stats[op_name]['shapes'].append(shape_info)
                    
                total_flops += flops
                total_time += exec_time
                
            return {
                'operations': dict(op_stats),
                'total_flops': total_flops,
                'total_time_ms': total_time,
                'operation_count': len(ops)
            }
            
        prefill_analysis = analyze_phase_ops(prefill_ops, 'prefill')
        decode_analysis = analyze_phase_ops(decode_ops, 'decode')
        
        # 生成完整报告
        report = {
            'execution_summary': {
                'total_operations': len(self.operations),
                'execution_time_seconds': time.time() - self.start_time,
                'gpu_count': 2,
                'detected_sequence_lengths': self.sequence_lengths,
                'model_path': os.environ.get("MODEL_PATH", "/home/<USER>/deepseek-int8")
            },
            'prefill_analysis': prefill_analysis,
            'decode_analysis': decode_analysis,
            'comparison': {
                'prefill_decode_flops_ratio': (prefill_analysis.get('total_flops', 0) / 
                                             max(decode_analysis.get('total_flops', 1), 1)),
                'prefill_decode_time_ratio': (prefill_analysis.get('total_time_ms', 0) / 
                                            max(decode_analysis.get('total_time_ms', 1), 1)),
                'prefill_decode_ops_ratio': (prefill_analysis.get('operation_count', 0) / 
                                           max(decode_analysis.get('operation_count', 1), 1))
            },
            'all_operations': self.operations
        }
        
        # 保存JSON报告
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False, default=str)
            
        print(f"\n=== 详细分析报告已保存: {filename} ===")
        
        # 打印摘要
        self.print_summary(prefill_analysis, decode_analysis, report['comparison'])
        
    def print_summary(self, prefill_analysis, decode_analysis, comparison):
        """打印分析摘要"""
        print("\n" + "="*80)
        print("SGLang 算子性能分析总结 (TP=2)")
        print("="*80)
        
        print(f"\n【执行概况】")
        print(f"总算子调用次数: {len(self.operations)}")
        print(f"检测到的序列长度: {self.sequence_lengths}")
        print(f"总执行时间: {time.time() - self.start_time:.2f}秒")
        
        print(f"\n【Prefill阶段分析】")
        if prefill_analysis:
            print(f"算子调用次数: {prefill_analysis['operation_count']}")
            print(f"总FLOPs: {prefill_analysis['total_flops']:,}")
            print(f"总耗时: {prefill_analysis['total_time_ms']:.2f}ms")
            print(f"主要算子:")
            for op_name, stats in sorted(prefill_analysis['operations'].items(), 
                                       key=lambda x: x[1]['total_flops'], reverse=True)[:5]:
                print(f"  {op_name}: {stats['count']}次, {stats['total_flops']:,} FLOPs, {stats['total_time']:.2f}ms")
        else:
            print("  未检测到Prefill阶段操作")
            
        print(f"\n【Decode阶段分析】")
        if decode_analysis:
            print(f"算子调用次数: {decode_analysis['operation_count']}")
            print(f"总FLOPs: {decode_analysis['total_flops']:,}")
            print(f"总耗时: {decode_analysis['total_time_ms']:.2f}ms")
            print(f"主要算子:")
            for op_name, stats in sorted(decode_analysis['operations'].items(), 
                                       key=lambda x: x[1]['total_flops'], reverse=True)[:5]:
                print(f"  {op_name}: {stats['count']}次, {stats['total_flops']:,} FLOPs, {stats['total_time']:.2f}ms")
        else:
            print("  未检测到Decode阶段操作")
            
        print(f"\n【Prefill vs Decode 对比】")
        print(f"FLOPs比例 (Prefill/Decode): {comparison['prefill_decode_flops_ratio']:.2f}")
        print(f"时间比例 (Prefill/Decode): {comparison['prefill_decode_time_ratio']:.2f}")
        print(f"操作数比例 (Prefill/Decode): {comparison['prefill_decode_ops_ratio']:.2f}")

# 全局追踪器
tracker = SimpleOperatorTracker()

# 保存原始函数的字典
original_funcs = {}

def create_monitor_wrapper(func_name: str, module, full_path: str):
    """创建算子监控包装器"""
    attr_name = func_name
    if not hasattr(module, attr_name):
        return
        
    original_func = getattr(module, attr_name)
    original_funcs[full_path] = original_func
    
    @wraps(original_func)
    def wrapper(*args, **kwargs):
        # 提取tensor参数
        input_tensors = []
        for arg in args:
            if isinstance(arg, torch.Tensor):
                input_tensors.append(arg)
        for value in kwargs.values():
            if isinstance(value, torch.Tensor):
                input_tensors.append(value)
                
        # 测量执行时间
        start_time = time.perf_counter()
        result = original_func(*args, **kwargs)
        end_time = time.perf_counter()
        execution_time = end_time - start_time
        
        # 提取输出tensor
        output_tensors = []
        if isinstance(result, torch.Tensor):
            output_tensors.append(result)
        elif isinstance(result, (tuple, list)):
            for item in result:
                if isinstance(item, torch.Tensor):
                    output_tensors.append(item)
                    
        # 记录操作
        tracker.log_operation(func_name, input_tensors, output_tensors, execution_time)
        
        return result
        
    setattr(module, attr_name, wrapper)

# 监控的算子列表 - 扩展版本
monitor_targets = [
    # 基础线性运算
    ('linear', torch.nn.functional),
    ('matmul', torch),
    ('bmm', torch),
    ('addmm', torch),
    ('mm', torch),
    
    # 激活函数
    ('gelu', torch.nn.functional),
    ('relu', torch.nn.functional),
    ('silu', torch.nn.functional),
    ('softmax', torch.nn.functional),
    
    # 归一化
    ('layer_norm', torch.nn.functional),
    ('group_norm', torch.nn.functional),
    ('rms_norm', torch.nn.functional),
    
    # Embedding
    ('embedding', torch.nn.functional),
    
    # 卷积（某些架构可能使用）
    ('conv1d', torch.nn.functional),
    ('conv2d', torch.nn.functional),
    
    # 注意力相关
    ('scaled_dot_product_attention', torch.nn.functional),
]

# 应用监控
for func_name, module in monitor_targets:
    try:
        create_monitor_wrapper(func_name, module, f"{module.__name__}.{func_name}")
    except Exception as e:
        print(f"警告: 无法监控 {module.__name__}.{func_name}: {e}")

# 监控量化相关函数
try:
    import sgl_kernel
    if hasattr(sgl_kernel, 'int8_scaled_mm'):
        create_monitor_wrapper('int8_scaled_mm', sgl_kernel, 'sgl_kernel.int8_scaled_mm')
        print("成功监控 sgl_kernel.int8_scaled_mm")
except ImportError:
    print("sgl_kernel 不可用，跳过量化内核监控")

import sglang as sgl

MODEL_PATH = os.environ.get("MODEL_PATH", "/home/<USER>/deepseek-int8")

def run_simple_test():
    """运行简化的测试"""
    llm = None
    try:
        print("[INFO] 初始化SGLang引擎 (TP=2, GPU 0,1)...")
        tracker.set_phase("engine_init")
        
        llm = sgl.Engine(
            model_path=MODEL_PATH,
            tp_size=2,
            quantization="w8a8_int8",
            trust_remote_code=True,
            dist_init_addr="127.0.0.1:8001",
            disable_cuda_graph=True,
            log_level="info",
        )
        
        print("[INFO] 引擎初始化完成，开始推理测试...")
        
        # 测试案例 - 从短到长，确保能观察到prefill和decode的区别
        test_prompts = [
            ("短序列", "深度学习是什么？", 20),
            ("中等序列", "请详细解释什么是Transformer架构，包括注意力机制的工作原理。", 15),
            ("长序列", "人工智能技术的发展历程可以追溯到20世纪50年代，从早期的专家系统到现在的深度学习和大语言模型，每一个阶段都有重要的技术突破。深度学习特别是Transformer架构的出现，为自然语言处理带来了革命性的变化。请分析这个发展趋势并预测未来的发展方向。", 10)
        ]
        
        for i, (name, prompt, max_tokens) in enumerate(test_prompts):
            print(f"\n{'='*60}")
            print(f"测试 {i+1}: {name} (最大生成{max_tokens}个token)")
            print(f"{'='*60}")
            
            tracker.set_phase(f"test_{i+1}")
            
            sampling_params = {
                "max_new_tokens": max_tokens,
                "temperature": 0.1
            }
            
            print(f"输入: {prompt}")
            
            start_time = time.time()
            output = llm.generate(prompt=prompt, sampling_params=sampling_params)
            end_time = time.time()
            
            print(f"输出: {output.get('text', output)}")
            print(f"生成耗时: {(end_time - start_time)*1000:.2f}ms")
            
            time.sleep(0.5)  # 短暂停顿
            
        tracker.set_phase("completed")
        
        # 保存分析报告
        report_filename = f"simple_operator_analysis_{int(time.time())}.json"
        tracker.save_analysis_report(report_filename)
        
        return 0
        
    except Exception as e:
        print(f"[ERROR] 执行出错: {e}")
        print(traceback.format_exc())
        return 1
    finally:
        if llm is not None:
            try:
                llm.shutdown()
            except Exception:
                pass

if __name__ == "__main__":
    sys.exit(run_simple_test())
