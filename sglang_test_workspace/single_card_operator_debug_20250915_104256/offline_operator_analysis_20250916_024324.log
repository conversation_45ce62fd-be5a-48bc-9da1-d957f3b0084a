2025-09-16 02:43:24,272 - INFO - 开始离线算子分析...
2025-09-16 02:43:24,272 - INFO - 初始化OfflineOperatorTracker，输出目录: offline_analysis_20250916_024324
2025-09-16 02:43:24,272 - INFO - 注册算子hooks...
2025-09-16 02:43:24,272 - INFO - 算子hooks注册完成
2025-09-16 02:43:24,272 - INFO - 开始模拟模型推理...
2025-09-16 02:43:24,414 - INFO - 模拟Prefill阶段...
2025-09-16 02:43:24,912 - INFO - [prefill] torch.nn.functional.embedding | Input: [[1, 512], [128256, 4096]] | Output: [[1, 512, 4096]] | FLOPs: 5.12e+04
2025-09-16 02:43:25,063 - INFO - [prefill] torch.nn.functional.linear | Input: [[1, 512, 4096], [4096, 4096]] | Output: [[1, 512, 4096]] | FLOPs: 1.72e+10
2025-09-16 02:43:25,064 - INFO - [prefill] torch.nn.functional.linear | Input: [[1, 512, 4096], [4096, 4096]] | Output: [[1, 512, 4096]] | FLOPs: 1.72e+10
2025-09-16 02:43:25,065 - INFO - [prefill] torch.nn.functional.linear | Input: [[1, 512, 4096], [4096, 4096]] | Output: [[1, 512, 4096]] | FLOPs: 1.72e+10
2025-09-16 02:43:25,066 - INFO - [prefill] torch.matmul | Input: [[1, 32, 512, 128], [1, 32, 128, 512]] | Output: [[1, 32, 512, 512]] | FLOPs: 6.71e+07
2025-09-16 02:43:25,084 - INFO - [prefill] torch.matmul | Input: [[1, 32, 512, 512], [1, 32, 512, 128]] | Output: [[1, 32, 512, 128]] | FLOPs: 6.71e+07
2025-09-16 02:43:25,093 - INFO - [prefill] torch.nn.functional.linear | Input: [[1, 512, 4096], [4096, 4096]] | Output: [[1, 512, 4096]] | FLOPs: 1.72e+10
2025-09-16 02:43:25,095 - INFO - [prefill] torch.nn.functional.linear | Input: [[1, 512, 4096], [14336, 4096]] | Output: [[1, 512, 14336]] | FLOPs: 1.72e+10
2025-09-16 02:43:25,095 - INFO - [prefill] torch.nn.functional.linear | Input: [[1, 512, 4096], [14336, 4096]] | Output: [[1, 512, 14336]] | FLOPs: 1.72e+10
2025-09-16 02:43:25,111 - INFO - [prefill] torch.nn.functional.linear | Input: [[1, 512, 14336], [4096, 14336]] | Output: [[1, 512, 4096]] | FLOPs: 2.10e+11
2025-09-16 02:43:25,111 - INFO - Prefill阶段模拟完成
2025-09-16 02:43:25,111 - INFO - 模拟Decode阶段...
2025-09-16 02:43:25,151 - INFO - [decode] torch.nn.functional.embedding | Input: [[1, 1], [128256, 4096]] | Output: [[1, 1, 4096]] | FLOPs: 1.00e+02
2025-09-16 02:43:25,151 - INFO - [decode] torch.nn.functional.linear | Input: [[1, 1, 4096], [4096, 4096]] | Output: [[1, 1, 4096]] | FLOPs: 3.36e+07
2025-09-16 02:43:25,152 - INFO - [decode] torch.nn.functional.linear | Input: [[1, 1, 4096], [4096, 4096]] | Output: [[1, 1, 4096]] | FLOPs: 3.36e+07
2025-09-16 02:43:25,154 - INFO - [decode] torch.nn.functional.linear | Input: [[1, 1, 4096], [4096, 4096]] | Output: [[1, 1, 4096]] | FLOPs: 3.36e+07
2025-09-16 02:43:25,172 - INFO - [decode] torch.matmul | Input: [[1, 32, 1, 128], [1, 32, 128, 1]] | Output: [[1, 32, 1, 1]] | FLOPs: 2.56e+02
2025-09-16 02:43:25,178 - INFO - [decode] torch.matmul | Input: [[1, 32, 1, 1], [1, 32, 1, 128]] | Output: [[1, 32, 1, 128]] | FLOPs: 2.56e+02
2025-09-16 02:43:25,186 - INFO - [decode] torch.nn.functional.linear | Input: [[1, 1, 4096], [14336, 4096]] | Output: [[1, 1, 14336]] | FLOPs: 3.36e+07
2025-09-16 02:43:25,186 - INFO - [decode] torch.nn.functional.linear | Input: [[1, 1, 4096], [14336, 4096]] | Output: [[1, 1, 14336]] | FLOPs: 3.36e+07
2025-09-16 02:43:25,187 - INFO - [decode] torch.nn.functional.linear | Input: [[1, 1, 14336], [4096, 14336]] | Output: [[1, 1, 4096]] | FLOPs: 4.11e+08
2025-09-16 02:43:25,187 - INFO - Decode阶段模拟完成
2025-09-16 02:43:25,187 - INFO - 添加额外的矩阵运算...
2025-09-16 02:43:25,198 - INFO - [prefill] torch.bmm | Input: [[1, 512, 1024], [1, 1024, 2048]] | Output: [[1, 512, 2048]] | FLOPs: 2.15e+09
2025-09-16 02:43:26,226 - INFO - [prefill] torch.bmm | Input: [[1, 512, 1024], [1, 1024, 2048]] | Output: [[1, 512, 2048]] | FLOPs: 2.15e+09
2025-09-16 02:43:26,227 - INFO - [prefill] torch.bmm | Input: [[1, 512, 1024], [1, 1024, 2048]] | Output: [[1, 512, 2048]] | FLOPs: 2.15e+09
2025-09-16 02:43:26,228 - INFO - [prefill] torch.bmm | Input: [[1, 512, 1024], [1, 1024, 2048]] | Output: [[1, 512, 2048]] | FLOPs: 2.15e+09
2025-09-16 02:43:26,228 - INFO - [prefill] torch.bmm | Input: [[1, 512, 1024], [1, 1024, 2048]] | Output: [[1, 512, 2048]] | FLOPs: 2.15e+09
2025-09-16 02:43:26,229 - INFO - 模拟推理完成
2025-09-16 02:43:27,230 - INFO - 保存分析结果...
2025-09-16 02:43:27,239 - INFO - 分析结果已保存到: offline_analysis_20250916_024324/offline_analysis.json
2025-09-16 02:43:27,240 - INFO - 汇总报告已保存到: offline_analysis_20250916_024324/offline_summary_report.md
2025-09-16 02:43:27,240 - INFO - 分析完成！结果保存在: offline_analysis_20250916_024324
2025-09-16 02:43:27,241 - INFO - 捕获到 24 个算子调用
2025-09-16 02:43:27,241 - INFO - Prefill阶段: 15 个算子
2025-09-16 02:43:27,241 - INFO - Decode阶段: 9 个算子
2025-09-16 02:43:27,241 - INFO - 未分类: 0 个算子
2025-09-16 02:43:27,241 - INFO - 算子类型统计:
2025-09-16 02:43:27,241 - INFO -   embedding: 2
2025-09-16 02:43:27,241 - INFO -   linear: 13
2025-09-16 02:43:27,242 - INFO -   matmul: 4
2025-09-16 02:43:27,242 - INFO -   bmm: 5
2025-09-16 02:43:27,242 - INFO -   int8_mm: 0
2025-09-16 02:43:27,242 - INFO -   attention: 0
