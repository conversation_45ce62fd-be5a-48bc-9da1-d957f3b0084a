2025-09-16 02:50:18,102 - INFO - 开始全面的算子分析...
2025-09-16 02:50:18,103 - INFO - 初始化ComprehensiveOperatorTracker，输出目录: comprehensive_analysis_20250916_025018
2025-09-16 02:50:18,103 - INFO - 注册算子hooks...
2025-09-16 02:50:18,103 - INFO - 算子hooks注册完成
2025-09-16 02:50:18,103 - INFO - 开始模拟全面的模型推理...
2025-09-16 02:50:21,218 - INFO - === 模拟Prefill阶段 ===
2025-09-16 02:50:21,675 - ERROR - 更新计数器错误: 'int' object is not subscriptable
2025-09-16 02:50:21,675 - INFO - [prefill][embedding.lookup] torch.nn.functional.embedding | Layer:0 | Input:[[1, 512], [128256, 4096]] | Output:[[1, 512, 4096]] | FLOPs:5.12e+04
2025-09-16 02:50:21,675 - INFO - --- Layer 0 ---
2025-09-16 02:50:24,589 - INFO - [prefill][attn.output] torch.nn.functional.linear | Layer:0 | Input:[[1, 512, 4096], [4096, 4096]] | Output:[[1, 512, 4096]] | FLOPs:1.72e+10
2025-09-16 02:50:24,591 - INFO - [prefill][attn.query] torch.nn.functional.linear | Layer:0 | Input:[[1, 512, 4096], [4096, 4096]] | Output:[[1, 512, 4096]] | FLOPs:1.72e+10
2025-09-16 02:50:24,592 - INFO - [prefill][attn.query] torch.nn.functional.linear | Layer:0 | Input:[[1, 512, 4096], [4096, 4096]] | Output:[[1, 512, 4096]] | FLOPs:1.72e+10
2025-09-16 02:50:24,597 - INFO - [prefill][attn.matmul] torch.matmul | Layer:0 | Input:[[1, 32, 512, 128], [1, 32, 128, 512]] | Output:[[1, 32, 512, 512]] | FLOPs:6.71e+07
2025-09-16 02:50:24,616 - INFO - [prefill][attn.matmul] torch.matmul | Layer:0 | Input:[[1, 32, 512, 512], [1, 32, 512, 128]] | Output:[[1, 32, 512, 128]] | FLOPs:6.71e+07
2025-09-16 02:50:24,626 - INFO - [prefill][attn.query] torch.nn.functional.linear | Layer:0 | Input:[[1, 512, 4096], [4096, 4096]] | Output:[[1, 512, 4096]] | FLOPs:1.72e+10
2025-09-16 02:50:24,635 - INFO - [prefill][expert.router] torch.nn.functional.linear | Layer:0 | Input:[[1, 512, 4096], [8, 4096]] | Output:[[1, 512, 8]] | FLOPs:1.72e+10
2025-09-16 02:50:24,666 - INFO - [prefill][expert.gate] torch.nn.functional.linear | Layer:0 | Input:[[1, 512, 4096], [14336, 4096]] | Output:[[1, 512, 14336]] | FLOPs:1.72e+10
2025-09-16 02:50:24,667 - INFO - [prefill][expert.gate] torch.nn.functional.linear | Layer:0 | Input:[[1, 512, 4096], [14336, 4096]] | Output:[[1, 512, 14336]] | FLOPs:1.72e+10
2025-09-16 02:50:24,685 - INFO - [prefill][expert.down] torch.nn.functional.linear | Layer:1 | Input:[[1, 512, 14336], [4096, 14336]] | Output:[[1, 512, 4096]] | FLOPs:2.10e+11
2025-09-16 02:50:24,693 - INFO - --- Layer 1 ---
2025-09-16 02:50:24,701 - INFO - [prefill][attn.query] torch.nn.functional.linear | Layer:1 | Input:[[1, 512, 4096], [4096, 4096]] | Output:[[1, 512, 4096]] | FLOPs:1.72e+10
2025-09-16 02:50:24,702 - INFO - [prefill][attn.query] torch.nn.functional.linear | Layer:1 | Input:[[1, 512, 4096], [4096, 4096]] | Output:[[1, 512, 4096]] | FLOPs:1.72e+10
2025-09-16 02:50:24,702 - INFO - [prefill][attn.query] torch.nn.functional.linear | Layer:1 | Input:[[1, 512, 4096], [4096, 4096]] | Output:[[1, 512, 4096]] | FLOPs:1.72e+10
2025-09-16 02:50:24,702 - INFO - [prefill][attn.matmul] torch.matmul | Layer:1 | Input:[[1, 32, 512, 128], [1, 32, 128, 512]] | Output:[[1, 32, 512, 512]] | FLOPs:6.71e+07
2025-09-16 02:50:24,703 - INFO - [prefill][attn.matmul] torch.matmul | Layer:1 | Input:[[1, 32, 512, 512], [1, 32, 512, 128]] | Output:[[1, 32, 512, 128]] | FLOPs:6.71e+07
2025-09-16 02:50:24,703 - INFO - [prefill][attn.query] torch.nn.functional.linear | Layer:1 | Input:[[1, 512, 4096], [4096, 4096]] | Output:[[1, 512, 4096]] | FLOPs:1.72e+10
2025-09-16 02:50:24,703 - INFO - [prefill][expert.router] torch.nn.functional.linear | Layer:1 | Input:[[1, 512, 4096], [8, 4096]] | Output:[[1, 512, 8]] | FLOPs:1.72e+10
2025-09-16 02:50:24,704 - INFO - [prefill][expert.gate] torch.nn.functional.linear | Layer:1 | Input:[[1, 512, 4096], [14336, 4096]] | Output:[[1, 512, 14336]] | FLOPs:1.72e+10
2025-09-16 02:50:24,704 - INFO - [prefill][expert.gate] torch.nn.functional.linear | Layer:1 | Input:[[1, 512, 4096], [14336, 4096]] | Output:[[1, 512, 14336]] | FLOPs:1.72e+10
2025-09-16 02:50:24,704 - INFO - [prefill][expert.down] torch.nn.functional.linear | Layer:2 | Input:[[1, 512, 14336], [4096, 14336]] | Output:[[1, 512, 4096]] | FLOPs:2.10e+11
2025-09-16 02:50:24,705 - ERROR - 更新计数器错误: 'int' object is not subscriptable
2025-09-16 02:50:24,705 - INFO - [prefill][output.projection] torch.nn.functional.linear | Layer:2 | Input:[[1, 512, 4096], [128256, 4096]] | Output:[[1, 512, 128256]] | FLOPs:1.72e+10
2025-09-16 02:50:24,705 - INFO - Prefill阶段模拟完成
2025-09-16 02:50:24,705 - INFO - === 模拟Decode阶段 ===
2025-09-16 02:50:24,745 - ERROR - 更新计数器错误: 'int' object is not subscriptable
2025-09-16 02:50:24,745 - INFO - [decode][embedding.lookup] torch.nn.functional.embedding | Layer:2 | Input:[[1, 1], [128256, 4096]] | Output:[[1, 1, 4096]] | FLOPs:1.00e+02
2025-09-16 02:50:24,745 - INFO - --- Decode Layer 0 ---
2025-09-16 02:50:24,745 - INFO - [decode][attn.output] torch.nn.functional.linear | Layer:2 | Input:[[1, 1, 4096], [4096, 4096]] | Output:[[1, 1, 4096]] | FLOPs:3.36e+07
2025-09-16 02:50:24,746 - INFO - [decode][attn.query] torch.nn.functional.linear | Layer:2 | Input:[[1, 1, 4096], [4096, 4096]] | Output:[[1, 1, 4096]] | FLOPs:3.36e+07
2025-09-16 02:50:24,746 - INFO - [decode][attn.query] torch.nn.functional.linear | Layer:2 | Input:[[1, 1, 4096], [4096, 4096]] | Output:[[1, 1, 4096]] | FLOPs:3.36e+07
2025-09-16 02:50:24,759 - INFO - [decode][attn.matmul] torch.matmul | Layer:2 | Input:[[1, 32, 1, 128], [1, 32, 128, 1]] | Output:[[1, 32, 1, 1]] | FLOPs:2.56e+02
2025-09-16 02:50:24,761 - INFO - [decode][attn.matmul] torch.matmul | Layer:2 | Input:[[1, 32, 1, 1], [1, 32, 1, 128]] | Output:[[1, 32, 1, 128]] | FLOPs:2.56e+02
2025-09-16 02:50:24,761 - ERROR - 更新计数器错误: 'int' object is not subscriptable
2025-09-16 02:50:24,761 - INFO - [decode][output.projection] torch.nn.functional.linear | Layer:2 | Input:[[1, 1, 4096], [128256, 4096]] | Output:[[1, 1, 128256]] | FLOPs:3.36e+07
2025-09-16 02:50:24,764 - ERROR - 执行过程中出错: mat1 and mat2 shapes cannot be multiplied (1x128256 and 4096x14336)
2025-09-16 02:50:24,764 - INFO - 保存全面分析结果...
2025-09-16 02:50:24,767 - INFO - 全面分析结果已保存到: comprehensive_analysis_20250916_025018/comprehensive_analysis.json
2025-09-16 02:50:24,767 - INFO - 全面报告已保存到: comprehensive_analysis_20250916_025018/comprehensive_report.md
2025-09-16 02:50:24,767 - INFO - 全面分析完成！结果保存在: comprehensive_analysis_20250916_025018
2025-09-16 02:50:24,767 - INFO - 捕获到 29 个算子调用
2025-09-16 02:50:24,767 - INFO - Prefill阶段: 22 个算子
2025-09-16 02:50:24,767 - INFO - Decode阶段: 7 个算子
2025-09-16 02:50:24,767 - INFO - 模块操作统计:
2025-09-16 02:50:24,767 - INFO -   attn.query: 9
2025-09-16 02:50:24,767 - INFO -   attn.matmul: 6
2025-09-16 02:50:24,767 - INFO -   expert.gate: 4
2025-09-16 02:50:24,767 - INFO -   embedding.lookup: 2
2025-09-16 02:50:24,767 - INFO -   attn.output: 2
2025-09-16 02:50:24,768 - INFO -   expert.router: 2
2025-09-16 02:50:24,768 - INFO -   expert.down: 2
2025-09-16 02:50:24,768 - INFO -   output.projection: 2
