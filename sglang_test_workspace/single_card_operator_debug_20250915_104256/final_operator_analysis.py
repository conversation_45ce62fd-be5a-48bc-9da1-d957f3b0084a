#!/usr/bin/env python3
"""
最终的算子分析脚本 - 解决JSON保存问题，确保decode阶段被触发
"""

import torch
import torch.nn.functional as F
import json
import time
import logging
import threading
import queue
import psutil
import os
import sys
from datetime import datetime
from collections import defaultdict, Counter
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict

# 设置日志
log_file = f"final_operator_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class OperatorCall:
    """单个算子调用记录"""
    name: str
    input_shapes: List[List[int]]
    output_shapes: List[List[int]]
    dtypes: List[str]
    device: str
    timestamp: float
    phase: str
    memory_before: float
    memory_after: float
    flops: float
    
    def to_dict(self):
        return asdict(self)

class FinalOperatorTracker:
    """最终的算子追踪器 - 确保数据保存成功"""
    
    def __init__(self):
        self.operations = []
        self.phase = "unknown"
        self.start_time = time.time()
        self.lock = threading.Lock()
        self.output_dir = f"final_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 目标算子列表
        self.target_ops = {
            'torch.nn.functional.linear',
            'torch.nn.functional.embedding', 
            'torch.matmul',
            'torch.bmm',
            'torch.addmm',
            'torch.mm',
            'torch.ops.sgl_kernel.int8_scaled_mm',
            'torch.ops.cutlass.scaled_mm',
            'torch.ops.vllm.int8_mm',
            'torch.ops.quantized.linear',
            'torch.ops.quantized.conv2d',
            'torch.ops.aten.addmm',
            'torch.ops.aten.bmm',
            'torch.ops.aten.mm',
            'torch.ops.aten.linear',
            'torch.ops.aten.embedding'
        }
        
        logger.info(f"初始化FinalOperatorTracker，输出目录: {self.output_dir}")
        
    def get_memory_info(self):
        """获取GPU内存使用情况"""
        try:
            if torch.cuda.is_available():
                return torch.cuda.memory_allocated() / 1024**3  # GB
            return 0.0
        except:
            return 0.0
    
    def detect_phase(self, shapes):
        """检测当前是prefill还是decode阶段"""
        # 通过input tensor的形状判断
        for shape in shapes:
            if len(shape) >= 2:
                seq_len = shape[-2] if len(shape) > 2 else shape[-1]
                if seq_len > 50:  # 长序列通常是prefill
                    return "prefill"
                elif seq_len == 1:  # 序列长度为1通常是decode
                    return "decode"
        return "unknown"
    
    def estimate_flops(self, op_name, shapes):
        """估算FLOPs"""
        if not shapes or len(shapes) < 2:
            return 0.0
            
        try:
            if 'linear' in op_name.lower() or 'mm' in op_name.lower():
                # 矩阵乘法: M*K * K*N = M*N*K FLOPs
                if len(shapes) >= 2:
                    shape1, shape2 = shapes[0], shapes[1]
                    if len(shape1) >= 2 and len(shape2) >= 2:
                        m = shape1[-2] if len(shape1) > 1 else shape1[-1]
                        k = shape1[-1]
                        n = shape2[-1] if len(shape2) > 1 else shape2[0]
                        return float(m * k * n)
            elif 'embedding' in op_name.lower():
                # Embedding查找
                if shapes and len(shapes[0]) >= 1:
                    return float(shapes[0][-1] * 100)  # 估算
        except:
            pass
        return 0.0
    
    def hook_function(self, module, input_tensors, output):
        """通用hook函数"""
        try:
            op_name = str(type(module).__name__)
            if hasattr(module, '__class__'):
                op_name = f"{module.__class__.__module__}.{module.__class__.__name__}"
            
            # 获取输入输出形状
            input_shapes = []
            output_shapes = []
            dtypes = []
            device = "unknown"
            
            # 处理输入
            if isinstance(input_tensors, (list, tuple)):
                for inp in input_tensors:
                    if hasattr(inp, 'shape') and hasattr(inp, 'dtype'):
                        input_shapes.append(list(inp.shape))
                        dtypes.append(str(inp.dtype))
                        device = str(inp.device) if hasattr(inp, 'device') else device
            
            # 处理输出
            if hasattr(output, 'shape'):
                output_shapes.append(list(output.shape))
            elif isinstance(output, (list, tuple)):
                for out in output:
                    if hasattr(out, 'shape'):
                        output_shapes.append(list(out.shape))
            
            # 检测阶段
            phase = self.detect_phase(input_shapes)
            
            # 获取内存信息
            memory_before = self.get_memory_info()
            memory_after = memory_before  # 简化版本
            
            # 估算FLOPs
            flops = self.estimate_flops(op_name, input_shapes)
            
            # 创建记录
            op_call = OperatorCall(
                name=op_name,
                input_shapes=input_shapes,
                output_shapes=output_shapes,
                dtypes=dtypes,
                device=device,
                timestamp=time.time() - self.start_time,
                phase=phase,
                memory_before=memory_before,
                memory_after=memory_after,
                flops=flops
            )
            
            with self.lock:
                self.operations.append(op_call)
                
            # 实时输出重要算子
            if any(target in op_name.lower() for target in ['linear', 'embedding', 'matmul', 'bmm', 'mm']):
                logger.info(f"[{phase}] {op_name} | Input: {input_shapes} | Output: {output_shapes} | FLOPs: {flops:.2e}")
                
        except Exception as e:
            logger.error(f"Hook函数错误: {e}")
    
    def register_hooks(self):
        """注册hooks"""
        logger.info("注册hooks...")
        hooks = []
        
        # Hook torch.nn.functional中的函数
        original_linear = F.linear
        original_embedding = F.embedding
        
        def hook_linear(*args, **kwargs):
            result = original_linear(*args, **kwargs)
            try:
                self.record_functional_op("torch.nn.functional.linear", args, result)
            except:
                pass
            return result
        
        def hook_embedding(*args, **kwargs):
            result = original_embedding(*args, **kwargs)
            try:
                self.record_functional_op("torch.nn.functional.embedding", args, result)
            except:
                pass
            return result
        
        F.linear = hook_linear
        F.embedding = hook_embedding
        
        return hooks
    
    def record_functional_op(self, op_name, args, result):
        """记录functional操作"""
        try:
            input_shapes = []
            output_shapes = []
            dtypes = []
            device = "unknown"
            
            # 处理输入参数
            for arg in args:
                if hasattr(arg, 'shape') and hasattr(arg, 'dtype'):
                    input_shapes.append(list(arg.shape))
                    dtypes.append(str(arg.dtype))
                    device = str(arg.device) if hasattr(arg, 'device') else device
            
            # 处理输出
            if hasattr(result, 'shape'):
                output_shapes.append(list(result.shape))
            
            phase = self.detect_phase(input_shapes)
            flops = self.estimate_flops(op_name, input_shapes)
            memory = self.get_memory_info()
            
            op_call = OperatorCall(
                name=op_name,
                input_shapes=input_shapes,
                output_shapes=output_shapes,
                dtypes=dtypes,
                device=device,
                timestamp=time.time() - self.start_time,
                phase=phase,
                memory_before=memory,
                memory_after=memory,
                flops=flops
            )
            
            with self.lock:
                self.operations.append(op_call)
                
        except Exception as e:
            logger.error(f"记录functional操作错误: {e}")
    
    def save_analysis(self):
        """保存分析结果"""
        logger.info("保存分析结果...")
        
        try:
            # 分析数据
            prefill_ops = [op for op in self.operations if op.phase == "prefill"]
            decode_ops = [op for op in self.operations if op.phase == "decode"]
            
            # 统计信息
            analysis = {
                "execution_summary": {
                    "total_operations": len(self.operations),
                    "execution_time_seconds": time.time() - self.start_time,
                    "gpu_count": torch.cuda.device_count() if torch.cuda.is_available() else 0,
                    "prefill_ops": len(prefill_ops),
                    "decode_ops": len(decode_ops)
                },
                "prefill_analysis": self._analyze_phase(prefill_ops),
                "decode_analysis": self._analyze_phase(decode_ops),
                "all_operations": [op.to_dict() for op in self.operations[-100:]]  # 只保存最后100个操作避免文件过大
            }
            
            # 保存JSON
            json_file = os.path.join(self.output_dir, "final_analysis.json")
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(analysis, f, indent=2, ensure_ascii=False)
            
            logger.info(f"分析结果已保存到: {json_file}")
            
            # 创建汇总报告
            self._create_summary_report()
            
        except Exception as e:
            logger.error(f"保存分析结果错误: {e}")
    
    def _analyze_phase(self, ops):
        """分析特定阶段的操作"""
        if not ops:
            return {}
        
        op_counts = Counter(op.name for op in ops)
        total_flops = sum(op.flops for op in ops)
        
        return {
            "operation_count": len(ops),
            "total_flops": total_flops,
            "operation_types": dict(op_counts.most_common(10)),
            "avg_memory_usage": sum(op.memory_before for op in ops) / len(ops) if ops else 0,
            "timeline": [(op.timestamp, op.name, op.input_shapes) for op in ops[-10:]]
        }
    
    def _create_summary_report(self):
        """创建汇总报告"""
        report_file = os.path.join(self.output_dir, "summary_report.md")
        
        prefill_ops = [op for op in self.operations if op.phase == "prefill"]
        decode_ops = [op for op in self.operations if op.phase == "decode"]
        
        content = f"""# SGLang 算子分析报告

## 执行摘要
- 总算子调用次数: {len(self.operations)}
- Prefill阶段算子: {len(prefill_ops)}
- Decode阶段算子: {len(decode_ops)}
- 执行时间: {time.time() - self.start_time:.2f}秒

## Prefill阶段分析
{self._format_phase_analysis(prefill_ops)}

## Decode阶段分析
{self._format_phase_analysis(decode_ops)}

## 主要发现
1. 主要计算算子: {', '.join(set(op.name.split('.')[-1] for op in self.operations if 'linear' in op.name.lower() or 'embedding' in op.name.lower()))}
2. 内存使用峰值: {max((op.memory_before for op in self.operations), default=0):.2f} GB
3. 总计算量估算: {sum(op.flops for op in self.operations):.2e} FLOPs
"""
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        logger.info(f"汇总报告已保存到: {report_file}")
    
    def _format_phase_analysis(self, ops):
        """格式化阶段分析"""
        if not ops:
            return "无操作记录"
        
        op_counts = Counter(op.name for op in ops)
        return f"""
- 算子调用次数: {len(ops)}
- 主要算子类型: {dict(op_counts.most_common(5))}
- 总FLOPs: {sum(op.flops for op in ops):.2e}
- 典型输入形状: {[op.input_shapes for op in ops[:3]]}
"""

def main():
    """主函数"""
    logger.info("开始最终的算子分析...")
    
    # 创建追踪器
    tracker = FinalOperatorTracker()
    
    try:
        # 注册hooks
        hooks = tracker.register_hooks()
        
        # 导入SGLang (可能会触发很多算子调用)
        logger.info("导入SGLang...")
        import sglang as sgl
        from sglang import function, system, user, assistant, gen, set_default_backend, RuntimeEndpoint
        
        # 设置SGLang后端
        logger.info("设置SGLang后端...")
        backend = RuntimeEndpoint("http://127.0.0.1:30000")
        sgl.set_default_backend(backend)
        
        # 定义测试函数
        @function
        def test_generation(s):
            """测试生成函数 - 确保触发prefill和decode"""
            s += user("请用中文简单介绍一下深度学习。")
            s += assistant(gen("response", max_tokens=100, temperature=0.7))
        
        logger.info("开始执行推理...")
        
        # 执行多次推理以确保捕获decode阶段
        for i in range(3):
            logger.info(f"执行第{i+1}次推理...")
            try:
                state = test_generation.run()
                logger.info(f"第{i+1}次推理完成")
                time.sleep(1)  # 短暂等待
            except Exception as e:
                logger.error(f"第{i+1}次推理失败: {e}")
        
        logger.info("推理完成，等待数据收集...")
        time.sleep(2)
        
    except Exception as e:
        logger.error(f"执行过程中出错: {e}")
    
    finally:
        # 保存分析结果
        logger.info("保存最终分析结果...")
        tracker.save_analysis()
        
        logger.info(f"分析完成！结果保存在: {tracker.output_dir}")
        
        # 输出简要统计
        logger.info(f"捕获到 {len(tracker.operations)} 个算子调用")
        prefill_count = len([op for op in tracker.operations if op.phase == "prefill"])
        decode_count = len([op for op in tracker.operations if op.phase == "decode"])
        logger.info(f"Prefill阶段: {prefill_count} 个算子")
        logger.info(f"Decode阶段: {decode_count} 个算子")

if __name__ == "__main__":
    main()
