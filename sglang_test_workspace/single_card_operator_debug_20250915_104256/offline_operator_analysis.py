#!/usr/bin/env python3
"""
离线算子分析脚本 - 直接加载模型进行推理，不依赖服务
"""

import os
import sys
import torch
import torch.nn.functional as F
import json
import time
import logging
import threading
from datetime import datetime
from collections import defaultdict, Counter
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict

# 设置日志
log_file = f"offline_operator_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class OperatorCall:
    """单个算子调用记录"""
    name: str
    input_shapes: List[List[int]]
    output_shapes: List[List[int]]
    dtypes: List[str]
    device: str
    timestamp: float
    phase: str
    memory_before: float
    memory_after: float
    flops: float
    
    def to_dict(self):
        return asdict(self)

class OfflineOperatorTracker:
    """离线算子追踪器"""
    
    def __init__(self):
        self.operations = []
        self.phase = "unknown"
        self.start_time = time.time()
        self.lock = threading.Lock()
        self.output_dir = f"offline_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 算子计数器
        self.op_counters = {
            'embedding': 0,
            'linear': 0,
            'matmul': 0,
            'bmm': 0,
            'int8_mm': 0,
            'attention': 0
        }
        
        logger.info(f"初始化OfflineOperatorTracker，输出目录: {self.output_dir}")
        
    def get_memory_info(self):
        """获取GPU内存使用情况"""
        try:
            if torch.cuda.is_available():
                return torch.cuda.memory_allocated() / 1024**3  # GB
            return 0.0
        except:
            return 0.0
    
    def detect_phase(self, shapes, token_count=None):
        """检测当前是prefill还是decode阶段"""
        if token_count is not None:
            if token_count == 1:
                return "decode"
            elif token_count > 1:
                return "prefill"
        
        # 通过形状判断
        for shape in shapes:
            if len(shape) >= 2:
                seq_len = shape[-2] if len(shape) > 2 else shape[-1]
                if seq_len > 50:
                    return "prefill"
                elif seq_len == 1:
                    return "decode"
        return "unknown"
    
    def estimate_flops(self, op_name, shapes):
        """估算FLOPs"""
        if not shapes or len(shapes) < 2:
            return 0.0
            
        try:
            if 'linear' in op_name.lower() or 'mm' in op_name.lower() or 'matmul' in op_name.lower():
                # 矩阵乘法: M*K * K*N = M*N*K FLOPs  
                if len(shapes) >= 2:
                    shape1, shape2 = shapes[0], shapes[1]
                    if len(shape1) >= 2 and len(shape2) >= 2:
                        # 处理batch维度
                        batch_size = 1
                        if len(shape1) > 2:
                            batch_size = shape1[0]
                        
                        m = shape1[-2] if len(shape1) > 1 else shape1[-1]
                        k = shape1[-1]
                        n = shape2[-1] if len(shape2) > 1 else shape2[0]
                        return float(batch_size * m * k * n * 2)  # 乘法+加法
            elif 'embedding' in op_name.lower():
                if shapes and len(shapes[0]) >= 1:
                    return float(shapes[0][-1] * 100)  # 估算
            elif 'bmm' in op_name.lower():
                if len(shapes) >= 2:
                    shape1, shape2 = shapes[0], shapes[1]
                    if len(shape1) >= 3 and len(shape2) >= 3:
                        b, m, k = shape1[-3], shape1[-2], shape1[-1]
                        n = shape2[-1]
                        return float(b * m * k * n * 2)
        except:
            pass
        return 0.0
    
    def record_op(self, op_name, input_shapes, output_shapes, dtypes, device, phase=None):
        """记录算子调用"""
        try:
            if phase is None:
                phase = self.detect_phase(input_shapes)
            
            memory_before = self.get_memory_info()
            flops = self.estimate_flops(op_name, input_shapes)
            
            op_call = OperatorCall(
                name=op_name,
                input_shapes=input_shapes,
                output_shapes=output_shapes,
                dtypes=dtypes,
                device=device,
                timestamp=time.time() - self.start_time,
                phase=phase,
                memory_before=memory_before,
                memory_after=memory_before,
                flops=flops
            )
            
            with self.lock:
                self.operations.append(op_call)
                
                # 更新计数器
                op_lower = op_name.lower()
                if 'embedding' in op_lower:
                    self.op_counters['embedding'] += 1
                elif 'linear' in op_lower:
                    self.op_counters['linear'] += 1
                elif 'matmul' in op_lower:
                    self.op_counters['matmul'] += 1
                elif 'bmm' in op_lower:
                    self.op_counters['bmm'] += 1
                elif 'int8' in op_lower:
                    self.op_counters['int8_mm'] += 1
                
            # 实时输出关键算子
            if any(key in op_lower for key in ['linear', 'embedding', 'matmul', 'bmm', 'int8']):
                logger.info(f"[{phase}] {op_name} | Input: {input_shapes} | Output: {output_shapes} | FLOPs: {flops:.2e}")
                
        except Exception as e:
            logger.error(f"记录算子错误: {e}")
    
    def hook_linear(self, original_linear):
        """Hook torch.nn.functional.linear"""
        def wrapped_linear(*args, **kwargs):
            result = original_linear(*args, **kwargs)
            try:
                input_shapes = []
                output_shapes = []
                dtypes = []
                device = "unknown"
                
                # 处理输入
                for arg in args:
                    if hasattr(arg, 'shape') and hasattr(arg, 'dtype'):
                        input_shapes.append(list(arg.shape))
                        dtypes.append(str(arg.dtype))
                        device = str(arg.device) if hasattr(arg, 'device') else device
                
                # 处理输出
                if hasattr(result, 'shape'):
                    output_shapes.append(list(result.shape))
                
                self.record_op("torch.nn.functional.linear", input_shapes, output_shapes, dtypes, device)
            except:
                pass
            return result
        return wrapped_linear
    
    def hook_embedding(self, original_embedding):
        """Hook torch.nn.functional.embedding"""
        def wrapped_embedding(*args, **kwargs):
            result = original_embedding(*args, **kwargs)
            try:
                input_shapes = []
                output_shapes = []
                dtypes = []
                device = "unknown"
                
                # 处理输入
                for arg in args:
                    if hasattr(arg, 'shape') and hasattr(arg, 'dtype'):
                        input_shapes.append(list(arg.shape))
                        dtypes.append(str(arg.dtype))
                        device = str(arg.device) if hasattr(arg, 'device') else device
                
                # 处理输出
                if hasattr(result, 'shape'):
                    output_shapes.append(list(result.shape))
                
                self.record_op("torch.nn.functional.embedding", input_shapes, output_shapes, dtypes, device)
            except:
                pass
            return result
        return wrapped_embedding
    
    def hook_matmul(self, original_matmul):
        """Hook torch.matmul"""
        def wrapped_matmul(*args, **kwargs):
            result = original_matmul(*args, **kwargs)
            try:
                input_shapes = []
                output_shapes = []
                dtypes = []
                device = "unknown"
                
                # 处理输入
                for arg in args:
                    if hasattr(arg, 'shape') and hasattr(arg, 'dtype'):
                        input_shapes.append(list(arg.shape))
                        dtypes.append(str(arg.dtype))
                        device = str(arg.device) if hasattr(arg, 'device') else device
                
                # 处理输出
                if hasattr(result, 'shape'):
                    output_shapes.append(list(result.shape))
                
                self.record_op("torch.matmul", input_shapes, output_shapes, dtypes, device)
            except:
                pass
            return result
        return wrapped_matmul
    
    def hook_bmm(self, original_bmm):
        """Hook torch.bmm"""
        def wrapped_bmm(*args, **kwargs):
            result = original_bmm(*args, **kwargs)
            try:
                input_shapes = []
                output_shapes = []
                dtypes = []
                device = "unknown"
                
                # 处理输入
                for arg in args:
                    if hasattr(arg, 'shape') and hasattr(arg, 'dtype'):
                        input_shapes.append(list(arg.shape))
                        dtypes.append(str(arg.dtype))
                        device = str(arg.device) if hasattr(arg, 'device') else device
                
                # 处理输出
                if hasattr(result, 'shape'):
                    output_shapes.append(list(result.shape))
                
                self.record_op("torch.bmm", input_shapes, output_shapes, dtypes, device)
            except:
                pass
            return result
        return wrapped_bmm
    
    def register_hooks(self):
        """注册所有hooks"""
        logger.info("注册算子hooks...")
        
        # 保存原始函数
        self.original_linear = F.linear
        self.original_embedding = F.embedding
        self.original_matmul = torch.matmul
        self.original_bmm = torch.bmm
        
        # 替换为hooked版本
        F.linear = self.hook_linear(self.original_linear)
        F.embedding = self.hook_embedding(self.original_embedding)
        torch.matmul = self.hook_matmul(self.original_matmul)
        torch.bmm = self.hook_bmm(self.original_bmm)
        
        logger.info("算子hooks注册完成")
    
    def restore_hooks(self):
        """恢复原始函数"""
        F.linear = self.original_linear
        F.embedding = self.original_embedding
        torch.matmul = self.original_matmul
        torch.bmm = self.original_bmm
    
    def save_analysis(self):
        """保存分析结果"""
        logger.info("保存分析结果...")
        
        try:
            # 分析数据
            prefill_ops = [op for op in self.operations if op.phase == "prefill"]
            decode_ops = [op for op in self.operations if op.phase == "decode"]
            unknown_ops = [op for op in self.operations if op.phase == "unknown"]
            
            # 统计信息
            analysis = {
                "execution_summary": {
                    "total_operations": len(self.operations),
                    "execution_time_seconds": time.time() - self.start_time,
                    "gpu_count": torch.cuda.device_count() if torch.cuda.is_available() else 0,
                    "prefill_ops": len(prefill_ops),
                    "decode_ops": len(decode_ops),
                    "unknown_ops": len(unknown_ops),
                    "operator_counters": self.op_counters
                },
                "prefill_analysis": self._analyze_phase(prefill_ops),
                "decode_analysis": self._analyze_phase(decode_ops),
                "unknown_analysis": self._analyze_phase(unknown_ops),
                "operator_timeline": [(op.timestamp, op.name, op.phase, op.input_shapes) for op in self.operations[-50:]],
                "sample_operations": [op.to_dict() for op in self.operations[-20:]]
            }
            
            # 保存JSON
            json_file = os.path.join(self.output_dir, "offline_analysis.json")
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(analysis, f, indent=2, ensure_ascii=False)
            
            logger.info(f"分析结果已保存到: {json_file}")
            
            # 创建汇总报告
            self._create_summary_report()
            
            return analysis
            
        except Exception as e:
            logger.error(f"保存分析结果错误: {e}")
            return None
    
    def _analyze_phase(self, ops):
        """分析特定阶段的操作"""
        if not ops:
            return {"operation_count": 0}
        
        op_counts = Counter(op.name for op in ops)
        total_flops = sum(op.flops for op in ops)
        
        return {
            "operation_count": len(ops),
            "total_flops": total_flops,
            "operation_types": dict(op_counts.most_common(10)),
            "avg_memory_usage": sum(op.memory_before for op in ops) / len(ops) if ops else 0,
            "typical_shapes": {
                "input_shapes": [op.input_shapes for op in ops[:5]],
                "output_shapes": [op.output_shapes for op in ops[:5]]
            }
        }
    
    def _create_summary_report(self):
        """创建汇总报告"""
        report_file = os.path.join(self.output_dir, "offline_summary_report.md")
        
        prefill_ops = [op for op in self.operations if op.phase == "prefill"]
        decode_ops = [op for op in self.operations if op.phase == "decode"]
        unknown_ops = [op for op in self.operations if op.phase == "unknown"]
        
        content = f"""# SGLang 离线算子分析报告

## 执行摘要
- 总算子调用次数: {len(self.operations)}
- Prefill阶段算子: {len(prefill_ops)}
- Decode阶段算子: {len(decode_ops)}
- 未分类算子: {len(unknown_ops)}
- 执行时间: {time.time() - self.start_time:.2f}秒

## 算子类型统计
- Embedding操作: {self.op_counters['embedding']}
- Linear操作: {self.op_counters['linear']}
- MatMul操作: {self.op_counters['matmul']}
- BMM操作: {self.op_counters['bmm']}
- INT8 MM操作: {self.op_counters['int8_mm']}

## Prefill阶段分析
{self._format_phase_analysis(prefill_ops)}

## Decode阶段分析
{self._format_phase_analysis(decode_ops)}

## 技术发现
1. 主要计算算子类型及调用次数
2. 不同阶段的算子分布差异
3. 内存使用模式
4. 计算复杂度估算

## 详细算子时间线
{chr(10).join([f"- {op.timestamp:.2f}s: {op.name} ({op.phase}) {op.input_shapes}" for op in self.operations[-10:]])}
"""
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        logger.info(f"汇总报告已保存到: {report_file}")
    
    def _format_phase_analysis(self, ops):
        """格式化阶段分析"""
        if not ops:
            return "无操作记录"
        
        op_counts = Counter(op.name for op in ops)
        total_flops = sum(op.flops for op in ops)
        return f"""
- 算子调用次数: {len(ops)}
- 主要算子类型: {dict(list(op_counts.most_common(3)))}
- 总FLOPs: {total_flops:.2e}
- 平均内存使用: {sum(op.memory_before for op in ops) / len(ops):.2f} GB
"""

def simulate_model_inference():
    """模拟模型推理过程"""
    logger.info("开始模拟模型推理...")
    
    # 模拟不同的算子调用
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    
    # 模拟embedding操作 (prefill阶段)
    logger.info("模拟Prefill阶段...")
    vocab_size = 128256
    embed_dim = 4096
    seq_len = 512
    batch_size = 1
    
    # Embedding lookup
    input_ids = torch.randint(0, vocab_size, (batch_size, seq_len), device=device)
    embedding_weight = torch.randn(vocab_size, embed_dim, device=device, dtype=torch.float16)
    embedded = F.embedding(input_ids, embedding_weight)
    
    # Linear operations (attention, FFN)
    hidden_size = 4096
    intermediate_size = 14336
    
    # Self-attention linear projections
    query_weight = torch.randn(hidden_size, hidden_size, device=device, dtype=torch.float16)
    key_weight = torch.randn(hidden_size, hidden_size, device=device, dtype=torch.float16)
    value_weight = torch.randn(hidden_size, hidden_size, device=device, dtype=torch.float16)
    
    query = F.linear(embedded, query_weight)
    key = F.linear(embedded, key_weight)
    value = F.linear(embedded, value_weight)
    
    # Attention computation
    num_heads = 32
    head_dim = hidden_size // num_heads
    query = query.view(batch_size, seq_len, num_heads, head_dim).transpose(1, 2)
    key = key.view(batch_size, seq_len, num_heads, head_dim).transpose(1, 2)
    value = value.view(batch_size, seq_len, num_heads, head_dim).transpose(1, 2)
    
    # Scaled dot-product attention
    scores = torch.matmul(query, key.transpose(-2, -1)) / (head_dim ** 0.5)
    attn_weights = torch.softmax(scores, dim=-1)
    attn_output = torch.matmul(attn_weights, value)
    
    # Output projection
    output_weight = torch.randn(hidden_size, hidden_size, device=device, dtype=torch.float16)
    attn_output = attn_output.transpose(1, 2).contiguous().view(batch_size, seq_len, hidden_size)
    attn_output = F.linear(attn_output, output_weight)
    
    # FFN
    gate_weight = torch.randn(intermediate_size, hidden_size, device=device, dtype=torch.float16)
    up_weight = torch.randn(intermediate_size, hidden_size, device=device, dtype=torch.float16)
    down_weight = torch.randn(hidden_size, intermediate_size, device=device, dtype=torch.float16)
    
    gate = F.linear(attn_output, gate_weight)
    up = F.linear(attn_output, up_weight)
    gate = torch.nn.functional.silu(gate)
    intermediate = gate * up
    output = F.linear(intermediate, down_weight)
    
    logger.info("Prefill阶段模拟完成")
    
    # 模拟decode阶段 (单token生成)
    logger.info("模拟Decode阶段...")
    seq_len = 1  # decode阶段每次只处理一个token
    
    # 新的input (单token)
    new_input_ids = torch.randint(0, vocab_size, (batch_size, seq_len), device=device)
    new_embedded = F.embedding(new_input_ids, embedding_weight)
    
    # 重复相同的计算但形状不同
    new_query = F.linear(new_embedded, query_weight)
    new_key = F.linear(new_embedded, key_weight)
    new_value = F.linear(new_embedded, value_weight)
    
    # 简化的attention (KV cache场景)
    new_query = new_query.view(batch_size, seq_len, num_heads, head_dim).transpose(1, 2)
    # 在实际decode中，key和value会来自cache
    
    # 简单的矩阵运算模拟
    simple_scores = torch.matmul(new_query, new_query.transpose(-2, -1))
    simple_output = torch.matmul(simple_scores, new_query)
    
    # FFN for decode
    simple_ffn_input = simple_output.transpose(1, 2).contiguous().view(batch_size, seq_len, hidden_size)
    simple_gate = F.linear(simple_ffn_input, gate_weight)
    simple_up = F.linear(simple_ffn_input, up_weight)
    simple_gate = torch.nn.functional.silu(simple_gate)
    simple_intermediate = simple_gate * simple_up
    simple_output = F.linear(simple_intermediate, down_weight)
    
    logger.info("Decode阶段模拟完成")
    
    # 额外的矩阵运算
    logger.info("添加额外的矩阵运算...")
    for i in range(5):
        a = torch.randn(batch_size, 512, 1024, device=device, dtype=torch.float16)
        b = torch.randn(batch_size, 1024, 2048, device=device, dtype=torch.float16)
        c = torch.bmm(a, b)
    
    logger.info("模拟推理完成")

def main():
    """主函数"""
    logger.info("开始离线算子分析...")
    
    # 创建追踪器
    tracker = OfflineOperatorTracker()
    
    try:
        # 注册hooks
        tracker.register_hooks()
        
        # 模拟模型推理
        simulate_model_inference()
        
        # 等待所有算子调用完成
        time.sleep(1)
        
    except Exception as e:
        logger.error(f"执行过程中出错: {e}")
    
    finally:
        # 恢复原始函数
        tracker.restore_hooks()
        
        # 保存分析结果
        analysis = tracker.save_analysis()
        
        if analysis:
            logger.info(f"分析完成！结果保存在: {tracker.output_dir}")
            
            # 输出简要统计
            logger.info(f"捕获到 {len(tracker.operations)} 个算子调用")
            prefill_count = len([op for op in tracker.operations if op.phase == "prefill"])
            decode_count = len([op for op in tracker.operations if op.phase == "decode"])
            unknown_count = len([op for op in tracker.operations if op.phase == "unknown"])
            logger.info(f"Prefill阶段: {prefill_count} 个算子")
            logger.info(f"Decode阶段: {decode_count} 个算子")
            logger.info(f"未分类: {unknown_count} 个算子")
            
            # 输出算子类型统计
            logger.info("算子类型统计:")
            for op_type, count in tracker.op_counters.items():
                logger.info(f"  {op_type}: {count}")

if __name__ == "__main__":
    main()
