#!/usr/bin/env python3
"""
SGLang 单GPU算子分析 - 专门监控prefill和decode阶段
避免多GPU内存平衡问题
"""

import torch
import time
import json
import os
import sys
import traceback
from collections import defaultdict
from typing import Dict, List, Tuple, Any
import functools

class SingleGPUOperatorProfiler:
    def __init__(self):
        self.profiling_data = {
            'prefill_phase': defaultdict(list),
            'decode_phase': defaultdict(list),
            'current_phase': 'unknown',
            'phase_transitions': [],
            'operator_details': defaultdict(list)
        }
        
        self.call_count = 0
        self.current_phase = 'unknown'
        
        # 核心算子监控
        self.target_operators = [
            'torch.nn.functional.linear',
            'torch.matmul',
            'torch.mm', 
            'torch.bmm',
            'torch.addmm',
            'torch.nn.functional.silu',
            'torch.nn.functional.softmax',
            'torch.nn.functional.layer_norm',
            'torch.nn.functional.embedding',
            'torch._C._nn.int8_scaled_mm',
        ]
        
        self.original_functions = {}
        
    def detect_phase(self, input_shape: Tuple[int, ...]) -> str:
        """根据输入shape检测阶段"""
        if len(input_shape) >= 2:
            batch_or_seq_len = input_shape[0]
            
            if batch_or_seq_len > 1:
                new_phase = 'prefill'
            else:
                new_phase = 'decode'
                
            if new_phase != self.current_phase:
                self.profiling_data['phase_transitions'].append({
                    'timestamp': time.time(),
                    'from_phase': self.current_phase,
                    'to_phase': new_phase,
                    'trigger_shape': input_shape,
                    'call_count': self.call_count
                })
                print(f"[PHASE_CHANGE] {self.current_phase} -> {new_phase} | Shape: {input_shape}")
                self.current_phase = new_phase
                
        return self.current_phase
    
    def create_wrapper(self, original_func, func_name: str):
        """创建函数包装器"""
        @functools.wraps(original_func)
        def wrapper(*args, **kwargs):
            self.call_count += 1
            
            start_time = time.perf_counter()
            
            try:
                # 分析输入
                input_shapes = []
                for arg in args:
                    if isinstance(arg, torch.Tensor):
                        input_shapes.append(tuple(arg.shape))
                        
                # 检测阶段
                if input_shapes:
                    phase = self.detect_phase(input_shapes[0])
                else:
                    phase = self.current_phase
                
                # 执行原函数
                result = original_func(*args, **kwargs)
                
                end_time = time.perf_counter()
                execution_time = (end_time - start_time) * 1000
                
                # 分析输出
                output_shapes = []
                if isinstance(result, torch.Tensor):
                    output_shapes.append(tuple(result.shape))
                elif isinstance(result, (list, tuple)):
                    for item in result:
                        if isinstance(item, torch.Tensor):
                            output_shapes.append(tuple(item.shape))
                
                # 记录数据
                record = {
                    'call_id': self.call_count,
                    'timestamp': start_time,
                    'function': func_name,
                    'phase': phase,
                    'execution_time_ms': execution_time,
                    'input_shapes': input_shapes,
                    'output_shapes': output_shapes,
                    'memory_allocated': torch.cuda.memory_allocated() if torch.cuda.is_available() else 0
                }
                
                # 分阶段存储
                if phase == 'prefill':
                    self.profiling_data['prefill_phase'][func_name].append(record)
                elif phase == 'decode':
                    self.profiling_data['decode_phase'][func_name].append(record)
                    
                self.profiling_data['operator_details'][func_name].append(record)
                
                # 打印关键信息
                if input_shapes and output_shapes:
                    print(f"[{phase.upper()}] {func_name}: {input_shapes[0]} -> {output_shapes[0]} | {execution_time:.3f}ms")
                
                return result
                
            except Exception as e:
                print(f"[ERROR] {func_name}: {str(e)}")
                return original_func(*args, **kwargs)
                
        return wrapper
    
    def install_hooks(self):
        """安装监控钩子"""
        print("[INFO] 安装单GPU算子监控钩子...")
        
        for op_name in self.target_operators:
            try:
                parts = op_name.split('.')
                current_module = torch
                
                for part in parts[1:-1]:
                    current_module = getattr(current_module, part)
                
                func_name = parts[-1]
                if hasattr(current_module, func_name):
                    original_func = getattr(current_module, func_name)
                    self.original_functions[op_name] = original_func
                    
                    wrapped_func = self.create_wrapper(original_func, op_name)
                    setattr(current_module, func_name, wrapped_func)
                    print(f"[HOOKED] {op_name}")
                    
            except Exception as e:
                print(f"[HOOK_FAILED] {op_name}: {str(e)}")
    
    def remove_hooks(self):
        """移除监控钩子"""
        print("[INFO] 移除算子监控钩子...")
        
        for op_name, original_func in self.original_functions.items():
            try:
                parts = op_name.split('.')
                current_module = torch
                
                for part in parts[1:-1]:
                    current_module = getattr(current_module, part)
                
                func_name = parts[-1]
                setattr(current_module, func_name, original_func)
                
            except Exception as e:
                print(f"[UNHOOK_FAILED] {op_name}: {str(e)}")
                
        self.original_functions.clear()
    
    def generate_report(self) -> Dict[str, Any]:
        """生成分析报告"""
        report = {
            'summary': {
                'total_calls': self.call_count,
                'prefill_calls': sum(len(calls) for calls in self.profiling_data['prefill_phase'].values()),
                'decode_calls': sum(len(calls) for calls in self.profiling_data['decode_phase'].values()),
                'phase_transitions': len(self.profiling_data['phase_transitions'])
            },
            'phase_analysis': {},
            'raw_data': {
                'prefill_phase': dict(self.profiling_data['prefill_phase']),
                'decode_phase': dict(self.profiling_data['decode_phase']),
                'phase_transitions': self.profiling_data['phase_transitions']
            }
        }
        
        # 分析各阶段
        for phase in ['prefill', 'decode']:
            phase_data = self.profiling_data[f'{phase}_phase']
            
            if not phase_data:
                continue
                
            phase_analysis = {
                'operator_count': len(phase_data),
                'total_calls': sum(len(calls) for calls in phase_data.values()),
                'total_time': 0,
                'operators': {}
            }
            
            for op_name, calls in phase_data.items():
                times = [call['execution_time_ms'] for call in calls]
                shapes = list(set(str(call['input_shapes']) for call in calls))
                
                op_stats = {
                    'call_count': len(calls),
                    'total_time': sum(times),
                    'avg_time': sum(times) / len(times) if times else 0,
                    'max_time': max(times) if times else 0,
                    'shapes': shapes
                }
                
                phase_analysis['operators'][op_name] = op_stats
                phase_analysis['total_time'] += op_stats['total_time']
                
            report['phase_analysis'][phase] = phase_analysis
            
        return report
    
    def save_results(self, filename: str):
        """保存结果"""
        report = self.generate_report()
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False, default=str)
            
        print(f"[INFO] 结果已保存到: {filename}")
        return report


def run_single_gpu_analysis():
    """运行单GPU分析"""
    print("="*80)
    print("SGLang 单GPU算子分析 - Prefill/Decode专项")
    print("="*80)
    
    profiler = SingleGPUOperatorProfiler()
    
    try:
        # 安装监控钩子
        profiler.install_hooks()
        
        print("\n[INFO] 启动SGLang引擎...")
        
        sys.path.append('/workspace/sglang_test/lib/python3.10/site-packages')
        import sglang as sgl
        
        # 使用单GPU配置
        engine = sgl.Engine(
            model_path="/home/<USER>/deepseek-int8",
            tokenizer_path="/home/<USER>/deepseek-int8",
            tp_size=1,  # 单GPU
            trust_remote_code=True,
            quantization="fp8",
            mem_fraction_static=0.8
        )
        
        print("[INFO] 引擎启动完成，开始分析...")
        
        # 测试用例
        test_prompts = [
            "你好",
            "请解释深度学习的基本概念",
            "请详细介绍Transformer架构的注意力机制，包括自注意力和交叉注意力的工作原理"
        ]
        
        for i, prompt in enumerate(test_prompts):
            print(f"\n[TEST {i+1}] 输入: {prompt[:30]}...")
            
            # 重置阶段
            profiler.current_phase = 'unknown'
            
            # 生成响应
            response = engine.generate(
                prompt,
                max_new_tokens=50,
                temperature=0.1
            )
            
            print(f"输出: {response['text'][:50]}...")
            
        print("\n[INFO] 分析完成")
        
    except Exception as e:
        print(f"[ERROR] 运行错误: {str(e)}")
        traceback.print_exc()
        
    finally:
        # 清理
        profiler.remove_hooks()
        
        # 保存结果
        timestamp = int(time.time())
        filename = f"single_gpu_operator_analysis_{timestamp}.json"
        report = profiler.save_results(filename)
        
        # 打印摘要
        print("\n" + "="*80)
        print("单GPU算子分析摘要")
        print("="*80)
        
        summary = report['summary']
        print(f"总调用次数: {summary['total_calls']}")
        print(f"Prefill调用: {summary['prefill_calls']}")
        print(f"Decode调用: {summary['decode_calls']}")
        print(f"阶段转换: {summary['phase_transitions']}")
        
        for phase in ['prefill', 'decode']:
            if phase in report['phase_analysis']:
                data = report['phase_analysis'][phase]
                print(f"\n{phase.capitalize()}阶段:")
                print(f"  算子类型: {data['operator_count']}")
                print(f"  总调用: {data['total_calls']}")
                print(f"  总时间: {data['total_time']:.2f}ms")
                
                # 显示主要算子
                if data['operators']:
                    print(f"  主要算子:")
                    sorted_ops = sorted(
                        data['operators'].items(),
                        key=lambda x: x[1]['total_time'],
                        reverse=True
                    )[:5]
                    
                    for op_name, stats in sorted_ops:
                        print(f"    {op_name}: {stats['call_count']}次, {stats['total_time']:.2f}ms")
        
        print(f"\n详细结果: {filename}")


if __name__ == "__main__":
    run_single_gpu_analysis()
