#!/usr/bin/env python3
"""
单卡深度算子追踪脚本
用于追踪SGLang模型加载和推理过程中的具体算子运算
"""
import os
import sys
import time
import json
import torch
import traceback
import functools
from typing import Dict, List, Any, Tuple
from collections import defaultdict
import inspect

# 环境配置 - 强制单卡运行
os.environ.setdefault("CUDA_VISIBLE_DEVICES", "0")
os.environ.setdefault("SGL_DISABLE_TP_MEMORY_INBALANCE_CHECK", "1")
os.environ.setdefault("UCX_VFS_ENABLE", "n")
os.environ.setdefault("NCCL_P2P_DISABLE", "1")
os.environ.setdefault("NCCL_IB_DISABLE", "1")
os.environ.setdefault("SGLANG_IS_FLASHINFER_AVAILABLE", "false")

# 全局算子追踪记录
operator_calls = []
layer_operations = defaultdict(list)
memory_timeline = []
torch_ops_count = defaultdict(int)
custom_ops_count = defaultdict(int)
timing_data = {}

class DeepOperatorTracker:
    """深度算子追踪器"""
    
    def __init__(self):
        self.start_time = None
        self.enabled = False
        self.hooked_functions = {}
        self.call_stack_depth = 0
        self.max_depth = 3  # 限制追踪深度避免过多输出
        
    def enable(self):
        """启用深度追踪"""
        self.enabled = True
        self.start_time = time.time()
        print("[DeepTracker] 启用深度算子追踪 (单卡模式)")
        
        # Hook核心PyTorch操作
        self._hook_torch_operations()
        
        # Hook自定义操作
        self._hook_custom_operations()
    
    def disable(self):
        """禁用追踪"""
        self.enabled = False
        self._restore_functions()
        print("[DeepTracker] 禁用算子追踪")
    
    def _hook_torch_operations(self):
        """Hook PyTorch核心操作"""
        # 矩阵运算
        matrix_ops = ['mm', 'bmm', 'matmul', 'addmm', 'addmv', 'mv']
        # 线性层操作
        linear_ops = ['linear', 'conv1d', 'conv2d']
        # 激活函数
        activation_ops = ['relu', 'gelu', 'silu', 'tanh', 'sigmoid', 'softmax']
        # 注意力相关
        attention_ops = ['scaled_dot_product_attention']
        # 其他重要操作
        other_ops = ['embedding', 'layer_norm', 'group_norm', 'dropout']
        
        all_ops = matrix_ops + linear_ops + activation_ops + attention_ops + other_ops
        
        # Hook torch函数
        for op_name in all_ops:
            if hasattr(torch, op_name):
                self._hook_function(torch, op_name, f"torch.{op_name}")
        
        # Hook torch.nn.functional函数  
        if hasattr(torch.nn, 'functional'):
            F = torch.nn.functional
            for op_name in linear_ops + activation_ops + other_ops:
                if hasattr(F, op_name):
                    self._hook_function(F, op_name, f"F.{op_name}")
    
    def _hook_custom_operations(self):
        """Hook自定义算子（后续在模型加载后执行）"""
        pass
    
    def _hook_function(self, module, func_name, display_name):
        """Hook特定函数"""
        if hasattr(module, func_name):
            original_func = getattr(module, func_name)
            if original_func not in self.hooked_functions.values():
                wrapped_func = self._create_wrapper(display_name, original_func)
                setattr(module, func_name, wrapped_func)
                self.hooked_functions[display_name] = original_func
    
    def _create_wrapper(self, op_name, original_func):
        """创建函数包装器"""
        @functools.wraps(original_func)
        def wrapper(*args, **kwargs):
            if not self.enabled or self.call_stack_depth > self.max_depth:
                return original_func(*args, **kwargs)
            
            self.call_stack_depth += 1
            start_time = time.time()
            
            try:
                # 分析输入
                input_info = self._analyze_inputs(args, kwargs)
                
                # 执行原始函数
                result = original_func(*args, **kwargs)
                
                # 分析输出
                output_info = self._analyze_outputs(result)
                
                end_time = time.time()
                execution_time = end_time - start_time
                
                # 记录操作
                operation_record = {
                    "operator": op_name,
                    "timestamp": time.time(),
                    "relative_time": time.time() - self.start_time,
                    "execution_time": execution_time,
                    "input_info": input_info,
                    "output_info": output_info,
                    "call_depth": self.call_stack_depth
                }
                
                operator_calls.append(operation_record)
                torch_ops_count[op_name] += 1
                
                # 只打印重要操作避免输出过多
                if execution_time > 0.001 or any(keyword in op_name.lower() for keyword in ['mm', 'linear', 'attention', 'conv']):
                    print(f"[Op] {op_name}: {input_info.get('main_shape', 'N/A')} -> {output_info.get('main_shape', 'N/A')} ({execution_time:.4f}s)")
                
                return result
                
            except Exception as e:
                print(f"[OpError] {op_name}: {e}")
                return original_func(*args, **kwargs)
            finally:
                self.call_stack_depth -= 1
        
        return wrapper
    
    def _analyze_inputs(self, args, kwargs):
        """分析输入参数"""
        input_info = {"arg_count": len(args), "kwarg_count": len(kwargs)}
        
        # 找到主要的张量输入
        main_tensors = []
        for arg in args:
            if isinstance(arg, torch.Tensor):
                main_tensors.append({
                    "shape": list(arg.shape),
                    "dtype": str(arg.dtype),
                    "device": str(arg.device)
                })
        
        if main_tensors:
            input_info["tensors"] = main_tensors
            input_info["main_shape"] = main_tensors[0]["shape"]  # 第一个张量作为主要形状
        
        return input_info
    
    def _analyze_outputs(self, result):
        """分析输出结果"""
        output_info = {}
        
        if isinstance(result, torch.Tensor):
            output_info = {
                "type": "single_tensor",
                "shape": list(result.shape),
                "dtype": str(result.dtype),
                "main_shape": list(result.shape)
            }
        elif isinstance(result, (tuple, list)):
            tensor_outputs = []
            for item in result:
                if isinstance(item, torch.Tensor):
                    tensor_outputs.append({
                        "shape": list(item.shape),
                        "dtype": str(item.dtype)
                    })
            if tensor_outputs:
                output_info = {
                    "type": "multiple_tensors", 
                    "tensors": tensor_outputs,
                    "main_shape": tensor_outputs[0]["shape"] if tensor_outputs else None
                }
        
        return output_info
    
    def _restore_functions(self):
        """恢复被Hook的函数"""
        for display_name, original_func in self.hooked_functions.items():
            # 这里需要根据display_name反向恢复，实际实现可能需要更复杂的逻辑
            pass

def hook_model_layers(model, tracker):
    """为模型层注册Hook"""
    print("[LayerHook] 开始为模型层注册Hook...")
    
    def create_layer_hook(layer_name):
        def hook_fn(module, input, output):
            if not tracker.enabled:
                return
            
            try:
                # 分析层级操作
                input_shapes = []
                if isinstance(input, (tuple, list)):
                    for inp in input:
                        if isinstance(inp, torch.Tensor):
                            input_shapes.append(list(inp.shape))
                elif isinstance(input, torch.Tensor):
                    input_shapes = [list(input.shape)]
                
                output_shapes = []
                if isinstance(output, torch.Tensor):
                    output_shapes = [list(output.shape)]
                elif isinstance(output, (tuple, list)):
                    for out in output:
                        if isinstance(out, torch.Tensor):
                            output_shapes.append(list(out.shape))
                
                layer_record = {
                    "layer_name": layer_name,
                    "module_type": type(module).__name__,
                    "timestamp": time.time(),
                    "relative_time": time.time() - tracker.start_time,
                    "input_shapes": input_shapes,
                    "output_shapes": output_shapes
                }
                
                layer_operations[layer_name].append(layer_record)
                
                print(f"[Layer] {layer_name} ({type(module).__name__}): {input_shapes} -> {output_shapes}")
                
            except Exception as e:
                print(f"[LayerHookError] {layer_name}: {e}")
        
        return hook_fn
    
    # 为关键层注册Hook
    hook_count = 0
    for name, module in model.named_modules():
        # 只Hook关键层，避免过多输出
        if any(keyword in name.lower() for keyword in [
            'attention', 'attn', 'mlp', 'feed_forward', 'moe', 
            'linear', 'conv', 'embedding', 'norm', 'gate'
        ]):
            module.register_forward_hook(create_layer_hook(name))
            hook_count += 1
            if hook_count <= 10:  # 限制显示数量
                print(f"[LayerHook] 注册: {name}")
    
    print(f"[LayerHook] 总共注册了 {hook_count} 个层级Hook")

def monitor_memory(stage_name):
    """监控显存使用"""
    if torch.cuda.is_available():
        allocated = torch.cuda.memory_allocated(0) / (1024**3)  # GB
        reserved = torch.cuda.memory_reserved(0) / (1024**3)   # GB
        max_allocated = torch.cuda.max_memory_allocated(0) / (1024**3)
        
        memory_record = {
            "stage": stage_name,
            "timestamp": time.time(),
            "allocated_gb": allocated,
            "reserved_gb": reserved,
            "max_allocated_gb": max_allocated
        }
        
        memory_timeline.append(memory_record)
        print(f"[Memory] {stage_name}: {allocated:.2f}GB allocated, {reserved:.2f}GB reserved")

def test_single_card_sglang():
    """单卡SGLang测试"""
    MODEL_PATH = os.environ.get("MODEL_PATH", "/home/<USER>/deepseek-int8")
    
    tracker = DeepOperatorTracker()
    tracker.enable()
    
    monitor_memory("initial")
    
    llm = None
    try:
        print("\n[SGLang] 单卡模式初始化...")
        timing_data["start_time"] = time.time()
        
        # 导入SGLang
        import sglang as sgl
        timing_data["import_time"] = time.time() - timing_data["start_time"]
        monitor_memory("after_import")
        
        # 创建单卡引擎
        print("[SGLang] 创建单卡引擎...")
        engine_start = time.time()
        
        llm = sgl.Engine(
            model_path=MODEL_PATH,
            tp_size=1,  # 单卡模式
            quantization="w8a8_int8",
            trust_remote_code=True,
            disable_cuda_graph=True,
            log_level="warning",
            max_running_requests=1,  # 减少并发
        )
        
        timing_data["engine_creation_time"] = time.time() - engine_start
        monitor_memory("after_engine_creation")
        
        # 为模型注册层级Hook
        if hasattr(llm, 'model'):
            hook_model_layers(llm.model, tracker)
        elif hasattr(llm, 'runner') and hasattr(llm.runner, 'model'):
            hook_model_layers(llm.runner.model, tracker)
        
        # 执行推理测试
        print("\n[SGLang] 开始推理测试...")
        inference_start = time.time()
        
        prompt = "深度学习中的注意力机制是什么？"
        sampling_params = {
            "max_new_tokens": 16,  # 减少token数量获得更清晰的追踪
            "temperature": 0.7
        }
        
        result = llm.generate(prompt=prompt, sampling_params=sampling_params)
        
        timing_data["inference_time"] = time.time() - inference_start
        monitor_memory("after_inference")
        
        print(f"[Result] 推理结果: {result.get('text', result)}")
        
        return True
        
    except Exception as e:
        print(f"[Error] 单卡测试失败: {e}")
        traceback.print_exc()
        return False
        
    finally:
        tracker.disable()
        
        if llm is not None:
            try:
                llm.shutdown()
                monitor_memory("after_cleanup")
            except Exception as e:
                print(f"[Cleanup] 清理失败: {e}")

def analyze_operator_patterns():
    """分析算子模式"""
    print("\n[Analysis] 分析算子调用模式...")
    
    if not operator_calls:
        print("[Analysis] 没有算子调用记录")
        return {}
    
    # 按算子类型分类
    operator_types = {
        "matrix_ops": [],
        "linear_ops": [],
        "activation_ops": [], 
        "attention_ops": [],
        "other_ops": []
    }
    
    for op in operator_calls:
        op_name = op["operator"].lower()
        if any(keyword in op_name for keyword in ['mm', 'matmul', 'mv']):
            operator_types["matrix_ops"].append(op)
        elif any(keyword in op_name for keyword in ['linear', 'conv']):
            operator_types["linear_ops"].append(op)
        elif any(keyword in op_name for keyword in ['relu', 'gelu', 'silu', 'softmax', 'tanh']):
            operator_types["activation_ops"].append(op)
        elif 'attention' in op_name:
            operator_types["attention_ops"].append(op)
        else:
            operator_types["other_ops"].append(op)
    
    # 统计信息
    analysis_results = {
        "total_operations": len(operator_calls),
        "operator_types_count": {k: len(v) for k, v in operator_types.items()},
        "torch_ops_frequency": dict(torch_ops_count),
        "timing_analysis": timing_data,
        "memory_timeline": memory_timeline,
        "layer_operations_count": len(layer_operations),
        "most_common_shapes": {}
    }
    
    # 分析最常见的形状
    shape_counts = defaultdict(int)
    for op in operator_calls:
        if "main_shape" in op.get("input_info", {}):
            shape = tuple(op["input_info"]["main_shape"])
            shape_counts[shape] += 1
    
    analysis_results["most_common_shapes"] = dict(sorted(shape_counts.items(), key=lambda x: x[1], reverse=True)[:10])
    
    return analysis_results

def generate_detailed_report(analysis_results):
    """生成详细报告"""
    print("\n[Report] 生成单卡算子追踪详细报告...")
    
    # 保存原始数据
    detailed_data = {
        "operator_calls": operator_calls,
        "layer_operations": dict(layer_operations),
        "analysis_results": analysis_results,
        "generation_time": time.strftime("%Y-%m-%d %H:%M:%S")
    }
    
    with open("single_card_detailed_tracking.json", 'w', encoding='utf-8') as f:
        json.dump(detailed_data, f, indent=2, ensure_ascii=False, default=str)
    
    # 生成Markdown报告
    report = []
    report.append("# 单卡深度算子追踪分析报告\n")
    report.append(f"## 生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
    
    # 总体统计
    report.append("## 总体统计")
    if analysis_results:
        report.append(f"- 总算子调用: {analysis_results.get('total_operations', 0)}")
        report.append(f"- 层级操作: {analysis_results.get('layer_operations_count', 0)}")
        
        types_count = analysis_results.get('operator_types_count', {})
        for op_type, count in types_count.items():
            report.append(f"- {op_type}: {count}")
        report.append("")
    
    # 性能分析
    if timing_data:
        report.append("## 性能分析")
        for key, value in timing_data.items():
            if isinstance(value, float):
                report.append(f"- {key}: {value:.3f}秒")
        report.append("")
    
    # 算子频率分析
    if torch_ops_count:
        report.append("## 高频算子TOP10")
        sorted_ops = sorted(torch_ops_count.items(), key=lambda x: x[1], reverse=True)
        for op_name, count in sorted_ops[:10]:
            report.append(f"- {op_name}: {count}次")
        report.append("")
    
    # 形状分析
    common_shapes = analysis_results.get("most_common_shapes", {})
    if common_shapes:
        report.append("## 常见张量形状TOP10")
        for shape, count in list(common_shapes.items())[:10]:
            report.append(f"- {list(shape)}: {count}次")
        report.append("")
    
    # 内存使用
    if memory_timeline:
        report.append("## 内存使用时间线")
        for mem_record in memory_timeline:
            stage = mem_record["stage"]
            allocated = mem_record["allocated_gb"]
            report.append(f"- {stage}: {allocated:.2f}GB")
        report.append("")
    
    # 层级操作概览
    if layer_operations:
        report.append("## 层级操作概览")
        for layer_name, ops in list(layer_operations.items())[:15]:  # 只显示前15个
            if ops:
                first_op = ops[0]
                report.append(f"### {layer_name}")
                report.append(f"- 模块类型: {first_op.get('module_type', 'Unknown')}")
                report.append(f"- 调用次数: {len(ops)}")
                if first_op.get('input_shapes'):
                    report.append(f"- 输入形状: {first_op['input_shapes']}")
                if first_op.get('output_shapes'):
                    report.append(f"- 输出形状: {first_op['output_shapes']}")
                report.append("")
    
    # 保存报告
    with open("single_card_tracking_report.md", 'w', encoding='utf-8') as f:
        f.write('\n'.join(report))
    
    print("[Report] 报告已生成:")
    print("  - 详细数据: single_card_detailed_tracking.json")
    print("  - 分析报告: single_card_tracking_report.md")

def main():
    """主函数"""
    print("="*80)
    print("单卡深度算子追踪分析")
    print("="*80)
    
    # 确认单卡环境
    print(f"[Config] CUDA设备: {os.environ.get('CUDA_VISIBLE_DEVICES', 'ALL')}")
    print(f"[Config] 可用GPU数量: {torch.cuda.device_count()}")
    
    # 执行单卡测试
    success = test_single_card_sglang()
    
    # 分析算子模式
    analysis_results = analyze_operator_patterns()
    
    # 生成详细报告
    generate_detailed_report(analysis_results)
    
    print("\n" + "="*80)
    if success:
        print("单卡深度算子追踪分析完成！")
        print(f"总共追踪到 {len(operator_calls)} 个算子调用")
        print(f"记录了 {len(layer_operations)} 个层级操作")
    else:
        print("分析过程中遇到错误，但已生成可用数据")
    print("="*80)

if __name__ == "__main__":
    main()
