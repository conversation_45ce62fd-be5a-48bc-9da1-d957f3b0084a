#!/usr/bin/env python3
"""
SGLang模型对象探索和算子追踪脚本
通过反射机制找到模型对象并进行算子追踪
"""
import os
import sys
import time
import json
import torch
import traceback
import inspect
from typing import Dict, List, Any
from collections import defaultdict

# 单卡环境配置
os.environ.setdefault("CUDA_VISIBLE_DEVICES", "0")
os.environ.setdefault("SGL_DISABLE_TP_MEMORY_INBALANCE_CHECK", "1")
os.environ.setdefault("UCX_VFS_ENABLE", "n")
os.environ.setdefault("NCCL_P2P_DISABLE", "1")
os.environ.setdefault("NCCL_IB_DISABLE", "1")
os.environ.setdefault("SGLANG_IS_FLASHINFER_AVAILABLE", "false")

# 全局数据收集
operation_records = []
model_exploration_log = []
tensor_flow_records = []

def explore_object_structure(obj, name="root", max_depth=3, current_depth=0):
    """递归探索对象结构"""
    if current_depth >= max_depth:
        return {"name": name, "type": str(type(obj)), "truncated": True}
    
    result = {
        "name": name,
        "type": str(type(obj)),
        "attributes": {},
        "methods": []
    }
    
    try:
        # 获取属性
        for attr_name in dir(obj):
            if not attr_name.startswith('_') and current_depth < 2:  # 限制深度
                try:
                    attr_value = getattr(obj, attr_name)
                    attr_type = type(attr_value).__name__
                    
                    # 特别关注模型相关的属性
                    if any(keyword in attr_name.lower() for keyword in [
                        'model', 'runner', 'worker', 'executor', 'engine'
                    ]):
                        result["attributes"][attr_name] = {
                            "type": attr_type,
                            "details": explore_object_structure(attr_value, attr_name, max_depth, current_depth + 1)
                        }
                    elif attr_type in ['str', 'int', 'float', 'bool']:
                        result["attributes"][attr_name] = {"type": attr_type, "value": str(attr_value)}
                    else:
                        result["attributes"][attr_name] = {"type": attr_type}
                        
                except Exception:
                    result["attributes"][attr_name] = {"type": "Error", "value": "Cannot access"}
        
        # 获取关键方法
        for method_name in dir(obj):
            if not method_name.startswith('_') and callable(getattr(obj, method_name, None)):
                if any(keyword in method_name.lower() for keyword in [
                    'forward', 'generate', 'run', 'execute', 'infer'
                ]):
                    result["methods"].append(method_name)
    
    except Exception as e:
        result["error"] = str(e)
    
    return result

def find_model_object(engine_obj):
    """找到真正的模型对象"""
    print("\n[Explorer] 探索SGLang引擎结构...")
    
    # 探索引擎对象结构
    engine_structure = explore_object_structure(engine_obj, "sgl.Engine", max_depth=4)
    model_exploration_log.append(engine_structure)
    
    # 尝试多种路径获取模型
    possible_paths = [
        # 直接路径
        ["model"],
        ["runner", "model"],  
        ["engine", "model"],
        ["worker", "model"],
        ["model_runner", "model"],
        
        # 深层路径
        ["engine", "model_executor", "driver_worker", "model_runner", "model"],
        ["runner", "model_runner", "model"],
        ["server", "model_runner", "model"],
        ["tp_worker", "model_runner", "model"],
        
        # SGLang特有路径
        ["data_parallel_controller", "model_runner", "model"],
        ["scheduler", "model_runner", "model"],
    ]
    
    for path in possible_paths:
        try:
            current_obj = engine_obj
            path_str = " -> ".join(path)
            
            for attr in path:
                if hasattr(current_obj, attr):
                    current_obj = getattr(current_obj, attr)
                else:
                    current_obj = None
                    break
            
            if current_obj is not None and hasattr(current_obj, 'forward'):
                print(f"[Explorer] 找到模型对象: {path_str}")
                print(f"[Explorer] 模型类型: {type(current_obj).__name__}")
                return current_obj, path_str
                
        except Exception as e:
            print(f"[Explorer] 路径 {path_str} 失败: {e}")
            continue
    
    # 如果直接路径都失败，尝试通过属性搜索
    print("[Explorer] 直接路径失败，尝试属性搜索...")
    
    def search_for_model(obj, visited=None, depth=0):
        if visited is None:
            visited = set()
        if id(obj) in visited or depth > 3:
            return None
        visited.add(id(obj))
        
        # 检查当前对象是否是模型
        if hasattr(obj, 'forward') and hasattr(obj, 'named_modules'):
            return obj
        
        # 搜索属性
        for attr_name in dir(obj):
            if not attr_name.startswith('_'):
                try:
                    attr_value = getattr(obj, attr_name)
                    if hasattr(attr_value, 'forward') and hasattr(attr_value, 'named_modules'):
                        return attr_value
                    elif depth < 2:  # 递归搜索
                        result = search_for_model(attr_value, visited, depth + 1)
                        if result is not None:
                            return result
                except Exception:
                    continue
        
        return None
    
    model = search_for_model(engine_obj)
    if model is not None:
        print(f"[Explorer] 通过搜索找到模型: {type(model).__name__}")
        return model, "searched"
    
    print("[Explorer] 未能找到模型对象")
    return None, None

def detailed_model_analysis(model):
    """详细分析模型结构"""
    print("\n[Analyzer] 开始详细模型分析...")
    
    analysis = {
        "model_type": type(model).__name__,
        "modules": {},
        "key_layers": [],
        "parameter_summary": {},
        "computational_graph": []
    }
    
    total_params = 0
    layer_types = defaultdict(int)
    
    # 分析所有模块
    for name, module in model.named_modules():
        module_type = type(module).__name__
        layer_types[module_type] += 1
        
        # 统计参数
        module_params = sum(p.numel() for p in module.parameters(recurse=False))
        total_params += module_params
        
        # 记录关键层
        if any(keyword in name.lower() for keyword in [
            'attention', 'attn', 'mlp', 'moe', 'expert', 'gate', 
            'linear', 'embed', 'norm', 'conv'
        ]) and module_params > 0:
            
            layer_info = {
                "name": name,
                "type": module_type,
                "parameters": module_params,
                "parameter_shapes": []
            }
            
            # 记录参数形状
            for param_name, param in module.named_parameters(recurse=False):
                if param is not None:
                    layer_info["parameter_shapes"].append({
                        "name": param_name,
                        "shape": list(param.shape),
                        "dtype": str(param.dtype)
                    })
            
            analysis["key_layers"].append(layer_info)
            
            # 打印重要层信息
            if len(analysis["key_layers"]) <= 20:  # 限制输出
                print(f"[Layer] {name}: {module_type} ({module_params:,} params)")
                for param_info in layer_info["parameter_shapes"]:
                    print(f"  {param_info['name']}: {param_info['shape']}")
    
    analysis["parameter_summary"] = {
        "total_parameters": total_params,
        "layer_type_counts": dict(layer_types)
    }
    
    print(f"[Analyzer] 总参数量: {total_params:,}")
    print(f"[Analyzer] 关键层数量: {len(analysis['key_layers'])}")
    
    return analysis

def setup_computation_hooks(model):
    """设置计算追踪hooks"""
    print("\n[Hooks] 设置计算追踪hooks...")
    
    hooks = []
    
    def create_computation_hook(layer_name):
        def hook_fn(module, input, output):
            try:
                timestamp = time.time()
                
                # 记录计算信息
                computation_record = {
                    "layer_name": layer_name,
                    "module_type": type(module).__name__,
                    "timestamp": timestamp,
                    "input_shapes": [],
                    "output_shapes": [],
                    "computation_type": "forward"
                }
                
                # 分析输入形状
                if isinstance(input, (tuple, list)):
                    for inp in input:
                        if isinstance(inp, torch.Tensor):
                            computation_record["input_shapes"].append(list(inp.shape))
                elif isinstance(input, torch.Tensor):
                    computation_record["input_shapes"] = [list(input.shape)]
                
                # 分析输出形状
                if isinstance(output, torch.Tensor):
                    computation_record["output_shapes"] = [list(output.shape)]
                elif isinstance(output, (tuple, list)):
                    for out in output:
                        if isinstance(out, torch.Tensor):
                            computation_record["output_shapes"].append(list(out.shape))
                
                operation_records.append(computation_record)
                
                # 判断是否是关键计算
                is_key_computation = any(keyword in layer_name.lower() for keyword in [
                    'attn', 'attention', 'mlp', 'moe', 'linear', 'conv'
                ]) and computation_record["input_shapes"]
                
                if is_key_computation:
                    print(f"[Compute] {layer_name}: {computation_record['input_shapes']} -> {computation_record['output_shapes']}")
                
            except Exception as e:
                print(f"[HookError] {layer_name}: {e}")
        
        return hook_fn
    
    # 为关键计算层设置hooks
    hook_count = 0
    for name, module in model.named_modules():
        # 只Hook真正的计算层
        if any(keyword in name.lower() for keyword in [
            'self_attn', 'attention', 'mlp', 'feed_forward', 
            'moe', 'expert', 'gate_proj', 'up_proj', 'down_proj',
            'q_proj', 'k_proj', 'v_proj', 'o_proj'
        ]) and any(keyword in type(module).__name__.lower() for keyword in [
            'linear', 'attention', 'mlp', 'moe'
        ]):
            hook = module.register_forward_hook(create_computation_hook(name))
            hooks.append(hook)
            hook_count += 1
    
    print(f"[Hooks] 设置了 {hook_count} 个计算hooks")
    return hooks

def run_comprehensive_analysis():
    """运行综合分析"""
    MODEL_PATH = os.environ.get("MODEL_PATH", "/home/<USER>/deepseek-int8")
    
    print("="*80)
    print("SGLang综合算子分析")
    print("="*80)
    
    llm = None
    hooks = []
    
    try:
        print("\n[SGLang] 初始化引擎...")
        
        import sglang as sgl
        
        llm = sgl.Engine(
            model_path=MODEL_PATH,
            tp_size=1,
            quantization="w8a8_int8",
            trust_remote_code=True,
            disable_cuda_graph=True,
            log_level="warning",
        )
        
        print("[SGLang] 引擎初始化完成")
        
        # 探索并找到模型对象
        model, model_path = find_model_object(llm)
        
        if model is None:
            print("[Error] 无法找到模型对象，结束分析")
            return False
        
        # 详细分析模型
        model_analysis = detailed_model_analysis(model)
        
        # 设置计算hooks
        hooks = setup_computation_hooks(model)
        
        # 执行推理测试
        print("\n[Test] 执行推理测试...")
        
        prompt = "Transformer是什么？"
        sampling_params = {
            "max_new_tokens": 5,  # 很少的tokens以获得清晰的追踪
            "temperature": 0.0
        }
        
        start_time = time.time()
        result = llm.generate(prompt=prompt, sampling_params=sampling_params)
        inference_time = time.time() - start_time
        
        print(f"\n[Result] 推理完成 ({inference_time:.3f}s)")
        print(f"[Result] 输出: {result.get('text', result)}")
        
        # 分析结果
        analysis_results = {
            "model_analysis": model_analysis,
            "computation_records": operation_records,
            "model_exploration": model_exploration_log,
            "inference_time": inference_time,
            "total_computations": len(operation_records)
        }
        
        # 生成报告
        generate_final_report(analysis_results)
        
        return True
        
    except Exception as e:
        print(f"[Error] 分析失败: {e}")
        traceback.print_exc()
        return False
        
    finally:
        # 清理hooks
        for hook in hooks:
            hook.remove()
        
        if llm is not None:
            try:
                llm.shutdown()
            except Exception:
                pass

def generate_final_report(analysis_results):
    """生成最终报告"""
    print("\n[Report] 生成最终分析报告...")
    
    # 保存详细数据
    with open("comprehensive_sglang_analysis.json", 'w', encoding='utf-8') as f:
        json.dump(analysis_results, f, indent=2, ensure_ascii=False, default=str)
    
    # 生成Markdown报告
    report = []
    report.append("# SGLang综合算子分析报告\n")
    report.append(f"## 生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
    
    model_analysis = analysis_results.get("model_analysis", {})
    computation_records = analysis_results.get("computation_records", [])
    
    # 模型基本信息
    if model_analysis:
        report.append("## 模型基本信息")
        report.append(f"- 模型类型: {model_analysis.get('model_type', 'Unknown')}")
        
        param_summary = model_analysis.get("parameter_summary", {})
        report.append(f"- 总参数量: {param_summary.get('total_parameters', 0):,}")
        report.append(f"- 关键层数量: {len(model_analysis.get('key_layers', []))}")
        
        layer_counts = param_summary.get("layer_type_counts", {})
        report.append("- 层类型分布:")
        for layer_type, count in sorted(layer_counts.items(), key=lambda x: x[1], reverse=True)[:10]:
            report.append(f"  - {layer_type}: {count}")
        report.append("")
    
    # 计算分析
    if computation_records:
        report.append("## 计算分析")
        report.append(f"- 总计算操作: {len(computation_records)}")
        
        # 按层类型统计
        layer_type_ops = defaultdict(int)
        for record in computation_records:
            layer_type_ops[record.get("module_type", "Unknown")] += 1
        
        report.append("- 计算操作分布:")
        for module_type, count in sorted(layer_type_ops.items(), key=lambda x: x[1], reverse=True):
            report.append(f"  - {module_type}: {count}次")
        report.append("")
    
    # 关键层详情
    key_layers = model_analysis.get("key_layers", [])
    if key_layers:
        report.append("## 关键层详情")
        
        # 按参数量排序，显示Top 15
        sorted_layers = sorted(key_layers, key=lambda x: x.get("parameters", 0), reverse=True)
        
        report.append("| 层名称 | 类型 | 参数量 | 主要参数形状 |")
        report.append("|--------|------|--------|-------------|")
        
        for layer in sorted_layers[:15]:
            name = layer.get("name", "")[:40]  # 限制长度
            layer_type = layer.get("type", "")
            params = layer.get("parameters", 0)
            
            # 获取主要参数形状
            param_shapes = layer.get("parameter_shapes", [])
            main_shape = ""
            if param_shapes:
                main_shape = str(param_shapes[0].get("shape", []))[:30]
            
            report.append(f"| {name} | {layer_type} | {params:,} | {main_shape} |")
        
        if len(sorted_layers) > 15:
            report.append(f"| ... | (省略{len(sorted_layers)-15}个层) | ... | ... |")
        report.append("")
    
    # 计算时间线
    if computation_records:
        report.append("## 计算时间线（关键操作）")
        report.append("| 层名称 | 模块类型 | 输入形状 | 输出形状 |")
        report.append("|--------|----------|----------|----------|")
        
        # 只显示有形状信息的关键操作
        key_records = [r for r in computation_records[:20] if r.get("input_shapes") and r.get("output_shapes")]
        
        for record in key_records:
            layer_name = record.get("layer_name", "")[:30]
            module_type = record.get("module_type", "")
            input_shapes = str(record.get("input_shapes", []))[:40]
            output_shapes = str(record.get("output_shapes", []))[:40]
            
            report.append(f"| {layer_name} | {module_type} | {input_shapes} | {output_shapes} |")
        
        if len(computation_records) > len(key_records):
            report.append(f"| ... | (省略{len(computation_records)-len(key_records)}个操作) | ... | ... |")
        report.append("")
    
    # 性能摘要
    report.append("## 性能摘要")
    report.append(f"- 推理时间: {analysis_results.get('inference_time', 0):.3f}秒")
    report.append(f"- 计算操作数: {analysis_results.get('total_computations', 0)}")
    
    # 保存报告
    with open("comprehensive_sglang_analysis_report.md", 'w', encoding='utf-8') as f:
        f.write('\n'.join(report))
    
    print("[Report] 综合分析报告已生成:")
    print("  - 详细数据: comprehensive_sglang_analysis.json")
    print("  - 分析报告: comprehensive_sglang_analysis_report.md")

if __name__ == "__main__":
    success = run_comprehensive_analysis()
    
    print("\n" + "="*80)
    if success:
        print("SGLang综合算子分析完成！")
        print(f"成功追踪到 {len(operation_records)} 个计算操作")
    else:
        print("分析过程遇到问题，但已生成可用数据")
    print("="*80)
