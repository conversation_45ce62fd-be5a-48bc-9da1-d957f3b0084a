#!/usr/bin/env python3
"""
基于SGLang引擎结构的直接算子追踪
通过RPC和内部通信机制访问模型算子
"""
import os
import sys
import time
import json
import torch
import traceback
from typing import Dict, List, Any
from collections import defaultdict
import gc

# 单卡环境配置
os.environ.setdefault("CUDA_VISIBLE_DEVICES", "0")
os.environ.setdefault("SGL_DISABLE_TP_MEMORY_INBALANCE_CHECK", "1")
os.environ.setdefault("UCX_VFS_ENABLE", "n")
os.environ.setdefault("NCCL_P2P_DISABLE", "1") 
os.environ.setdefault("NCCL_IB_DISABLE", "1")
os.environ.setdefault("SGLANG_IS_FLASHINFER_AVAILABLE", "false")

# 全局追踪变量
tensor_operations = []
computation_traces = []
memory_allocations = []

class TensorOperationTracker:
    """张量操作追踪器 - 通过PyTorch的Hook机制"""
    
    def __init__(self):
        self.start_time = None
        self.enabled = False
        self.tensor_count = 0
        self.operation_count = 0
        
    def enable(self):
        """启用张量操作追踪"""
        self.enabled = True
        self.start_time = time.time()
        print("[TensorTracker] 启用张量操作追踪")
        
        # Hook张量创建
        self._hook_tensor_creation()
        
    def disable(self):
        """禁用追踪"""
        self.enabled = False
        print("[TensorTracker] 禁用张量操作追踪")
    
    def _hook_tensor_creation(self):
        """Hook张量创建和操作"""
        # 保存原始函数
        original_new = torch.Tensor.__new__
        original_mm = torch.mm
        original_matmul = torch.matmul
        original_linear = torch.nn.functional.linear
        
        # 包装mm操作
        def tracked_mm(input, mat2, out=None):
            if self.enabled:
                self._log_operation("torch.mm", [input, mat2], None)
            return original_mm(input, mat2, out)
        
        # 包装matmul操作
        def tracked_matmul(input, other, out=None):
            if self.enabled:
                self._log_operation("torch.matmul", [input, other], None)
            return original_matmul(input, other, out)
        
        # 包装linear操作
        def tracked_linear(input, weight, bias=None):
            if self.enabled:
                self._log_operation("F.linear", [input, weight, bias], None)
            return original_linear(input, weight, bias)
        
        # 应用包装
        torch.mm = tracked_mm
        torch.matmul = tracked_matmul
        torch.nn.functional.linear = tracked_linear
    
    def _log_operation(self, op_name, inputs, output):
        """记录操作"""
        if not self.enabled:
            return
            
        try:
            input_shapes = []
            for inp in inputs:
                if isinstance(inp, torch.Tensor):
                    input_shapes.append(list(inp.shape))
            
            operation_record = {
                "operation": op_name,
                "timestamp": time.time(),
                "relative_time": time.time() - self.start_time,
                "input_shapes": input_shapes,
                "operation_id": self.operation_count
            }
            
            tensor_operations.append(operation_record)
            self.operation_count += 1
            
            # 打印重要操作
            if len(input_shapes) >= 2:  # 矩阵运算
                print(f"[TensorOp] {op_name}: {input_shapes}")
                
        except Exception as e:
            print(f"[TensorOpError] {op_name}: {e}")

def memory_profiling():
    """内存性能分析"""
    if torch.cuda.is_available():
        allocated = torch.cuda.memory_allocated(0) / (1024**3)
        reserved = torch.cuda.memory_reserved(0) / (1024**3)
        max_allocated = torch.cuda.max_memory_allocated(0) / (1024**3)
        
        memory_record = {
            "timestamp": time.time(),
            "allocated_gb": allocated,
            "reserved_gb": reserved, 
            "max_allocated_gb": max_allocated
        }
        
        memory_allocations.append(memory_record)
        return memory_record
    return {}

def analyze_sglang_with_inference_tracking():
    """通过推理过程追踪分析SGLang"""
    MODEL_PATH = os.environ.get("MODEL_PATH", "/home/<USER>/deepseek-int8")
    
    print("="*80)
    print("SGLang推理过程算子追踪分析")
    print("="*80)
    
    # 初始化追踪器
    tracker = TensorOperationTracker()
    
    llm = None
    try:
        print("\n[Setup] 初始化SGLang引擎...")
        
        # 初始内存状态
        initial_memory = memory_profiling()
        print(f"[Memory] 初始状态: {initial_memory.get('allocated_gb', 0):.2f}GB")
        
        import sglang as sgl
        
        # 启用追踪
        tracker.enable()
        
        # 创建引擎
        print("[Setup] 创建引擎...")
        engine_start = time.time()
        
        llm = sgl.Engine(
            model_path=MODEL_PATH,
            tp_size=1,
            quantization="w8a8_int8",
            trust_remote_code=True,
            disable_cuda_graph=True,
            log_level="error",  # 减少日志干扰
        )
        
        engine_time = time.time() - engine_start
        engine_memory = memory_profiling()
        print(f"[Setup] 引擎创建完成 ({engine_time:.2f}s)")
        print(f"[Memory] 引擎加载后: {engine_memory.get('allocated_gb', 0):.2f}GB")
        
        # 执行多次推理以获得更多算子信息
        test_prompts = [
            "什么是深度学习？",
            "Transformer架构的核心是什么？", 
            "注意力机制如何工作？"
        ]
        
        for i, prompt in enumerate(test_prompts):
            print(f"\n[Inference {i+1}] 测试: {prompt}")
            
            # 清理之前的操作记录，专注当前推理
            if i > 0:
                tensor_operations.clear()
            
            inference_start = time.time()
            
            sampling_params = {
                "max_new_tokens": 3,  # 极少的tokens获得清晰追踪
                "temperature": 0.0,
                "do_sample": False
            }
            
            result = llm.generate(prompt=prompt, sampling_params=sampling_params)
            
            inference_time = time.time() - inference_start
            inference_memory = memory_profiling()
            
            print(f"[Result {i+1}] ({inference_time:.3f}s): {result.get('text', result)}")
            print(f"[Memory] 推理后: {inference_memory.get('allocated_gb', 0):.2f}GB")
            print(f"[TensorOps] 本次推理捕获 {len(tensor_operations)} 个张量操作")
            
            # 记录本次推理的计算轨迹
            inference_trace = {
                "inference_id": i + 1,
                "prompt": prompt,
                "inference_time": inference_time,
                "tensor_operations": list(tensor_operations),  # 复制当前操作
                "memory_peak": inference_memory.get('max_allocated_gb', 0)
            }
            
            computation_traces.append(inference_trace)
            
            # 分析这次推理的算子模式
            analyze_single_inference(inference_trace)
        
        return True
        
    except Exception as e:
        print(f"[Error] 分析失败: {e}")
        traceback.print_exc()
        return False
        
    finally:
        tracker.disable()
        
        if llm is not None:
            try:
                llm.shutdown()
                final_memory = memory_profiling()
                print(f"[Cleanup] 最终内存: {final_memory.get('allocated_gb', 0):.2f}GB")
            except Exception:
                pass

def analyze_single_inference(inference_trace):
    """分析单次推理的算子模式"""
    operations = inference_trace.get("tensor_operations", [])
    
    if not operations:
        print(f"[Analysis] 推理 {inference_trace['inference_id']} 没有捕获到张量操作")
        return
    
    # 统计操作类型
    op_types = defaultdict(int)
    shape_patterns = defaultdict(int)
    
    for op in operations:
        op_name = op.get("operation", "unknown")
        op_types[op_name] += 1
        
        input_shapes = op.get("input_shapes", [])
        for shape in input_shapes:
            if len(shape) >= 2:  # 至少是2D张量
                shape_key = tuple(shape)
                shape_patterns[shape_key] += 1
    
    print(f"[Analysis] 推理 {inference_trace['inference_id']} 算子分析:")
    for op_type, count in op_types.items():
        print(f"  {op_type}: {count}次")
    
    # 显示最常见的形状
    if shape_patterns:
        top_shapes = sorted(shape_patterns.items(), key=lambda x: x[1], reverse=True)[:5]
        print(f"  主要张量形状:")
        for shape, count in top_shapes:
            print(f"    {list(shape)}: {count}次")

def generate_inference_tracking_report():
    """生成推理追踪报告"""
    print("\n[Report] 生成推理追踪分析报告...")
    
    # 汇总所有数据
    all_operations = []
    for trace in computation_traces:
        all_operations.extend(trace.get("tensor_operations", []))
    
    # 综合分析
    total_ops = len(all_operations)
    op_type_summary = defaultdict(int)
    all_shapes = defaultdict(int)
    
    for op in all_operations:
        op_type_summary[op.get("operation", "unknown")] += 1
        for shape in op.get("input_shapes", []):
            if len(shape) >= 2:
                all_shapes[tuple(shape)] += 1
    
    # 创建报告数据
    report_data = {
        "summary": {
            "total_inferences": len(computation_traces),
            "total_tensor_operations": total_ops,
            "operation_type_summary": dict(op_type_summary),
            "memory_timeline": memory_allocations
        },
        "inference_traces": computation_traces,
        "shape_analysis": dict(sorted(all_shapes.items(), key=lambda x: x[1], reverse=True)[:20]),
        "generation_time": time.strftime("%Y-%m-%d %H:%M:%S")
    }
    
    # 保存详细数据
    with open("sglang_inference_tracking.json", 'w', encoding='utf-8') as f:
        json.dump(report_data, f, indent=2, ensure_ascii=False, default=str)
    
    # 生成Markdown报告
    report = []
    report.append("# SGLang推理过程算子追踪报告\n")
    report.append(f"## 生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
    
    # 总体摘要
    summary = report_data["summary"]
    report.append("## 总体摘要")
    report.append(f"- 推理次数: {summary['total_inferences']}")
    report.append(f"- 总张量操作: {summary['total_tensor_operations']}")
    
    # 操作类型分布
    op_summary = summary.get("operation_type_summary", {})
    if op_summary:
        report.append("- 操作类型分布:")
        for op_type, count in sorted(op_summary.items(), key=lambda x: x[1], reverse=True):
            report.append(f"  - {op_type}: {count}次")
    report.append("")
    
    # 形状分析
    shapes = report_data.get("shape_analysis", {})
    if shapes:
        report.append("## 张量形状分析")
        report.append("### 最常见的张量形状:")
        for shape, count in list(shapes.items())[:15]:
            report.append(f"- {list(shape)}: {count}次")
        report.append("")
    
    # 每次推理的详情
    report.append("## 推理详情")
    for trace in computation_traces:
        inference_id = trace.get("inference_id", 0)
        prompt = trace.get("prompt", "")[:50]
        inference_time = trace.get("inference_time", 0)
        ops_count = len(trace.get("tensor_operations", []))
        
        report.append(f"### 推理 {inference_id}")
        report.append(f"- 提示: {prompt}...")
        report.append(f"- 推理时间: {inference_time:.3f}秒")
        report.append(f"- 张量操作数: {ops_count}")
        report.append("")
    
    # 内存使用分析
    if memory_allocations:
        report.append("## 内存使用分析")
        peak_memory = max(mem.get("max_allocated_gb", 0) for mem in memory_allocations)
        report.append(f"- 峰值内存使用: {peak_memory:.2f}GB")
        
        report.append("### 内存使用时间线:")
        for i, mem in enumerate(memory_allocations[-10:]):  # 显示最后10个记录
            allocated = mem.get("allocated_gb", 0)
            report.append(f"- 记录{i+1}: {allocated:.2f}GB")
        report.append("")
    
    # 算子与参考文档对比
    report.append("## 与参考算子对比")
    reference_shapes = {
        (1, 128, 1536, 7168): "attn_wqa",
        (1, 128, 24576, 1536): "attn_wqb", 
        (1, 128, 576, 7168): "attn_wkv_a",
        (1, 128, 32768, 512): "attn_wkv_b",
        (1, 128, 18432, 7168): "dense_up/gate",
        (1, 128, 7168, 18432): "dense_down"
    }
    
    matched_shapes = []
    for shape_tuple, count in shapes.items():
        # 检查是否匹配参考形状（允许部分匹配）
        for ref_shape, ref_name in reference_shapes.items():
            if len(shape_tuple) >= 2 and len(ref_shape) >= 2:
                # 检查最后两个维度是否匹配（这是最重要的）
                if shape_tuple[-2:] == ref_shape[-2:]:
                    matched_shapes.append((list(shape_tuple), ref_name, count))
                    break
    
    if matched_shapes:
        report.append("### 匹配的参考算子形状:")
        for shape, ref_name, count in matched_shapes:
            report.append(f"- {shape} → {ref_name} (出现{count}次)")
    else:
        report.append("### 未找到匹配的参考算子形状")
    report.append("")
    
    # 保存报告
    with open("sglang_inference_tracking_report.md", 'w', encoding='utf-8') as f:
        f.write('\n'.join(report))
    
    print("[Report] 推理追踪报告已生成:")
    print("  - 详细数据: sglang_inference_tracking.json")
    print("  - 分析报告: sglang_inference_tracking_report.md")

def main():
    """主函数"""
    print("启动SGLang推理过程算子追踪分析...")
    
    # 执行分析
    success = analyze_sglang_with_inference_tracking()
    
    # 生成报告
    generate_inference_tracking_report()
    
    print("\n" + "="*80)
    if success:
        print("SGLang推理过程算子追踪分析完成！")
        total_ops = sum(len(trace.get("tensor_operations", [])) for trace in computation_traces)
        print(f"总共追踪到 {total_ops} 个张量操作")
        print(f"分析了 {len(computation_traces)} 次推理过程")
    else:
        print("分析过程遇到错误，但已生成可用数据")
    print("="*80)

if __name__ == "__main__":
    main()
