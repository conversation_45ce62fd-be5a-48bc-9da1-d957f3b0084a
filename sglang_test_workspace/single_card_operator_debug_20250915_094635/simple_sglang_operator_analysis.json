{"summary": {"total_test_cases": 3, "total_operations": 0, "total_inference_time": 3.557380437850952, "operation_type_distribution": {}, "shape_patterns": {}}, "inference_details": [{"case_name": "简单测试", "prompt": "AI是什么？", "output": "XXXXXXXXXXXXXXXXabits[size_EQ", "inference_time": 3.508594512939453, "operations": [], "operation_count": 0}, {"case_name": "技术测试", "prompt": "深度学习的原理？", "output": " события shear相关政策头皮", "inference_time": 0.025477170944213867, "operations": [], "operation_count": 0}, {"case_name": "架构测试", "prompt": "Transformer如何工作？", "output": " tweBecause colonization那一刻", "inference_time": 0.023308753967285156, "operations": [], "operation_count": 0}], "memory_timeline": [{"stage": "initial", "timestamp": 1757930389.1363966, "allocated_gb": 0.0, "reserved_gb": 0.0}, {"stage": "after_engine_creation", "timestamp": 1757930410.7781248, "allocated_gb": 0.0, "reserved_gb": 0.0}, {"stage": "after_简单测试", "timestamp": 1757930414.2869682, "allocated_gb": 0.0, "reserved_gb": 0.0}, {"stage": "after_技术测试", "timestamp": 1757930414.3129902, "allocated_gb": 0.0, "reserved_gb": 0.0}, {"stage": "after_架构测试", "timestamp": 1757930414.3368547, "allocated_gb": 0.0, "reserved_gb": 0.0}], "generation_time": "2025-09-15 10:00:14"}