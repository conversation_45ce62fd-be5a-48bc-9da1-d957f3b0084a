#!/usr/bin/env python3
"""
PyTorch Profiler集成的算子追踪
使用PyTorch自带的profiler捕获算子运算
"""
import os
import sys
import time
import json
import torch
import traceback
from typing import Dict, List, Any
from collections import defaultdict

# 单卡环境配置
os.environ.setdefault("CUDA_VISIBLE_DEVICES", "0")
os.environ.setdefault("SGL_DISABLE_TP_MEMORY_INBALANCE_CHECK", "1")
os.environ.setdefault("UCX_VFS_ENABLE", "n")
os.environ.setdefault("NCCL_P2P_DISABLE", "1")
os.environ.setdefault("NCCL_IB_DISABLE", "1")
os.environ.setdefault("SGLANG_IS_FLASHINFER_AVAILABLE", "false")

# 确保正确的路径
sys.path.insert(0, "/workspace/sglang_test_workspace/sglang_debug_task_20250915_071412")

def run_profiler_based_tracking():
    """使用PyTorch Profiler进行算子追踪"""
    
    print("="*80)
    print("PyTorch Profiler算子追踪")
    print("="*80)
    
    # 导入SGLang
    from sglang import RuntimeEndpoint
    
    MODEL_PATH = os.environ.get("MODEL_PATH", "/home/<USER>/deepseek-int8")
    
    try:
        print("\n[Profiler] 初始化SGLang运行时...")
        
        # 创建运行时
        runtime = RuntimeEndpoint(
            model_path=MODEL_PATH,
            tp_size=1,
            quantization="w8a8_int8",
            trust_remote_code=True,
            log_level="error",
        )
        
        print("[Profiler] SGLang运行时创建完成")
        
        # 准备profiler
        profiler_activities = [
            torch.profiler.ProfilerActivity.CPU,
        ]
        
        if torch.cuda.is_available():
            profiler_activities.append(torch.profiler.ProfilerActivity.CUDA)
        
        print(f"[Profiler] 启动profiler，活动类型: {profiler_activities}")
        
        # 执行带profiler的推理
        with torch.profiler.profile(
            activities=profiler_activities,
            record_shapes=True,
            profile_memory=True,
            with_stack=True,
        ) as prof:
            
            print("[Profiler] 开始推理...")
            
            # 执行推理
            response = runtime.generate(
                "什么是深度学习？请简单解释。",
                max_new_tokens=10,
                temperature=0.8,
            )
            
            print(f"[Profiler] 推理结果: {response}")
        
        print("[Profiler] 推理完成，分析profiler数据...")
        
        # 分析profiler结果
        analyze_profiler_results(prof)
        
        return True
        
    except Exception as e:
        print(f"[Error] Profiler追踪失败: {e}")
        traceback.print_exc()
        return False
    
    finally:
        try:
            runtime.shutdown()
        except Exception:
            pass

def analyze_profiler_results(prof):
    """分析profiler结果"""
    print("\n[Analysis] 分析PyTorch Profiler结果...")
    
    # 获取关键事件
    events = prof.key_averages().table(sort_by="cuda_time_total", row_limit=20)
    print("\n[Analysis] CUDA时间排序的前20个操作:")
    print(events)
    
    # 获取所有事件
    all_events = prof.key_averages()
    
    # 分析算子操作
    operator_analysis = {}
    shape_analysis = defaultdict(int)
    memory_analysis = {}
    
    for event in all_events:
        key = event.key
        
        # 过滤出重要的算子操作
        if any(op in key.lower() for op in ['mm', 'linear', 'conv', 'add', 'mul', 'attention']):
            operator_analysis[key] = {
                "count": event.count,
                "cpu_time": event.cpu_time_total,
                "cuda_time": event.cuda_time_total,
                "cpu_time_avg": event.cpu_time,
                "cuda_time_avg": event.cuda_time,
                "input_shapes": event.input_shapes if hasattr(event, 'input_shapes') else []
            }
            
            # 统计形状
            if hasattr(event, 'input_shapes') and event.input_shapes:
                for shape in event.input_shapes:
                    if shape and len(shape) >= 2:
                        shape_tuple = tuple(shape)
                        shape_analysis[shape_tuple] += event.count
    
    # 生成详细分析报告
    generate_profiler_report(operator_analysis, shape_analysis, all_events)

def generate_profiler_report(operator_analysis, shape_analysis, all_events):
    """生成profiler分析报告"""
    print("\n[Report] 生成PyTorch Profiler分析报告...")
    
    # 创建报告数据
    report_data = {
        "summary": {
            "total_events": len(all_events),
            "operator_events": len(operator_analysis),
            "unique_shapes": len(shape_analysis),
            "generation_time": time.strftime("%Y-%m-%d %H:%M:%S")
        },
        "operator_analysis": operator_analysis,
        "shape_analysis": dict(sorted(shape_analysis.items(), key=lambda x: x[1], reverse=True)),
        "top_events": []
    }
    
    # 获取最耗时的事件
    sorted_events = sorted(all_events, key=lambda x: x.cuda_time_total, reverse=True)[:20]
    for event in sorted_events:
        report_data["top_events"].append({
            "name": event.key,
            "count": event.count,
            "cpu_time_total": event.cpu_time_total,
            "cuda_time_total": event.cuda_time_total,
            "cpu_time_avg": event.cpu_time,
            "cuda_time_avg": event.cuda_time
        })
    
    # 保存详细数据
    with open("profiler_operator_analysis.json", 'w', encoding='utf-8') as f:
        json.dump(report_data, f, indent=2, ensure_ascii=False, default=str)
    
    # 生成Markdown报告
    generate_profiler_markdown_report(report_data)
    
    print("[Report] PyTorch Profiler分析报告已生成:")
    print("  - 详细数据: profiler_operator_analysis.json")
    print("  - 分析报告: profiler_operator_analysis_report.md")

def generate_profiler_markdown_report(report_data):
    """生成Profiler Markdown报告"""
    report = []
    report.append("# SGLang PyTorch Profiler算子追踪分析报告\n")
    report.append(f"## 生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
    
    summary = report_data.get("summary", {})
    
    # 总体摘要
    report.append("## 总体摘要")
    report.append(f"- 总事件数: {summary.get('total_events', 0)}")
    report.append(f"- 算子事件数: {summary.get('operator_events', 0)}")
    report.append(f"- 唯一形状数: {summary.get('unique_shapes', 0)}")
    
    # 最耗时事件
    top_events = report_data.get("top_events", [])
    if top_events:
        report.append("\n## 最耗时的操作 (前10个)")
        report.append("| 操作名称 | 调用次数 | CUDA总时间(μs) | CUDA平均时间(μs) |")
        report.append("|----------|----------|----------------|------------------|")
        
        for event in top_events[:10]:
            name = event.get("name", "").replace("|", "\\|")[:50]
            count = event.get("count", 0)
            cuda_total = event.get("cuda_time_total", 0)
            cuda_avg = event.get("cuda_time_avg", 0)
            report.append(f"| {name} | {count} | {cuda_total:.1f} | {cuda_avg:.1f} |")
    
    # 算子分析
    operator_analysis = report_data.get("operator_analysis", {})
    if operator_analysis:
        report.append("\n## 关键算子操作分析")
        
        # 按CUDA时间排序
        sorted_ops = sorted(operator_analysis.items(), 
                          key=lambda x: x[1].get("cuda_time", 0), reverse=True)
        
        for op_name, op_data in sorted_ops[:15]:
            report.append(f"\n### {op_name}")
            report.append(f"- **调用次数**: {op_data.get('count', 0)}")
            report.append(f"- **CUDA总时间**: {op_data.get('cuda_time', 0):.1f}μs")
            report.append(f"- **CUDA平均时间**: {op_data.get('cuda_time_avg', 0):.1f}μs")
            
            input_shapes = op_data.get('input_shapes', [])
            if input_shapes:
                report.append(f"- **输入形状**: {input_shapes}")
    
    # 形状分析
    shape_analysis = report_data.get("shape_analysis", {})
    if shape_analysis:
        report.append("\n## 张量形状分析")
        report.append("### 最常见的张量形状 (前20个):")
        
        for shape_tuple, count in list(shape_analysis.items())[:20]:
            shape = list(shape_tuple)
            report.append(f"- `{shape}`: {count}次")
        
        # 与参考文档对比
        report.append("\n### 与参考算子形状对比")
        reference_patterns = {
            (128, 7168): "hidden_size相关 (B=128, M=7168)",
            (128, 18432): "intermediate_size相关 (B=128, M=18432)", 
            (128, 1536): "attention相关 (B=128, M=1536)",
            (128, 576): "kv_attention相关 (B=128, M=576)",
            (7168, 18432): "dense层权重 (M=7168, N=18432)",
            (18432, 7168): "dense层权重 (M=18432, N=7168)",
            (1536, 7168): "attention权重 (M=1536, N=7168)",
            (7168, 1536): "attention权重 (M=7168, N=1536)"
        }
        
        matched_patterns = []
        for shape_tuple, count in shape_analysis.items():
            # 检查最后两个维度
            if len(shape_tuple) >= 2:
                last_two = shape_tuple[-2:]
                if last_two in reference_patterns:
                    matched_patterns.append((list(shape_tuple), reference_patterns[last_two], count))
        
        if matched_patterns:
            report.append("#### 匹配的参考模式:")
            for shape, pattern_type, count in matched_patterns[:10]:
                report.append(f"- `{shape}` → {pattern_type} ({count}次)")
        else:
            report.append("#### 未找到明确匹配的参考模式")
    
    # 技术结论
    report.append("\n## 技术结论")
    report.append("### 主要发现:")
    
    if summary.get('operator_events', 0) > 0:
        report.append("1. **PyTorch Profiler成功捕获算子**: 通过profiler成功追踪到推理过程中的算子操作")
        report.append("2. **性能热点识别**: 可以清楚看到最耗时的操作和调用次数")
        
        if shape_analysis:
            report.append("3. **张量形状分析**: 成功捕获到实际的张量形状，可与参考文档对比")
        
        report.append("4. **SGLang内部结构**: PyTorch Profiler可以穿透SGLang抽象层，看到底层计算")
    else:
        report.append("1. **Profiler数据有限**: 虽然profiler运行成功，但捕获的算子相关事件较少")
        report.append("2. **可能的原因**: SGLang使用了高度优化的实现，或推理时间太短导致采样不足")
    
    report.append("\n### 优化建议:")
    if top_events:
        top_op = top_events[0].get("name", "").split()[0]
        report.append(f"1. **重点优化**: 关注最耗时的操作 `{top_op}`")
    
    report.append("2. **内存优化**: 根据常见张量形状优化内存分配策略")
    report.append("3. **算子融合**: 考虑将频繁调用的小算子进行融合")
    report.append("4. **量化效果**: 分析w8a8量化对不同算子的性能影响")
    
    # 保存报告
    with open("profiler_operator_analysis_report.md", 'w', encoding='utf-8') as f:
        f.write('\n'.join(report))

def main():
    """主函数"""
    print("启动PyTorch Profiler算子追踪分析...")
    
    success = run_profiler_based_tracking()
    
    print("\n" + "="*80)
    if success:
        print("PyTorch Profiler算子追踪分析完成！")
        print("使用PyTorch内置profiler成功捕获了算子性能数据")
    else:
        print("Profiler分析过程遇到错误")
    print("="*80)

if __name__ == "__main__":
    main()
