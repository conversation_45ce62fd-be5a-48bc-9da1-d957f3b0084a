#!/usr/bin/env python3
"""
SGLang特定算子追踪脚本
专门针对SGLang内部算子进行深度追踪
"""
import os
import sys
import time
import json
import torch
import traceback
from typing import Dict, List, Any
from collections import defaultdict
import threading

# 单卡环境配置
os.environ.setdefault("CUDA_VISIBLE_DEVICES", "0")
os.environ.setdefault("SGL_DISABLE_TP_MEMORY_INBALANCE_CHECK", "1") 
os.environ.setdefault("UCX_VFS_ENABLE", "n")
os.environ.setdefault("NCCL_P2P_DISABLE", "1")
os.environ.setdefault("NCCL_IB_DISABLE", "1")
os.environ.setdefault("SGLANG_IS_FLASHINFER_AVAILABLE", "false")

# 全局追踪数据
operation_logs = []
tensor_operations = []
computation_timeline = []
model_structure_info = {}

class SGLangOperatorInspector:
    """SGLang算子检查器"""
    
    def __init__(self):
        self.start_time = None
        self.hooks = []
        self.registered_modules = {}
        
    def start_inspection(self):
        """开始检查"""
        self.start_time = time.time()
        print("[Inspector] 开始SGLang算子检查")
        
    def inspect_model_structure(self, model):
        """检查模型结构"""
        print("\n[Inspector] 检查模型结构...")
        
        structure_info = {
            "model_type": type(model).__name__,
            "modules": {},
            "parameters": {},
            "total_params": 0
        }
        
        # 遍历模型模块
        for name, module in model.named_modules():
            module_info = {
                "type": type(module).__name__,
                "parameters": []
            }
            
            # 检查模块参数
            for param_name, param in module.named_parameters(recurse=False):
                if param is not None:
                    param_info = {
                        "name": param_name,
                        "shape": list(param.shape),
                        "dtype": str(param.dtype),
                        "requires_grad": param.requires_grad,
                        "numel": param.numel()
                    }
                    module_info["parameters"].append(param_info)
                    structure_info["total_params"] += param.numel()
            
            if module_info["parameters"] or any(keyword in name.lower() for keyword in [
                'attention', 'attn', 'mlp', 'moe', 'linear', 'embed', 'norm'
            ]):
                structure_info["modules"][name] = module_info
                
                # 打印重要模块
                if len(structure_info["modules"]) <= 15:  # 限制输出数量
                    param_count = sum(p["numel"] for p in module_info["parameters"])
                    print(f"[Module] {name}: {type(module).__name__} ({param_count:,} params)")
        
        model_structure_info.update(structure_info)
        print(f"[Inspector] 总参数量: {structure_info['total_params']:,}")
        
        return structure_info
    
    def register_forward_hooks(self, model):
        """注册前向传播hooks"""
        print("\n[Inspector] 注册前向传播hooks...")
        
        def create_hook(module_name, module_type):
            def forward_hook(module, input, output):
                try:
                    # 记录操作
                    timestamp = time.time()
                    relative_time = timestamp - self.start_time
                    
                    # 分析输入
                    input_analysis = self._analyze_tensor_data(input, "input")
                    
                    # 分析输出  
                    output_analysis = self._analyze_tensor_data(output, "output")
                    
                    # 创建操作记录
                    operation_record = {
                        "module_name": module_name,
                        "module_type": module_type,
                        "timestamp": timestamp,
                        "relative_time": relative_time,
                        "input_analysis": input_analysis,
                        "output_analysis": output_analysis
                    }
                    
                    operation_logs.append(operation_record)
                    
                    # 打印重要操作
                    print(f"[Forward] {module_name} ({module_type})")
                    if input_analysis.get("shapes"):
                        print(f"  Input: {input_analysis['shapes']}")
                    if output_analysis.get("shapes"):
                        print(f"  Output: {output_analysis['shapes']}")
                    
                except Exception as e:
                    print(f"[HookError] {module_name}: {e}")
            
            return forward_hook
        
        # 为关键模块注册hooks
        hook_count = 0
        for name, module in model.named_modules():
            # 专注于关键计算模块
            if any(keyword in name.lower() for keyword in [
                'attention', 'attn', 'self_attn', 'cross_attn',
                'mlp', 'feed_forward', 'ffn', 
                'moe', 'expert', 'gate',
                'linear', 'conv', 'embedding', 'embed',
                'norm', 'layer_norm', 'group_norm'
            ]) and not any(skip in name.lower() for skip in ['dropout', 'activation']):
                
                hook = module.register_forward_hook(
                    create_hook(name, type(module).__name__)
                )
                self.hooks.append(hook)
                self.registered_modules[name] = type(module).__name__
                hook_count += 1
        
        print(f"[Inspector] 注册了 {hook_count} 个前向hooks")
        return hook_count
    
    def _analyze_tensor_data(self, data, data_type):
        """分析张量数据"""
        analysis = {"type": data_type, "shapes": [], "dtypes": [], "devices": []}
        
        if isinstance(data, torch.Tensor):
            analysis["shapes"] = [list(data.shape)]
            analysis["dtypes"] = [str(data.dtype)]
            analysis["devices"] = [str(data.device)]
        elif isinstance(data, (tuple, list)):
            for item in data:
                if isinstance(item, torch.Tensor):
                    analysis["shapes"].append(list(item.shape))
                    analysis["dtypes"].append(str(item.dtype))
                    analysis["devices"].append(str(item.device))
        
        return analysis
    
    def monitor_tensor_operations(self):
        """监控张量操作（通过torch的dispatcher）"""
        print("[Inspector] 开始监控张量操作...")
        
        # 这里可以添加更深层的tensor操作监控
        # 由于SGLang的复杂性，我们主要依赖forward hooks
        pass
    
    def cleanup(self):
        """清理hooks"""
        for hook in self.hooks:
            hook.remove()
        self.hooks.clear()
        print("[Inspector] 清理完成")

def run_sglang_inspection():
    """运行SGLang检查"""
    MODEL_PATH = os.environ.get("MODEL_PATH", "/home/<USER>/deepseek-int8")
    
    inspector = SGLangOperatorInspector()
    inspector.start_inspection()
    
    llm = None
    try:
        print("\n[SGLang] 初始化单卡引擎...")
        
        import sglang as sgl
        
        # 创建引擎
        llm = sgl.Engine(
            model_path=MODEL_PATH,
            tp_size=1,  # 单卡
            quantization="w8a8_int8",
            trust_remote_code=True,
            disable_cuda_graph=True,
            log_level="info",
        )
        
        # 获取模型对象
        model = None
        if hasattr(llm, 'model'):
            model = llm.model
        elif hasattr(llm, 'runner') and hasattr(llm.runner, 'model'):
            model = llm.runner.model
        elif hasattr(llm, 'engine') and hasattr(llm.engine, 'model_executor'):
            model = llm.engine.model_executor.driver_worker.model_runner.model
        
        if model is None:
            print("[Error] 无法获取模型对象")
            return False
        
        print(f"[SGLang] 找到模型对象: {type(model).__name__}")
        
        # 检查模型结构
        structure_info = inspector.inspect_model_structure(model)
        
        # 注册hooks
        hook_count = inspector.register_forward_hooks(model)
        
        if hook_count == 0:
            print("[Warning] 没有注册任何hooks，尝试备用方案...")
            # 尝试更简单的hook策略
            for name, module in model.named_modules():
                if 'linear' in name.lower() or 'attention' in name.lower():
                    print(f"[Available] {name}: {type(module).__name__}")
        
        # 执行推理以触发hooks
        print("\n[SGLang] 执行推理测试...")
        
        prompt = "什么是Transformer？"
        sampling_params = {
            "max_new_tokens": 8,  # 少量tokens以获得清晰的追踪
            "temperature": 0.0,   # 使用确定性采样
        }
        
        start_inference = time.time()
        result = llm.generate(prompt=prompt, sampling_params=sampling_params)
        inference_time = time.time() - start_inference
        
        print(f"\n[Result] 推理完成 ({inference_time:.3f}s)")
        print(f"[Result] 输出: {result.get('text', result)}")
        
        return True
        
    except Exception as e:
        print(f"[Error] SGLang检查失败: {e}")
        traceback.print_exc()
        return False
        
    finally:
        inspector.cleanup()
        
        if llm is not None:
            try:
                llm.shutdown()
            except Exception:
                pass

def analyze_operation_logs():
    """分析操作日志"""
    print("\n[Analysis] 分析操作日志...")
    
    if not operation_logs:
        print("[Analysis] 没有操作记录")
        return {}
    
    # 按模块类型分类
    module_type_stats = defaultdict(int)
    shape_patterns = defaultdict(int) 
    timing_stats = []
    
    for op in operation_logs:
        module_type_stats[op["module_type"]] += 1
        timing_stats.append(op["relative_time"])
        
        # 分析形状模式
        input_shapes = op.get("input_analysis", {}).get("shapes", [])
        output_shapes = op.get("output_analysis", {}).get("shapes", [])
        
        for shape in input_shapes + output_shapes:
            shape_key = tuple(shape)
            shape_patterns[shape_key] += 1
    
    analysis = {
        "total_operations": len(operation_logs),
        "module_type_distribution": dict(module_type_stats),
        "shape_patterns": dict(sorted(shape_patterns.items(), key=lambda x: x[1], reverse=True)[:20]),
        "timing_range": [min(timing_stats), max(timing_stats)] if timing_stats else [0, 0],
        "model_structure": model_structure_info
    }
    
    return analysis

def generate_inspection_report(analysis):
    """生成检查报告"""
    print("\n[Report] 生成SGLang算子检查报告...")
    
    # 保存详细数据
    detailed_data = {
        "operation_logs": operation_logs,
        "analysis": analysis,
        "generation_time": time.strftime("%Y-%m-%d %H:%M:%S")
    }
    
    with open("sglang_operator_inspection.json", 'w', encoding='utf-8') as f:
        json.dump(detailed_data, f, indent=2, ensure_ascii=False, default=str)
    
    # 生成Markdown报告
    report = []
    report.append("# SGLang算子深度检查报告\n")
    report.append(f"## 生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
    
    # 基本统计
    report.append("## 基本统计")
    report.append(f"- 总操作数: {analysis.get('total_operations', 0)}")
    
    module_dist = analysis.get("module_type_distribution", {})
    if module_dist:
        report.append("- 模块类型分布:")
        for module_type, count in sorted(module_dist.items(), key=lambda x: x[1], reverse=True):
            report.append(f"  - {module_type}: {count}次")
    report.append("")
    
    # 模型结构信息
    model_info = analysis.get("model_structure", {})
    if model_info:
        report.append("## 模型结构信息")
        report.append(f"- 模型类型: {model_info.get('model_type', 'Unknown')}")
        report.append(f"- 总参数量: {model_info.get('total_params', 0):,}")
        report.append(f"- 主要模块数: {len(model_info.get('modules', {}))}")
        report.append("")
    
    # 形状模式分析
    shapes = analysis.get("shape_patterns", {})
    if shapes:
        report.append("## 张量形状模式TOP15")
        for shape, count in list(shapes.items())[:15]:
            report.append(f"- {list(shape)}: {count}次")
        report.append("")
    
    # 操作时间线（显示前20个）
    if operation_logs:
        report.append("## 操作时间线（前20个）")
        report.append("| 时间(s) | 模块名称 | 模块类型 | 输入形状 | 输出形状 |")
        report.append("|---------|----------|----------|----------|----------|")
        
        for op in operation_logs[:20]:
            module_name = op.get("module_name", "")
            module_type = op.get("module_type", "")
            relative_time = op.get("relative_time", 0)
            
            input_shapes = op.get("input_analysis", {}).get("shapes", [])
            output_shapes = op.get("output_analysis", {}).get("shapes", [])
            
            input_str = str(input_shapes[:2]) if input_shapes else "[]"  # 只显示前2个
            output_str = str(output_shapes[:2]) if output_shapes else "[]"
            
            report.append(f"| {relative_time:.3f} | {module_name[:30]} | {module_type} | {input_str[:30]} | {output_str[:30]} |")
        
        if len(operation_logs) > 20:
            report.append(f"| ... | (省略{len(operation_logs)-20}个操作) | ... | ... | ... |")
        report.append("")
    
    # 保存报告
    with open("sglang_operator_inspection_report.md", 'w', encoding='utf-8') as f:
        f.write('\n'.join(report))
    
    print("[Report] 报告已生成:")
    print("  - 详细数据: sglang_operator_inspection.json")
    print("  - 分析报告: sglang_operator_inspection_report.md")

def main():
    """主函数"""
    print("="*80)
    print("SGLang深度算子检查")
    print("="*80)
    
    # 运行检查
    success = run_sglang_inspection()
    
    # 分析结果
    analysis = analyze_operation_logs()
    
    # 生成报告
    generate_inspection_report(analysis)
    
    print("\n" + "="*80)
    if success:
        print("SGLang算子检查完成！")
        if operation_logs:
            print(f"成功追踪到 {len(operation_logs)} 个模块操作")
        else:
            print("未能追踪到模块操作，可能需要调整Hook策略")
    else:
        print("检查过程遇到错误")
    print("="*80)

if __name__ == "__main__":
    main()
