#!/usr/bin/env python3
"""
简化版SGLang算子追踪
直接追踪推理过程中的张量操作
"""
import os
import sys
import time
import json
import torch
import traceback
from typing import Dict, List, Any
from collections import defaultdict

# 单卡环境配置
os.environ.setdefault("CUDA_VISIBLE_DEVICES", "0")
os.environ.setdefault("SGL_DISABLE_TP_MEMORY_INBALANCE_CHECK", "1")
os.environ.setdefault("UCX_VFS_ENABLE", "n")
os.environ.setdefault("NCCL_P2P_DISABLE", "1")
os.environ.setdefault("NCCL_IB_DISABLE", "1")
os.environ.setdefault("SGLANG_IS_FLASHINFER_AVAILABLE", "false")

# 全局操作记录
captured_operations = []
memory_records = []

class SimpleOperatorTracker:
    """简化算子追踪器"""
    
    def __init__(self):
        self.start_time = None
        self.enabled = False
        self.original_functions = {}
        
    def start_tracking(self):
        """开始追踪"""
        self.enabled = True
        self.start_time = time.time()
        print("[Tracker] 开始简化算子追踪")
        
        # Hook关键的torch操作
        self._hook_operations()
    
    def stop_tracking(self):
        """停止追踪"""
        self.enabled = False
        self._restore_operations()
        print("[Tracker] 停止算子追踪")
    
    def _hook_operations(self):
        """Hook关键操作"""
        # 保存原始函数
        self.original_functions = {
            'torch.mm': torch.mm,
            'torch.matmul': torch.matmul,
            'torch.addmm': torch.addmm,
            'F.linear': torch.nn.functional.linear,
        }
        
        # 创建包装函数
        def wrapped_mm(input, mat2, *, out=None):
            result = self.original_functions['torch.mm'](input, mat2, out=out)
            if self.enabled:
                self._log_operation("torch.mm", [input, mat2], result)
            return result
        
        def wrapped_matmul(input, other, *, out=None):
            result = self.original_functions['torch.matmul'](input, other, out=out)
            if self.enabled:
                self._log_operation("torch.matmul", [input, other], result)
            return result
        
        def wrapped_addmm(bias, input, mat2, *, beta=1, alpha=1, out=None):
            result = self.original_functions['torch.addmm'](bias, input, mat2, beta=beta, alpha=alpha, out=out)
            if self.enabled:
                self._log_operation("torch.addmm", [bias, input, mat2], result)
            return result
        
        def wrapped_linear(input, weight, bias=None):
            result = self.original_functions['F.linear'](input, weight, bias)
            if self.enabled:
                self._log_operation("F.linear", [input, weight, bias], result)
            return result
        
        # 应用包装
        torch.mm = wrapped_mm
        torch.matmul = wrapped_matmul
        torch.addmm = wrapped_addmm
        torch.nn.functional.linear = wrapped_linear
    
    def _restore_operations(self):
        """恢复原始操作"""
        if self.original_functions:
            torch.mm = self.original_functions['torch.mm']
            torch.matmul = self.original_functions['torch.matmul'] 
            torch.addmm = self.original_functions['torch.addmm']
            torch.nn.functional.linear = self.original_functions['F.linear']
    
    def _log_operation(self, op_name, inputs, output):
        """记录操作"""
        try:
            input_shapes = []
            for inp in inputs:
                if isinstance(inp, torch.Tensor):
                    input_shapes.append(list(inp.shape))
            
            output_shape = []
            if isinstance(output, torch.Tensor):
                output_shape = list(output.shape)
            
            if input_shapes:  # 只记录有张量输入的操作
                operation_record = {
                    "operation": op_name,
                    "timestamp": time.time(),
                    "relative_time": time.time() - self.start_time,
                    "input_shapes": input_shapes,
                    "output_shape": output_shape
                }
                
                captured_operations.append(operation_record)
                
                # 实时打印重要操作
                print(f"[Op] {op_name}: {input_shapes} -> {output_shape}")
                
        except Exception as e:
            print(f"[OpError] {op_name}: {e}")

def monitor_memory(stage):
    """监控内存使用"""
    if torch.cuda.is_available():
        allocated = torch.cuda.memory_allocated(0) / (1024**3)
        reserved = torch.cuda.memory_reserved(0) / (1024**3)
        
        memory_record = {
            "stage": stage,
            "timestamp": time.time(),
            "allocated_gb": allocated,
            "reserved_gb": reserved
        }
        
        memory_records.append(memory_record)
        print(f"[Memory] {stage}: {allocated:.2f}GB allocated, {reserved:.2f}GB reserved")
        
        return memory_record
    return {}

def run_simple_sglang_test():
    """运行简单的SGLang测试"""
    MODEL_PATH = os.environ.get("MODEL_PATH", "/home/<USER>/deepseek-int8")
    
    print("="*80)
    print("简化版SGLang算子追踪")
    print("="*80)
    
    tracker = SimpleOperatorTracker()
    llm = None
    
    try:
        # 初始状态
        monitor_memory("initial")
        
        print("\n[SGLang] 初始化引擎...")
        import sglang as sgl
        
        # 启动追踪
        tracker.start_tracking()
        
        # 创建引擎
        engine_start = time.time()
        llm = sgl.Engine(
            model_path=MODEL_PATH,
            tp_size=1,
            quantization="w8a8_int8",
            trust_remote_code=True,
            disable_cuda_graph=True,
            log_level="error",
        )
        
        engine_time = time.time() - engine_start
        print(f"[SGLang] 引擎创建完成 ({engine_time:.2f}s)")
        monitor_memory("after_engine_creation")
        
        # 执行推理测试
        test_cases = [
            ("简单测试", "AI是什么？"),
            ("技术测试", "深度学习的原理？"),
            ("架构测试", "Transformer如何工作？")
        ]
        
        inference_results = []
        
        for case_name, prompt in test_cases:
            print(f"\n[Test] {case_name}: {prompt}")
            
            # 清理之前的操作记录
            captured_operations.clear()
            
            inference_start = time.time()
            
            # 使用正确的采样参数
            sampling_params = {
                "max_new_tokens": 4,
                "temperature": 0.1
            }
            
            result = llm.generate(prompt=prompt, sampling_params=sampling_params)
            
            inference_time = time.time() - inference_start
            monitor_memory(f"after_{case_name}")
            
            output_text = result.get("text", str(result))
            print(f"[Result] ({inference_time:.3f}s): {output_text}")
            print(f"[Captured] 本次推理捕获 {len(captured_operations)} 个算子操作")
            
            # 保存这次推理的结果
            inference_result = {
                "case_name": case_name,
                "prompt": prompt,
                "output": output_text,
                "inference_time": inference_time,
                "operations": list(captured_operations),  # 复制操作记录
                "operation_count": len(captured_operations)
            }
            
            inference_results.append(inference_result)
            
            # 分析这次推理的操作模式
            analyze_inference_operations(inference_result)
        
        # 生成综合分析
        generate_comprehensive_analysis(inference_results)
        
        return True
        
    except Exception as e:
        print(f"[Error] 测试失败: {e}")
        traceback.print_exc()
        return False
        
    finally:
        tracker.stop_tracking()
        
        if llm is not None:
            try:
                llm.shutdown()
                monitor_memory("cleanup")
            except Exception:
                pass

def analyze_inference_operations(inference_result):
    """分析单次推理的操作"""
    operations = inference_result.get("operations", [])
    case_name = inference_result.get("case_name", "Unknown")
    
    if not operations:
        print(f"[Analysis] {case_name}: 没有捕获到算子操作")
        return
    
    # 统计操作类型
    op_counts = defaultdict(int)
    shape_patterns = defaultdict(int)
    
    for op in operations:
        op_name = op.get("operation", "unknown")
        op_counts[op_name] += 1
        
        # 分析输入形状模式
        input_shapes = op.get("input_shapes", [])
        for shape in input_shapes:
            if len(shape) >= 2:  # 至少2D张量
                shape_key = tuple(shape)
                shape_patterns[shape_key] += 1
    
    print(f"[Analysis] {case_name} 操作分析:")
    for op_type, count in op_counts.items():
        print(f"  {op_type}: {count}次")
    
    # 显示最重要的形状
    if shape_patterns:
        top_shapes = sorted(shape_patterns.items(), key=lambda x: x[1], reverse=True)[:3]
        print(f"  主要张量形状:")
        for shape, count in top_shapes:
            print(f"    {list(shape)}: {count}次")

def generate_comprehensive_analysis(inference_results):
    """生成综合分析报告"""
    print("\n[Report] 生成综合分析报告...")
    
    # 汇总所有操作
    all_operations = []
    total_inference_time = 0
    
    for result in inference_results:
        all_operations.extend(result.get("operations", []))
        total_inference_time += result.get("inference_time", 0)
    
    # 综合统计
    total_ops = len(all_operations)
    op_type_summary = defaultdict(int)
    all_shape_patterns = defaultdict(int)
    
    for op in all_operations:
        op_type_summary[op.get("operation", "unknown")] += 1
        
        for shape in op.get("input_shapes", []):
            if len(shape) >= 2:
                all_shape_patterns[tuple(shape)] += 1
    
    # 创建报告数据
    report_data = {
        "summary": {
            "total_test_cases": len(inference_results),
            "total_operations": total_ops,
            "total_inference_time": total_inference_time,
            "operation_type_distribution": dict(op_type_summary),
            "shape_patterns": dict(sorted(all_shape_patterns.items(), key=lambda x: x[1], reverse=True)[:20])
        },
        "inference_details": inference_results,
        "memory_timeline": memory_records,
        "generation_time": time.strftime("%Y-%m-%d %H:%M:%S")
    }
    
    # 保存详细数据
    with open("simple_sglang_operator_analysis.json", 'w', encoding='utf-8') as f:
        json.dump(report_data, f, indent=2, ensure_ascii=False, default=str)
    
    # 生成Markdown报告
    generate_markdown_report(report_data)
    
    print("[Report] 简化算子分析报告已生成:")
    print("  - 详细数据: simple_sglang_operator_analysis.json")
    print("  - 分析报告: simple_sglang_operator_analysis_report.md")

def generate_markdown_report(report_data):
    """生成Markdown报告"""
    report = []
    report.append("# SGLang简化算子追踪分析报告\n")
    report.append(f"## 生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
    
    summary = report_data.get("summary", {})
    
    # 总体摘要
    report.append("## 总体摘要")
    report.append(f"- 测试案例数: {summary.get('total_test_cases', 0)}")
    report.append(f"- 总算子操作数: {summary.get('total_operations', 0)}")
    report.append(f"- 总推理时间: {summary.get('total_inference_time', 0):.3f}秒")
    
    # 操作类型分布
    op_dist = summary.get("operation_type_distribution", {})
    if op_dist:
        report.append("\n### 算子操作分布")
        for op_type, count in sorted(op_dist.items(), key=lambda x: x[1], reverse=True):
            report.append(f"- **{op_type}**: {count}次")
    
    # 形状模式分析
    shapes = summary.get("shape_patterns", {})
    if shapes:
        report.append("\n## 张量形状模式分析")
        report.append("### 最常见的张量形状:")
        for shape_tuple, count in list(shapes.items())[:15]:
            shape = list(shape_tuple)
            report.append(f"- `{shape}`: {count}次")
        
        # 与参考文档对比
        report.append("\n### 与参考算子形状对比")
        reference_patterns = {
            (128, 7168): "hidden_size相关",
            (128, 18432): "intermediate_size相关", 
            (128, 1536): "attention相关",
            (128, 576): "kv_attention相关",
            (7168, 18432): "dense层权重",
            (18432, 7168): "dense层权重",
            (1536, 7168): "attention权重",
            (7168, 1536): "attention权重"
        }
        
        matched_patterns = []
        for shape_tuple, count in shapes.items():
            # 检查最后两个维度
            if len(shape_tuple) >= 2:
                last_two = shape_tuple[-2:]
                if last_two in reference_patterns:
                    matched_patterns.append((list(shape_tuple), reference_patterns[last_two], count))
        
        if matched_patterns:
            report.append("#### 匹配的参考模式:")
            for shape, pattern_type, count in matched_patterns:
                report.append(f"- `{shape}` → {pattern_type} ({count}次)")
        else:
            report.append("#### 未找到明确匹配的参考模式")
    
    # 推理详情
    inference_details = report_data.get("inference_details", [])
    if inference_details:
        report.append("\n## 推理详情")
        for detail in inference_details:
            case_name = detail.get("case_name", "")
            prompt = detail.get("prompt", "")[:40]
            output = detail.get("output", "")[:60]
            inference_time = detail.get("inference_time", 0)
            op_count = detail.get("operation_count", 0)
            
            report.append(f"\n### {case_name}")
            report.append(f"- **提示**: {prompt}...")
            report.append(f"- **输出**: {output}...")
            report.append(f"- **推理时间**: {inference_time:.3f}秒")
            report.append(f"- **算子操作数**: {op_count}")
    
    # 内存使用分析
    memory_timeline = report_data.get("memory_timeline", [])
    if memory_timeline:
        report.append("\n## 内存使用分析")
        peak_memory = max(mem.get("allocated_gb", 0) for mem in memory_timeline)
        report.append(f"- **峰值内存使用**: {peak_memory:.2f}GB")
        
        report.append("\n### 内存使用时间线:")
        for mem in memory_timeline:
            stage = mem.get("stage", "")
            allocated = mem.get("allocated_gb", 0)
            report.append(f"- {stage}: {allocated:.2f}GB")
    
    # 技术结论
    report.append("\n## 技术结论")
    report.append("### 主要发现:")
    
    if summary.get("total_operations", 0) > 0:
        report.append("1. **成功捕获算子操作**: 通过Hook PyTorch核心函数成功追踪到实际的计算操作")
        
        dominant_op = max(op_dist.items(), key=lambda x: x[1])[0] if op_dist else "未知"
        report.append(f"2. **主要计算操作**: {dominant_op}是最频繁的算子操作")
        
        if shapes:
            main_shape = list(list(shapes.keys())[0])
            report.append(f"3. **主要张量形状**: {main_shape}是最常见的张量形状")
        
        avg_ops_per_inference = summary.get("total_operations", 0) / max(summary.get("total_test_cases", 1), 1)
        report.append(f"4. **计算复杂度**: 平均每次推理涉及{avg_ops_per_inference:.1f}个算子操作")
    else:
        report.append("1. **算子捕获挑战**: 未能捕获到算子操作，可能需要更深层的Hook机制")
        report.append("2. **SGLang内部优化**: SGLang可能使用了自定义算子或融合优化，绕过了标准PyTorch函数")
    
    report.append("\n### 优化建议:")
    report.append("1. 针对主要算子操作进行性能优化")
    report.append("2. 根据常见张量形状优化内存布局")
    report.append("3. 考虑算子融合以减少操作数量")
    
    # 保存报告
    with open("simple_sglang_operator_analysis_report.md", 'w', encoding='utf-8') as f:
        f.write('\n'.join(report))

def main():
    """主函数"""
    print("启动简化版SGLang算子追踪分析...")
    
    success = run_simple_sglang_test()
    
    print("\n" + "="*80)
    if success:
        print("简化版SGLang算子追踪分析完成！")
        if captured_operations:
            print(f"成功追踪到算子操作")
        else:
            print("未能捕获到算子操作，但获得了系统性能数据")
    else:
        print("分析过程遇到错误")
    print("="*80)

if __name__ == "__main__":
    main()
