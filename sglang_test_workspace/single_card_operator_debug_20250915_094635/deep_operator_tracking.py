#!/usr/bin/env python3
"""
深度算子追踪 - 多层hook机制
尝试从多个角度捕获算子运算
"""
import os
import sys
import time
import json
import torch
import traceback
from typing import Dict, List, Any
from collections import defaultdict

# 单卡环境配置
os.environ.setdefault("CUDA_VISIBLE_DEVICES", "0")
os.environ.setdefault("SGL_DISABLE_TP_MEMORY_INBALANCE_CHECK", "1")
os.environ.setdefault("UCX_VFS_ENABLE", "n")
os.environ.setdefault("NCCL_P2P_DISABLE", "1")
os.environ.setdefault("NCCL_IB_DISABLE", "1")
os.environ.setdefault("SGLANG_IS_FLASHINFER_AVAILABLE", "false")

# 全局记录
captured_operations = []
tensor_operations = []
module_forward_calls = []

class DeepOperatorTracker:
    """深度算子追踪器"""
    
    def __init__(self):
        self.start_time = None
        self.enabled = False
        self.original_functions = {}
        self.hooked_modules = set()
        
    def start_tracking(self):
        """开始深度追踪"""
        self.enabled = True
        self.start_time = time.time()
        print("[DeepTracker] 开始深度算子追踪")
        
        # 多层hook
        self._hook_torch_operations()
        self._hook_torch_tensor_methods()
        self._hook_torch_dispatcher()
        
    def stop_tracking(self):
        """停止追踪"""
        self.enabled = False
        self._restore_operations()
        print("[DeepTracker] 停止深度算子追踪")
    
    def _hook_torch_operations(self):
        """Hook PyTorch函数级操作"""
        self.original_functions = {
            'torch.mm': torch.mm,
            'torch.matmul': torch.matmul,
            'torch.addmm': torch.addmm,
            'torch.bmm': torch.bmm,
            'torch.baddbmm': torch.baddbmm,
            'F.linear': torch.nn.functional.linear,
            'F.conv1d': torch.nn.functional.conv1d,
            'F.conv2d': torch.nn.functional.conv2d,
            'F.embedding': torch.nn.functional.embedding,
            'F.layer_norm': torch.nn.functional.layer_norm,
            'F.softmax': torch.nn.functional.softmax,
            'F.relu': torch.nn.functional.relu,
            'F.gelu': torch.nn.functional.gelu,
            'F.silu': torch.nn.functional.silu,
        }
        
        # 包装所有目标函数
        for name, original_func in self.original_functions.items():
            wrapped_func = self._create_wrapper(name, original_func)
            
            # 根据名称设置到正确位置
            if name.startswith('F.'):
                func_name = name.split('.')[1]
                setattr(torch.nn.functional, func_name, wrapped_func)
            else:
                func_name = name.split('.')[1]
                setattr(torch, func_name, wrapped_func)
    
    def _create_wrapper(self, op_name, original_func):
        """创建函数包装器"""
        def wrapper(*args, **kwargs):
            result = original_func(*args, **kwargs)
            if self.enabled:
                self._log_operation(op_name, args, result, "function_level")
            return result
        return wrapper
    
    def _hook_torch_tensor_methods(self):
        """Hook Tensor方法级操作"""
        tensor_methods = ['mm', 'matmul', 'bmm', 'addmm', 'add', 'mul', 'div', 'transpose', 'view', 'reshape']
        
        for method_name in tensor_methods:
            if hasattr(torch.Tensor, method_name):
                original_method = getattr(torch.Tensor, method_name)
                
                def create_tensor_wrapper(orig_method, name):
                    def wrapper(self, *args, **kwargs):
                        result = orig_method(self, *args, **kwargs)
                        if self.enabled:
                            all_args = [self] + list(args)
                            self._log_operation(f"Tensor.{name}", all_args, result, "tensor_method")
                        return result
                    return wrapper
                
                wrapped_method = create_tensor_wrapper(original_method, method_name)
                self.original_functions[f'Tensor.{method_name}'] = original_method
                setattr(torch.Tensor, method_name, wrapped_method)
    
    def _hook_torch_dispatcher(self):
        """Hook torch调度器级别"""
        try:
            # 尝试hook更底层的操作
            if hasattr(torch.ops, 'aten'):
                print("[DeepTracker] 尝试hook aten操作...")
                # 这部分需要更深入的PyTorch内部知识
        except Exception as e:
            print(f"[DeepTracker] aten hook失败: {e}")
    
    def add_module_hooks(self, model):
        """为模型添加forward hook"""
        if not hasattr(model, 'modules'):
            print("[DeepTracker] 模型没有modules方法")
            return
            
        print("[DeepTracker] 添加模块forward hook...")
        
        def create_forward_hook(module_name):
            def hook_fn(module, input, output):
                if self.enabled:
                    self._log_module_forward(module_name, module, input, output)
            return hook_fn
        
        hook_count = 0
        for name, module in model.named_modules():
            if isinstance(module, (torch.nn.Linear, torch.nn.Embedding, torch.nn.LayerNorm)):
                hook = module.register_forward_hook(create_forward_hook(name))
                self.hooked_modules.add(hook)
                hook_count += 1
        
        print(f"[DeepTracker] 已添加{hook_count}个模块hook")
    
    def _log_operation(self, op_name, inputs, output, source):
        """记录操作"""
        try:
            input_shapes = []
            input_dtypes = []
            
            for inp in inputs:
                if isinstance(inp, torch.Tensor):
                    input_shapes.append(list(inp.shape))
                    input_dtypes.append(str(inp.dtype))
            
            output_shape = []
            output_dtype = ""
            
            if isinstance(output, torch.Tensor):
                output_shape = list(output.shape)
                output_dtype = str(output.dtype)
            elif isinstance(output, (list, tuple)):
                output_shape = [list(o.shape) if isinstance(o, torch.Tensor) else str(type(o)) for o in output]
            
            if input_shapes:  # 只记录有张量的操作
                operation_record = {
                    "operation": op_name,
                    "source": source,
                    "timestamp": time.time(),
                    "relative_time": time.time() - self.start_time,
                    "input_shapes": input_shapes,
                    "input_dtypes": input_dtypes,
                    "output_shape": output_shape,
                    "output_dtype": output_dtype
                }
                
                captured_operations.append(operation_record)
                
                # 实时打印重要操作
                print(f"[{source[:4]}] {op_name}: {input_shapes} -> {output_shape}")
                
        except Exception as e:
            print(f"[OpError] {op_name}: {e}")
    
    def _log_module_forward(self, module_name, module, input, output):
        """记录模块forward调用"""
        try:
            input_info = []
            if isinstance(input, (list, tuple)):
                for inp in input:
                    if isinstance(inp, torch.Tensor):
                        input_info.append({
                            "shape": list(inp.shape),
                            "dtype": str(inp.dtype)
                        })
            
            output_info = []
            if isinstance(output, torch.Tensor):
                output_info = {
                    "shape": list(output.shape),
                    "dtype": str(output.dtype)
                }
            elif isinstance(output, (list, tuple)):
                for out in output:
                    if isinstance(out, torch.Tensor):
                        output_info.append({
                            "shape": list(out.shape),
                            "dtype": str(out.dtype)
                        })
            
            module_record = {
                "module_name": module_name,
                "module_type": type(module).__name__,
                "timestamp": time.time(),
                "relative_time": time.time() - self.start_time,
                "input_info": input_info,
                "output_info": output_info
            }
            
            module_forward_calls.append(module_record)
            
            # 实时打印重要模块
            if any(kw in module_name.lower() for kw in ['linear', 'attention', 'mlp', 'embed']):
                print(f"[Module] {module_name} ({type(module).__name__}): {len(input_info)} inputs -> {output_info}")
                
        except Exception as e:
            print(f"[ModuleError] {module_name}: {e}")
    
    def _restore_operations(self):
        """恢复原始操作"""
        for name, original_func in self.original_functions.items():
            try:
                if name.startswith('F.'):
                    func_name = name.split('.')[1]
                    setattr(torch.nn.functional, func_name, original_func)
                elif name.startswith('Tensor.'):
                    func_name = name.split('.')[1]
                    setattr(torch.Tensor, func_name, original_func)
                else:
                    func_name = name.split('.')[1]
                    setattr(torch, func_name, original_func)
            except Exception as e:
                print(f"[RestoreError] {name}: {e}")
        
        # 清理模块hook
        for hook in self.hooked_modules:
            try:
                hook.remove()
            except Exception:
                pass
        self.hooked_modules.clear()

def run_deep_sglang_test():
    """运行深度SGLang测试"""
    MODEL_PATH = os.environ.get("MODEL_PATH", "/home/<USER>/deepseek-int8")
    
    print("="*80)
    print("深度SGLang算子追踪")
    print("="*80)
    
    tracker = DeepOperatorTracker()
    llm = None
    
    try:
        print("\n[SGLang] 初始化引擎...")
        import sglang as sgl
        
        # 启动追踪
        tracker.start_tracking()
        
        # 创建引擎
        engine_start = time.time()
        llm = sgl.Engine(
            model_path=MODEL_PATH,
            tp_size=1,
            quantization="w8a8_int8",
            trust_remote_code=True,
            disable_cuda_graph=True,
            log_level="error",
        )
        
        engine_time = time.time() - engine_start
        print(f"[SGLang] 引擎创建完成 ({engine_time:.2f}s)")
        
        # 尝试获取模型对象并添加hook
        try:
            if hasattr(llm, 'model'):
                tracker.add_module_hooks(llm.model)
            elif hasattr(llm, 'llm_engine') and hasattr(llm.llm_engine, 'model'):
                tracker.add_module_hooks(llm.llm_engine.model)
            else:
                print("[Warning] 无法找到模型对象添加hook")
        except Exception as e:
            print(f"[Warning] 添加模块hook失败: {e}")
        
        # 执行单个推理测试
        print(f"\n[Test] 深度追踪推理测试")
        
        # 清理之前的记录
        captured_operations.clear()
        module_forward_calls.clear()
        
        inference_start = time.time()
        
        # 使用简单采样参数
        sampling_params = {
            "max_new_tokens": 8,
            "temperature": 0.8
        }
        
        # 进行推理
        result = llm.generate(prompt="什么是深度学习？", sampling_params=sampling_params)
        
        inference_time = time.time() - inference_start
        output_text = result.get("text", str(result))
        
        print(f"[Result] ({inference_time:.3f}s): {output_text}")
        print(f"[Captured] 函数级操作: {len(captured_operations)}")
        print(f"[Captured] 模块forward调用: {len(module_forward_calls)}")
        
        # 生成详细分析报告
        generate_deep_analysis_report()
        
        return True
        
    except Exception as e:
        print(f"[Error] 深度测试失败: {e}")
        traceback.print_exc()
        return False
        
    finally:
        tracker.stop_tracking()
        
        if llm is not None:
            try:
                llm.shutdown()
            except Exception:
                pass

def generate_deep_analysis_report():
    """生成深度分析报告"""
    print("\n[Report] 生成深度分析报告...")
    
    # 分析函数级操作
    op_analysis = analyze_function_operations()
    
    # 分析模块级操作
    module_analysis = analyze_module_operations()
    
    # 创建综合报告
    report_data = {
        "summary": {
            "function_level_operations": len(captured_operations),
            "module_level_forwards": len(module_forward_calls),
            "generation_time": time.strftime("%Y-%m-%d %H:%M:%S")
        },
        "function_operations": captured_operations,
        "module_forwards": module_forward_calls,
        "analysis": {
            "function_analysis": op_analysis,
            "module_analysis": module_analysis
        }
    }
    
    # 保存详细数据
    with open("deep_sglang_operator_analysis.json", 'w', encoding='utf-8') as f:
        json.dump(report_data, f, indent=2, ensure_ascii=False, default=str)
    
    # 生成Markdown报告
    generate_deep_markdown_report(report_data)
    
    print("[Report] 深度算子分析报告已生成:")
    print("  - 详细数据: deep_sglang_operator_analysis.json")
    print("  - 分析报告: deep_sglang_operator_analysis_report.md")

def analyze_function_operations():
    """分析函数级操作"""
    if not captured_operations:
        return {"message": "未捕获到函数级操作"}
    
    # 统计操作类型
    op_counts = defaultdict(int)
    source_counts = defaultdict(int)
    shape_patterns = defaultdict(int)
    
    for op in captured_operations:
        op_name = op.get("operation", "unknown")
        source = op.get("source", "unknown")
        
        op_counts[op_name] += 1
        source_counts[source] += 1
        
        # 分析形状模式
        input_shapes = op.get("input_shapes", [])
        for shape in input_shapes:
            if len(shape) >= 2:
                shape_patterns[tuple(shape)] += 1
    
    return {
        "operation_counts": dict(op_counts),
        "source_distribution": dict(source_counts),
        "shape_patterns": dict(sorted(shape_patterns.items(), key=lambda x: x[1], reverse=True))
    }

def analyze_module_operations():
    """分析模块级操作"""
    if not module_forward_calls:
        return {"message": "未捕获到模块级操作"}
    
    # 统计模块类型
    module_type_counts = defaultdict(int)
    module_name_patterns = defaultdict(int)
    
    for call in module_forward_calls:
        module_type = call.get("module_type", "unknown")
        module_name = call.get("module_name", "")
        
        module_type_counts[module_type] += 1
        
        # 提取模块名称模式
        if '.' in module_name:
            pattern = '.'.join(module_name.split('.')[:2])  # 取前两级
            module_name_patterns[pattern] += 1
    
    return {
        "module_type_counts": dict(module_type_counts),
        "module_name_patterns": dict(sorted(module_name_patterns.items(), key=lambda x: x[1], reverse=True))
    }

def generate_deep_markdown_report(report_data):
    """生成深度Markdown报告"""
    report = []
    report.append("# SGLang深度算子追踪分析报告\n")
    report.append(f"## 生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
    
    summary = report_data.get("summary", {})
    
    # 总体摘要
    report.append("## 总体摘要")
    report.append(f"- 函数级操作数: {summary.get('function_level_operations', 0)}")
    report.append(f"- 模块级forward调用数: {summary.get('module_level_forwards', 0)}")
    
    # 函数级分析
    func_analysis = report_data.get("analysis", {}).get("function_analysis", {})
    if "operation_counts" in func_analysis:
        report.append("\n## 函数级操作分析")
        
        op_counts = func_analysis["operation_counts"]
        if op_counts:
            report.append("### 操作类型分布:")
            for op_type, count in sorted(op_counts.items(), key=lambda x: x[1], reverse=True):
                report.append(f"- **{op_type}**: {count}次")
        
        source_dist = func_analysis.get("source_distribution", {})
        if source_dist:
            report.append("\n### 操作来源分布:")
            for source, count in sorted(source_dist.items(), key=lambda x: x[1], reverse=True):
                report.append(f"- **{source}**: {count}次")
        
        shapes = func_analysis.get("shape_patterns", {})
        if shapes:
            report.append("\n### 张量形状模式:")
            for shape_tuple, count in list(shapes.items())[:10]:
                shape = list(shape_tuple)
                report.append(f"- `{shape}`: {count}次")
    else:
        report.append(f"\n## 函数级操作分析\n{func_analysis.get('message', '无数据')}")
    
    # 模块级分析
    module_analysis = report_data.get("analysis", {}).get("module_analysis", {})
    if "module_type_counts" in module_analysis:
        report.append("\n## 模块级操作分析")
        
        type_counts = module_analysis["module_type_counts"]
        if type_counts:
            report.append("### 模块类型分布:")
            for module_type, count in sorted(type_counts.items(), key=lambda x: x[1], reverse=True):
                report.append(f"- **{module_type}**: {count}次")
        
        name_patterns = module_analysis.get("module_name_patterns", {})
        if name_patterns:
            report.append("\n### 模块名称模式:")
            for pattern, count in list(name_patterns.items())[:10]:
                report.append(f"- **{pattern}**: {count}次")
    else:
        report.append(f"\n## 模块级操作分析\n{module_analysis.get('message', '无数据')}")
    
    # 技术结论
    report.append("\n## 技术结论")
    report.append("### 主要发现:")
    
    total_ops = summary.get('function_level_operations', 0) + summary.get('module_level_forwards', 0)
    
    if total_ops > 0:
        report.append(f"1. **成功捕获操作**: 总共捕获到{total_ops}个操作调用")
        
        if summary.get('function_level_operations', 0) > 0:
            report.append("2. **函数级追踪成功**: PyTorch函数级hook生效")
        
        if summary.get('module_level_forwards', 0) > 0:
            report.append("3. **模块级追踪成功**: 神经网络模块forward hook生效")
        
        report.append("4. **SGLang内部结构**: 可以通过多层hook机制捕获到计算操作")
    else:
        report.append("1. **深度追踪挑战**: 多层hook机制仍未捕获到操作，SGLang可能使用了更底层的优化")
        report.append("2. **需要更深入分析**: 可能需要直接hook CUDA kernels或使用profiling工具")
    
    report.append("\n### 下一步建议:")
    report.append("1. 使用PyTorch Profiler进行更深入的性能分析")
    report.append("2. 检查SGLang是否使用了自定义CUDA kernels")
    report.append("3. 尝试hook更底层的操作如aten操作")
    
    # 保存报告
    with open("deep_sglang_operator_analysis_report.md", 'w', encoding='utf-8') as f:
        f.write('\n'.join(report))

def main():
    """主函数"""
    print("启动深度SGLang算子追踪分析...")
    
    success = run_deep_sglang_test()
    
    print("\n" + "="*80)
    if success:
        print("深度SGLang算子追踪分析完成！")
        total_ops = len(captured_operations) + len(module_forward_calls)
        if total_ops > 0:
            print(f"成功追踪到{total_ops}个操作")
        else:
            print("仍未捕获到算子操作，需要更深层的分析方法")
    else:
        print("深度分析过程遇到错误")
    print("="*80)

if __name__ == "__main__":
    main()
