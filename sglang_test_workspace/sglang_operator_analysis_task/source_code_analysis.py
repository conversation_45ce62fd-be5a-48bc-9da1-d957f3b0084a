#!/usr/bin/env python3
"""
SGLang 源码算子分析脚本
深度分析sglang源码，提取所有相关的算子操作和shape信息
"""

import os
import sys
import json
import re
from pathlib import Path
from typing import Dict, List, Any, Set
from collections import defaultdict

def analyze_source_files():
    """分析sglang源码文件"""
    sglang_path = Path("/workspace/sglang_test/lib/python3.10/site-packages/sglang")
    
    # 需要分析的关键文件
    key_files = [
        "srt/models/deepseek.py",
        "srt/model_executor/model_runner.py", 
        "srt/layers/linear.py",
        "srt/layers/attention/",
        "srt/layers/moe/",
        "srt/layers/quantization/",
        "srt/layers/radix_attention.py",
        "srt/layers/rotary_embedding.py",
        "srt/layers/layernorm.py",
        "srt/layers/activation.py",
        "srt/layers/sampler.py",
        "srt/layers/vocab_parallel_embedding.py"
    ]
    
    operator_info = {
        "linear_operators": [],
        "attention_operators": [],
        "mlp_operators": [],
        "moe_operators": [],
        "normalization_operators": [],
        "activation_operators": [],
        "embedding_operators": [],
        "quantization_operators": [],
        "shape_computations": []
    }
    
    def extract_operators_from_file(file_path: Path, category: str):
        """从文件中提取算子信息"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找类定义
            class_pattern = r'class\s+(\w+)\([^)]*\):'
            classes = re.findall(class_pattern, content)
            
            # 查找forward方法
            forward_pattern = r'def\s+forward\s*\([^)]*\).*?(?=def|\nclass|\Z)'
            forward_methods = re.findall(forward_pattern, content, re.DOTALL)
            
            # 查找torch操作
            torch_ops_pattern = r'torch\.(\w+)\('
            torch_ops = re.findall(torch_ops_pattern, content)
            
            # 查找矩阵乘法等操作
            matmul_pattern = r'@|torch\.matmul|torch\.bmm|torch\.mm'
            matmuls = re.findall(matmul_pattern, content)
            
            # 查找shape相关操作
            shape_pattern = r'\.shape|\.size\(\)|\.view\(|\.reshape\('
            shape_ops = re.findall(shape_pattern, content)
            
            info = {
                "file": str(file_path.relative_to(sglang_path)),
                "classes": classes,
                "torch_operations": list(set(torch_ops)),
                "matrix_operations": len(matmuls),
                "shape_operations": len(shape_ops),
                "forward_methods_count": len(forward_methods)
            }
            
            operator_info[category].append(info)
            
        except Exception as e:
            print(f"分析文件 {file_path} 时出错: {e}")
    
    # 分析各类文件
    for key_file in key_files:
        file_path = sglang_path / key_file
        
        if file_path.is_file():
            if "linear" in key_file:
                extract_operators_from_file(file_path, "linear_operators")
            elif "attention" in key_file:
                extract_operators_from_file(file_path, "attention_operators") 
            elif "moe" in key_file:
                extract_operators_from_file(file_path, "moe_operators")
            elif "layernorm" in key_file or "norm" in key_file:
                extract_operators_from_file(file_path, "normalization_operators")
            elif "activation" in key_file:
                extract_operators_from_file(file_path, "activation_operators")
            elif "embedding" in key_file:
                extract_operators_from_file(file_path, "embedding_operators")
            elif "quantization" in key_file:
                extract_operators_from_file(file_path, "quantization_operators")
            else:
                extract_operators_from_file(file_path, "shape_computations")
        elif file_path.is_dir():
            # 处理目录
            for sub_file in file_path.rglob("*.py"):
                if "attention" in key_file:
                    extract_operators_from_file(sub_file, "attention_operators")
                elif "moe" in key_file:
                    extract_operators_from_file(sub_file, "moe_operators")
                elif "quantization" in key_file:
                    extract_operators_from_file(sub_file, "quantization_operators")
    
    return operator_info

def analyze_deepseek_model_structure():
    """深度分析DeepSeek模型结构"""
    deepseek_file = Path("/workspace/sglang_test/lib/python3.10/site-packages/sglang/srt/models/deepseek.py")
    
    model_analysis = {
        "model_components": {},
        "layer_structure": {},
        "operator_flow": {},
        "shape_transformations": []
    }
    
    try:
        with open(deepseek_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 分析DeepseekMLP
        mlp_analysis = {
            "components": ["gate_up_proj", "down_proj", "act_fn"],
            "operations": [
                "MergedColumnParallelLinear (gate + up projection)",
                "SiluAndMul activation",
                "RowParallelLinear (down projection)"
            ],
            "shape_flow": [
                "input: [batch_size, seq_len, hidden_size]",
                "gate_up_proj: [batch_size, seq_len, 2 * intermediate_size]", 
                "activation: [batch_size, seq_len, intermediate_size]",
                "down_proj: [batch_size, seq_len, hidden_size]"
            ]
        }
        
        # 分析DeepseekMoE
        moe_analysis = {
            "components": ["gate", "experts", "shared_experts", "topk"],
            "operations": [
                "Router gate computation",
                "Expert selection (TopK)",
                "Fused MoE computation",
                "Shared experts (if present)",
                "All-reduce for tensor parallelism"
            ],
            "expert_ops": [
                "Gate projection",
                "Up projection", 
                "Down projection",
                "SiLU activation"
            ]
        }
        
        # 分析DeepseekAttention
        attention_analysis = {
            "components": ["qkv_proj", "rotary_emb", "attn", "o_proj"],
            "operations": [
                "QKV parallel linear projection",
                "Rotary position embedding",
                "Radix attention computation",
                "Output projection"
            ],
            "shape_flow": [
                "input: [batch_size, seq_len, hidden_size]",
                "qkv: [batch_size, seq_len, (num_heads + 2*num_kv_heads) * head_dim]",
                "q: [batch_size, seq_len, num_heads * head_dim]",
                "k,v: [batch_size, seq_len, num_kv_heads * head_dim]",
                "attention_output: [batch_size, seq_len, num_heads * head_dim]",
                "output: [batch_size, seq_len, hidden_size]"
            ]
        }
        
        model_analysis["model_components"] = {
            "DeepseekMLP": mlp_analysis,
            "DeepseekMoE": moe_analysis,
            "DeepseekAttention": attention_analysis
        }
        
    except Exception as e:
        print(f"分析DeepSeek模型时出错: {e}")
    
    return model_analysis

def analyze_quantization_operators():
    """分析量化相关算子"""
    quant_analysis = {
        "w8a8_operators": [],
        "int8_kernels": [],
        "quantization_flow": [],
        "dequantization_flow": []
    }
    
    # 分析量化文件夹
    quant_path = Path("/workspace/sglang_test/lib/python3.10/site-packages/sglang/srt/layers/quantization")
    
    if quant_path.exists():
        for quant_file in quant_path.rglob("*.py"):
            try:
                with open(quant_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 查找量化相关函数
                quant_functions = re.findall(r'def\s+(\w*quant\w*)\s*\(', content, re.IGNORECASE)
                dequant_functions = re.findall(r'def\s+(\w*dequant\w*)\s*\(', content, re.IGNORECASE)
                
                # 查找int8相关操作
                int8_ops = re.findall(r'int8|INT8|torch\.int8', content)
                
                # 查找kernel调用
                kernel_calls = re.findall(r'torch\.ops\.\w+|cuda\.\w+', content)
                
                file_analysis = {
                    "file": str(quant_file.relative_to(quant_path)),
                    "quantization_functions": quant_functions,
                    "dequantization_functions": dequant_functions,
                    "int8_operations": len(int8_ops),
                    "kernel_calls": kernel_calls
                }
                
                quant_analysis["w8a8_operators"].append(file_analysis)
                
            except Exception as e:
                print(f"分析量化文件 {quant_file} 时出错: {e}")
    
    return quant_analysis

def generate_operator_summary():
    """生成算子总结"""
    print("开始源码分析...")
    
    # 执行各种分析
    source_analysis = analyze_source_files()
    model_analysis = analyze_deepseek_model_structure()
    quant_analysis = analyze_quantization_operators()
    
    # 生成综合报告
    full_report = {
        "analysis_timestamp": "2025-09-16",
        "source_code_analysis": source_analysis,
        "deepseek_model_analysis": model_analysis,
        "quantization_analysis": quant_analysis,
        "operator_categories": {
            "core_operators": [
                "Linear transformations (MLP gate, up, down projections)",
                "Attention mechanisms (QKV, O projections)",
                "MoE routing and expert computation",
                "Quantization/Dequantization (W8A8)",
                "Normalization (RMSNorm)",
                "Activations (SiLU, SiluAndMul)",
                "Embeddings (Token, Position)"
            ],
            "prefill_specific": [
                "Sequence-level attention computation",
                "Batch matrix multiplications",
                "Large activation computation",
                "KV cache filling"
            ],
            "decode_specific": [
                "Single-token attention",
                "Incremental KV cache update",
                "Sampler operations",
                "Autoregressive generation"
            ]
        },
        "shape_analysis": {
            "typical_shapes": {
                "embedding": "[batch_size, seq_len, hidden_size]",
                "attention_weights": "[batch_size, num_heads, seq_len, seq_len]", 
                "mlp_intermediate": "[batch_size, seq_len, intermediate_size]",
                "logits": "[batch_size, seq_len, vocab_size]"
            },
            "tp_sharding": {
                "attention_heads": "num_heads // tp_size",
                "mlp_intermediate": "intermediate_size // tp_size",
                "vocab_parallel": "vocab_size // tp_size"
            }
        }
    }
    
    return full_report

def main():
    """主函数"""
    print("开始SGLang源码算子分析...")
    
    try:
        # 生成分析报告
        report = generate_operator_summary()
        
        # 保存报告
        output_file = "sglang_operator_source_analysis.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"源码分析完成，报告已保存到: {output_file}")
        
        # 打印关键信息
        print("\n=== 核心算子类别 ===")
        for category in report["operator_categories"]["core_operators"]:
            print(f"- {category}")
        
        print("\n=== Prefill阶段特有算子 ===")
        for op in report["operator_categories"]["prefill_specific"]:
            print(f"- {op}")
            
        print("\n=== Decode阶段特有算子 ===")
        for op in report["operator_categories"]["decode_specific"]:
            print(f"- {op}")
        
    except Exception as e:
        print(f"分析过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
