#!/usr/bin/env python3
"""
SGLang 算子追踪分析脚本
分析模型加载和推理过程中的算子计算流程，包括prefill和decode两个阶段
"""
import os
import sys
import traceback
import json
import time
from typing import Dict, List, Any
from collections import defaultdict

# 必须在导入 sglang 前设置分布式/通信相关环境
os.environ.setdefault("SGL_DISABLE_TP_MEMORY_INBALANCE_CHECK", "1")
os.environ.setdefault("UCX_VFS_ENABLE", "n")
os.environ.setdefault("NCCL_P2P_DISABLE", "1")
os.environ.setdefault("NCCL_IB_DISABLE", "1")
os.environ.setdefault("SGLANG_IS_FLASHINFER_AVAILABLE", "false")

import torch
import torch.nn as nn
import sglang as sgl

MODEL_PATH = os.environ.get("MODEL_PATH", "/home/<USER>/deepseek-int8")

# 全局追踪记录器
class OperatorTracker:
    def __init__(self):
        self.forward_calls = []
        self.current_phase = "unknown"  # prefill, decode, idle
        self.layer_calls = defaultdict(list)
        self.operator_shapes = {}
        
    def reset(self):
        self.forward_calls = []
        self.layer_calls = defaultdict(list)
        self.operator_shapes = {}
        
    def record_operation(self, op_name: str, input_shape: tuple, output_shape: tuple, 
                        module_name: str = "", layer_id: int = -1):
        """记录算子操作"""
        record = {
            "phase": self.current_phase,
            "op_name": op_name,
            "module_name": module_name,
            "layer_id": layer_id,
            "input_shape": input_shape,
            "output_shape": output_shape,
            "timestamp": time.time()
        }
        self.forward_calls.append(record)
        if layer_id >= 0:
            self.layer_calls[layer_id].append(record)
    
    def set_phase(self, phase: str):
        self.current_phase = phase
        print(f"[TRACE] 切换到阶段: {phase}")

# 全局追踪器实例
tracker = OperatorTracker()

# Hook函数来追踪模块的forward调用
def create_forward_hook(module_name: str, layer_id: int = -1):
    def hook_fn(module, input, output):
        if isinstance(input, (tuple, list)) and len(input) > 0:
            first_input = input[0]
            if isinstance(first_input, torch.Tensor):
                input_shape = tuple(first_input.shape)
            else:
                input_shape = "non-tensor"
        else:
            input_shape = "no-input"
            
        if isinstance(output, torch.Tensor):
            output_shape = tuple(output.shape)
        elif isinstance(output, (tuple, list)) and len(output) > 0:
            if isinstance(output[0], torch.Tensor):
                output_shape = tuple(output[0].shape)
            else:
                output_shape = "mixed-output"
        else:
            output_shape = "no-output"
            
        tracker.record_operation(
            op_name=module.__class__.__name__,
            input_shape=input_shape,
            output_shape=output_shape,
            module_name=module_name,
            layer_id=layer_id
        )
        
    return hook_fn

def register_hooks_for_model(model):
    """为模型的关键模块注册hook"""
    print("[TRACE] 注册模型hook开始...")
    
    # 为整个模型注册hook
    model.register_forward_hook(create_forward_hook("DeepseekForCausalLM"))
    
    # 为主要模块注册hook
    if hasattr(model, 'model'):
        model.model.register_forward_hook(create_forward_hook("DeepseekModel"))
        
        # embedding层
        if hasattr(model.model, 'embed_tokens'):
            model.model.embed_tokens.register_forward_hook(
                create_forward_hook("VocabParallelEmbedding"))
        
        # 各个decoder层
        if hasattr(model.model, 'layers'):
            for i, layer in enumerate(model.model.layers):
                layer.register_forward_hook(create_forward_hook(f"DeepseekDecoderLayer-{i}", i))
                
                # attention模块
                if hasattr(layer, 'self_attn'):
                    layer.self_attn.register_forward_hook(
                        create_forward_hook(f"DeepseekAttention-{i}", i))
                    
                    # qkv投影
                    if hasattr(layer.self_attn, 'qkv_proj'):
                        layer.self_attn.qkv_proj.register_forward_hook(
                            create_forward_hook(f"QKVParallelLinear-{i}", i))
                    
                    # 输出投影
                    if hasattr(layer.self_attn, 'o_proj'):
                        layer.self_attn.o_proj.register_forward_hook(
                            create_forward_hook(f"RowParallelLinear-{i}", i))
                
                # MLP/MoE模块
                if hasattr(layer, 'mlp'):
                    mlp_type = "DeepseekMoE" if "MoE" in str(type(layer.mlp)) else "DeepseekMLP"
                    layer.mlp.register_forward_hook(
                        create_forward_hook(f"{mlp_type}-{i}", i))
                    
                    # MLP的具体层
                    if hasattr(layer.mlp, 'gate_up_proj'):
                        layer.mlp.gate_up_proj.register_forward_hook(
                            create_forward_hook(f"MLP-gate_up_proj-{i}", i))
                    if hasattr(layer.mlp, 'down_proj'):
                        layer.mlp.down_proj.register_forward_hook(
                            create_forward_hook(f"MLP-down_proj-{i}", i))
                    
                    # MoE特有的部分
                    if hasattr(layer.mlp, 'gate'):  # MoE gate
                        layer.mlp.gate.register_forward_hook(
                            create_forward_hook(f"MoE-gate-{i}", i))
                    if hasattr(layer.mlp, 'shared_experts'):  # shared experts
                        layer.mlp.shared_experts.register_forward_hook(
                            create_forward_hook(f"MoE-shared_experts-{i}", i))
        
        # 最终norm层
        if hasattr(model.model, 'norm'):
            model.model.norm.register_forward_hook(create_forward_hook("RMSNorm-final"))
    
    # lm_head
    if hasattr(model, 'lm_head'):
        model.lm_head.register_forward_hook(create_forward_hook("ParallelLMHead"))
    
    print("[TRACE] 模型hook注册完成")

def analyze_operator_statistics():
    """分析算子统计信息"""
    print("\n" + "="*80)
    print("算子统计分析")
    print("="*80)
    
    # 按阶段分组
    phase_ops = defaultdict(list)
    for call in tracker.forward_calls:
        phase_ops[call["phase"]].append(call)
    
    for phase, ops in phase_ops.items():
        print(f"\n--- {phase.upper()} 阶段 ---")
        print(f"总操作数: {len(ops)}")
        
        # 按操作类型统计
        op_types = defaultdict(int)
        for op in ops:
            op_types[op["op_name"]] += 1
        
        print("操作类型统计:")
        for op_type, count in sorted(op_types.items()):
            print(f"  {op_type}: {count}")
    
    # 按层分析
    print(f"\n--- 按层分析 ---")
    for layer_id in sorted(tracker.layer_calls.keys()):
        ops = tracker.layer_calls[layer_id]
        print(f"第{layer_id}层: {len(ops)} 个操作")
        
        layer_op_types = defaultdict(int)
        for op in ops:
            layer_op_types[op["op_name"]] += 1
        for op_type, count in layer_op_types.items():
            print(f"  {op_type}: {count}")

def save_analysis_to_file(filename: str):
    """保存分析结果到文件"""
    result = {
        "summary": {
            "total_operations": len(tracker.forward_calls),
            "phases": list(set(call["phase"] for call in tracker.forward_calls)),
            "layers_analyzed": list(tracker.layer_calls.keys())
        },
        "operations": tracker.forward_calls,
        "layer_breakdown": dict(tracker.layer_calls),
        "timestamp": time.time()
    }
    
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(result, f, indent=2, ensure_ascii=False)
    print(f"[TRACE] 分析结果已保存到: {filename}")

def main():
    """主函数"""
    llm = None
    try:
        print("[TRACE] 开始初始化SGLang引擎...")
        tracker.set_phase("initialization")
        
        # 直接离线引擎加载（不启动HTTP服务）
        llm = sgl.Engine(
            model_path=MODEL_PATH,
            tp_size=4,
            quantization="w8a8_int8",
            trust_remote_code=True,
            dist_init_addr="127.0.0.1:8001",
            disable_cuda_graph=True,
            log_level="info",
        )
        
        print("[TRACE] 引擎初始化完成，开始注册hook...")
        
        # 注册hook来追踪算子
        register_hooks_for_model(llm.model_runner.model)
        
        print("[TRACE] 开始PREFILL阶段测试...")
        tracker.set_phase("prefill")
        
        # 第一次推理 - prefill阶段
        prompt = "用一句话介绍你自己。"
        sampling_params = {"max_new_tokens": 1, "temperature": 0.7}
        
        # 重置追踪记录
        tracker.reset()
        tracker.set_phase("prefill")
        
        out = llm.generate(prompt=prompt, sampling_params=sampling_params)
        print(f"[TRACE] Prefill阶段完成: {out.get('text', out)}")
        
        # 分析prefill阶段
        print(f"[TRACE] Prefill阶段记录了 {len(tracker.forward_calls)} 个操作")
        prefill_ops = tracker.forward_calls.copy()
        
        # 保存prefill分析
        save_analysis_to_file("prefill_analysis.json")
        
        print("[TRACE] 开始DECODE阶段测试...")
        tracker.reset()
        tracker.set_phase("decode")
        
        # 继续生成更多token - decode阶段
        sampling_params_decode = {"max_new_tokens": 5, "temperature": 0.7}
        out = llm.generate(prompt=prompt, sampling_params=sampling_params_decode)
        print(f"[TRACE] Decode阶段完成: {out.get('text', out)}")
        
        # 分析decode阶段
        print(f"[TRACE] Decode阶段记录了 {len(tracker.forward_calls)} 个操作")
        decode_ops = tracker.forward_calls.copy()
        
        # 保存decode分析
        save_analysis_to_file("decode_analysis.json")
        
        # 合并分析
        tracker.forward_calls = prefill_ops + decode_ops
        analyze_operator_statistics()
        save_analysis_to_file("full_analysis.json")
        
    except Exception as e:
        print(f"[TRACE] 错误: {e}")
        print(traceback.format_exc())
        sys.exit(1)
    finally:
        if llm is not None:
            try:
                llm.shutdown()
            except Exception:
                pass

if __name__ == "__main__":
    main()
