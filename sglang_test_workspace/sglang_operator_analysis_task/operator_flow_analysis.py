#!/usr/bin/env python3
"""
SGLang 算子流程分析脚本 - 基于源码分析和理论推导
"""

import json
import os
from typing import Dict, List, Any

def analyze_deepseek_operator_flow():
    """分析DeepSeek模型的算子流程"""
    
    # 基于源码分析得出的DeepSeek模型结构
    model_structure = {
        "model_info": {
            "model_type": "DeepSeekV3ForCausalLMNextN",
            "quantization": "W8A8-INT8",
            "tensor_parallel_size": 4,
            "hidden_size": 7168,  # 推测值，实际需要从配置获取
            "intermediate_size": 18944,  # 推测值
            "num_layers": 61,  # 推测值
            "num_attention_heads": 56,  # 推测值  
            "num_key_value_heads": 8,  # 推测值
            "head_dim": 128,  # 推测值
            "vocab_size": 129280  # 推测值
        },
        
        "prefill_phase": {
            "description": "处理完整的输入序列，生成第一个token",
            "main_operations": [
                {
                    "operation": "Embedding Lookup",
                    "module": "VocabParallelEmbedding",
                    "input_shape": "[batch_size, seq_len]",
                    "output_shape": "[batch_size, seq_len, hidden_size]",
                    "operator_type": "embedding",
                    "quantization": "不需要量化"
                },
                {
                    "operation": "Layer Loop (61 layers)",
                    "layers": []
                }
            ]
        },
        
        "decode_phase": {
            "description": "逐个生成后续token，每次输入单个token",
            "main_operations": [
                {
                    "operation": "Embedding Lookup", 
                    "module": "VocabParallelEmbedding",
                    "input_shape": "[batch_size, 1]",
                    "output_shape": "[batch_size, 1, hidden_size]",
                    "operator_type": "embedding",
                    "quantization": "不需要量化"
                },
                {
                    "operation": "Layer Loop (61 layers)",
                    "layers": []
                }
            ]
        }
    }
    
    # 为每一层添加详细的算子信息
    for phase in ["prefill_phase", "decode_phase"]:
        is_prefill = (phase == "prefill_phase")
        seq_len_desc = "seq_len" if is_prefill else "1"
        
        layer_operations = []
        
        for layer_id in range(model_structure["model_info"]["num_layers"]):
            layer_ops = {
                "layer_id": layer_id,
                "layer_type": "DeepseekDecoderLayer",
                "operations": [
                    # 1. Layer Norm (Input)
                    {
                        "op_name": "Input LayerNorm",
                        "module": "RMSNorm",
                        "input_shape": f"[batch_size, {seq_len_desc}, hidden_size]",
                        "output_shape": f"[batch_size, {seq_len_desc}, hidden_size]",
                        "operator_type": "normalization",
                        "params": {
                            "eps": "rms_norm_eps",
                            "hidden_size": 7168
                        },
                        "computation": "RMS normalization"
                    },
                    
                    # 2. Attention Block
                    {
                        "op_name": "Self Attention",
                        "module": "DeepseekAttention", 
                        "sub_operations": [
                            {
                                "op_name": "QKV Projection",
                                "module": "QKVParallelLinear",
                                "input_shape": f"[batch_size, {seq_len_desc}, hidden_size]",
                                "output_shape": f"[batch_size, {seq_len_desc}, (num_heads + 2*num_kv_heads) * head_dim]",
                                "operator_type": "linear",
                                "quantization": "W8A8-INT8",
                                "weight_shape": "[hidden_size, (num_heads + 2*num_kv_heads) * head_dim]",
                                "computation": "Linear transformation for Q, K, V"
                            },
                            {
                                "op_name": "Split QKV",
                                "computation": "Split concatenated QKV into separate Q, K, V tensors",
                                "q_shape": f"[batch_size, {seq_len_desc}, num_heads * head_dim]",
                                "k_shape": f"[batch_size, {seq_len_desc}, num_kv_heads * head_dim]", 
                                "v_shape": f"[batch_size, {seq_len_desc}, num_kv_heads * head_dim]"
                            },
                            {
                                "op_name": "Rotary Position Embedding",
                                "module": "get_rope",
                                "input": "Q, K tensors",
                                "computation": "Apply rotary position encoding to Q and K",
                                "operator_type": "position_encoding"
                            },
                            {
                                "op_name": "Attention Computation",
                                "module": "RadixAttention",
                                "backend": "triton",
                                "computation": "Scaled dot-product attention with KV caching",
                                "operator_type": "attention",
                                "kv_cache_update": "Update KV cache with new K, V values",
                                "attention_pattern": "causal" if is_prefill else "single_token"
                            },
                            {
                                "op_name": "Output Projection", 
                                "module": "RowParallelLinear",
                                "input_shape": f"[batch_size, {seq_len_desc}, num_heads * head_dim]",
                                "output_shape": f"[batch_size, {seq_len_desc}, hidden_size]",
                                "operator_type": "linear",
                                "quantization": "W8A8-INT8",
                                "weight_shape": "[num_heads * head_dim, hidden_size]",
                                "computation": "Linear transformation of attention output"
                            }
                        ]
                    },
                    
                    # 3. Layer Norm (Post Attention)
                    {
                        "op_name": "Post Attention LayerNorm",
                        "module": "RMSNorm", 
                        "input_shape": f"[batch_size, {seq_len_desc}, hidden_size]",
                        "output_shape": f"[batch_size, {seq_len_desc}, hidden_size]",
                        "operator_type": "normalization",
                        "computation": "RMS normalization"
                    },
                    
                    # 4. MLP/MoE Block
                    {
                        "op_name": "MLP/MoE Block",
                        "module": "DeepseekMoE" if layer_id >= 1 and layer_id % 3 == 0 else "DeepseekMLP",  # 推测MoE层分布
                        "sub_operations": []
                    }
                ]
            }
            
            # 添加MLP/MoE的具体操作
            if layer_id >= 1 and layer_id % 3 == 0:  # MoE层
                moe_ops = [
                    {
                        "op_name": "Router Gate",
                        "module": "ReplicatedLinear",
                        "input_shape": f"[batch_size, {seq_len_desc}, hidden_size]",
                        "output_shape": f"[batch_size, {seq_len_desc}, n_routed_experts]",
                        "operator_type": "linear",
                        "computation": "Expert routing logits computation"
                    },
                    {
                        "op_name": "TopK Selection",
                        "module": "TopK",
                        "computation": "Select top-k experts for each token",
                        "operator_type": "selection"
                    },
                    {
                        "op_name": "Expert Computation",
                        "module": "fused_moe",
                        "sub_operations": [
                            {
                                "op_name": "Gate+Up Projection",
                                "input_shape": f"[num_tokens, hidden_size]",
                                "output_shape": f"[num_tokens, 2 * moe_intermediate_size]",
                                "operator_type": "linear",
                                "quantization": "W8A8-INT8"
                            },
                            {
                                "op_name": "SiLU Activation",
                                "operator_type": "activation"
                            },
                            {
                                "op_name": "Down Projection", 
                                "input_shape": f"[num_tokens, moe_intermediate_size]",
                                "output_shape": f"[num_tokens, hidden_size]",
                                "operator_type": "linear",
                                "quantization": "W8A8-INT8"
                            }
                        ]
                    },
                    {
                        "op_name": "Shared Experts (if present)",
                        "module": "DeepseekMLP",
                        "sub_operations": [
                            {
                                "op_name": "Gate+Up Projection",
                                "operator_type": "linear",
                                "quantization": "W8A8-INT8"
                            },
                            {
                                "op_name": "SiLU Activation",
                                "operator_type": "activation"
                            },
                            {
                                "op_name": "Down Projection",
                                "operator_type": "linear", 
                                "quantization": "W8A8-INT8"
                            }
                        ]
                    },
                    {
                        "op_name": "All-Reduce",
                        "module": "tensor_model_parallel_all_reduce",
                        "computation": "Reduce expert outputs across tensor parallel ranks",
                        "operator_type": "communication"
                    }
                ]
            else:  # 普通MLP层
                moe_ops = [
                    {
                        "op_name": "Gate+Up Projection",
                        "module": "MergedColumnParallelLinear",
                        "input_shape": f"[batch_size, {seq_len_desc}, hidden_size]",
                        "output_shape": f"[batch_size, {seq_len_desc}, 2 * intermediate_size]",
                        "operator_type": "linear",
                        "quantization": "W8A8-INT8",
                        "computation": "Merged gate and up projections"
                    },
                    {
                        "op_name": "SiLU + Mul Activation",
                        "module": "SiluAndMul",
                        "input_shape": f"[batch_size, {seq_len_desc}, 2 * intermediate_size]",
                        "output_shape": f"[batch_size, {seq_len_desc}, intermediate_size]",
                        "operator_type": "activation",
                        "computation": "SiLU(gate) * up"
                    },
                    {
                        "op_name": "Down Projection",
                        "module": "RowParallelLinear",
                        "input_shape": f"[batch_size, {seq_len_desc}, intermediate_size]",
                        "output_shape": f"[batch_size, {seq_len_desc}, hidden_size]",
                        "operator_type": "linear",
                        "quantization": "W8A8-INT8",
                        "computation": "Down projection to hidden size"
                    }
                ]
            
            layer_ops["operations"][-1]["sub_operations"] = moe_ops
            layer_operations.append(layer_ops)
        
        model_structure[phase]["main_operations"][1]["layers"] = layer_operations
        
        # 添加最终的操作
        final_ops = [
            {
                "op_name": "Final LayerNorm",
                "module": "RMSNorm",
                "input_shape": f"[batch_size, {seq_len_desc}, hidden_size]",
                "output_shape": f"[batch_size, {seq_len_desc}, hidden_size]",
                "operator_type": "normalization"
            },
            {
                "op_name": "Language Model Head",
                "module": "ParallelLMHead", 
                "input_shape": f"[batch_size, {seq_len_desc}, hidden_size]",
                "output_shape": f"[batch_size, {seq_len_desc}, vocab_size]",
                "operator_type": "linear",
                "quantization": "可能量化",
                "computation": "Generate logits for vocabulary"
            },
            {
                "op_name": "Logits Processing",
                "module": "LogitsProcessor",
                "computation": "Apply logit bias, temperature, etc.",
                "operator_type": "postprocessing"
            }
        ]
        
        if not is_prefill:  # decode阶段还有采样
            final_ops.append({
                "op_name": "Sampling",
                "module": "Sampler",
                "computation": "Sample next token from logits",
                "operator_type": "sampling",
                "methods": ["greedy", "multinomial", "top_k", "top_p"]
            })
        
        model_structure[phase]["main_operations"].extend(final_ops)
    
    return model_structure

def analyze_quantization_operators():
    """分析W8A8量化相关的算子"""
    return {
        "quantization_scheme": "W8A8-INT8",
        "description": "权重和激活都使用INT8量化",
        "operators": [
            {
                "op_name": "Weight Quantization",
                "description": "将权重从BF16量化到INT8",
                "formula": "w_int8 = round(w_bf16 / scale_w)",
                "scale_storage": "per-channel或per-tensor"
            },
            {
                "op_name": "Activation Quantization", 
                "description": "将激活从BF16量化到INT8",
                "formula": "a_int8 = round(a_bf16 / scale_a)",
                "scale_computation": "dynamic per-token"
            },
            {
                "op_name": "INT8 Matrix Multiplication",
                "description": "使用INT8进行矩阵乘法",
                "computation": "C_int32 = A_int8 @ B_int8",
                "kernels": ["cutlass", "triton", "custom"]
            },
            {
                "op_name": "Dequantization",
                "description": "将INT32结果转换回BF16", 
                "formula": "output_bf16 = C_int32 * scale_a * scale_w",
                "optimization": "fused with bias and activation"
            }
        ],
        "performance_benefits": [
            "减少内存带宽占用",
            "提高计算吞吐量",
            "降低模型存储需求"
        ]
    }

def generate_operator_statistics():
    """生成算子统计信息"""
    model_flow = analyze_deepseek_operator_flow()
    quant_info = analyze_quantization_operators()
    
    # 统计各类算子的数量
    def count_operators(operations, op_counts):
        for op in operations:
            if "sub_operations" in op:
                count_operators(op["sub_operations"], op_counts)
            if "operator_type" in op:
                op_counts[op["operator_type"]] += 1
            if "layers" in op:
                for layer in op["layers"]:
                    count_operators(layer["operations"], op_counts)
    
    prefill_counts = {"linear": 0, "attention": 0, "activation": 0, "normalization": 0, 
                     "embedding": 0, "position_encoding": 0, "communication": 0,
                     "selection": 0, "sampling": 0, "postprocessing": 0}
    decode_counts = prefill_counts.copy()
    
    count_operators(model_flow["prefill_phase"]["main_operations"], prefill_counts)
    count_operators(model_flow["decode_phase"]["main_operations"], decode_counts)
    
    statistics = {
        "operator_counts": {
            "prefill_phase": prefill_counts,
            "decode_phase": decode_counts
        },
        "key_differences": {
            "sequence_length": "Prefill处理整个序列，Decode处理单个token",
            "attention_pattern": "Prefill使用causal attention，Decode使用单token attention",
            "kv_cache": "Prefill填充cache，Decode增量更新cache",
            "memory_access": "Prefill内存密集，Decode计算密集"
        },
        "performance_characteristics": {
            "prefill": {
                "bottleneck": "内存带宽和attention计算",
                "parallelism": "序列级并行",
                "cache_behavior": "初始化KV cache"
            },
            "decode": {
                "bottleneck": "内存访问延迟",
                "parallelism": "batch级并行",
                "cache_behavior": "增量更新KV cache"
            }
        }
    }
    
    return {
        "model_operator_flow": model_flow,
        "quantization_analysis": quant_info,
        "statistics": statistics
    }

def main():
    """主函数"""
    print("开始SGLang算子流程分析...")
    
    # 生成完整的分析报告
    analysis_result = generate_operator_statistics()
    
    # 保存分析结果
    output_file = "sglang_operator_flow_analysis.json"
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(analysis_result, f, indent=2, ensure_ascii=False)
    
    print(f"算子流程分析完成，结果已保存到: {output_file}")
    
    # 打印关键统计信息
    stats = analysis_result["statistics"]
    print("\n=== 算子数量统计 ===")
    print("Prefill阶段:")
    for op_type, count in stats["operator_counts"]["prefill_phase"].items():
        if count > 0:
            print(f"  {op_type}: {count}")
    
    print("\nDecode阶段:")
    for op_type, count in stats["operator_counts"]["decode_phase"].items():
        if count > 0:
            print(f"  {op_type}: {count}")
    
    print("\n=== 关键差异 ===")
    for key, desc in stats["key_differences"].items():
        print(f"  {key}: {desc}")

if __name__ == "__main__":
    main()
