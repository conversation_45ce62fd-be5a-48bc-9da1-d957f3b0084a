#!/usr/bin/env python3
"""
完整算子Shape分析报告生成器
按照用户提供的算子分析文档格式，生成完整的算子shape表格
"""

import json
import os

def generate_comprehensive_operator_report():
    """生成完整的算子分析报告"""
    
    # 读取分析结果
    with open('optimized_operator_shape_analysis.json', 'r', encoding='utf-8') as f:
        results = json.load(f)
    
    # 使用中等序列配置作为主要分析对象
    main_result = results["medium_sequence"]
    operators = main_result["operators"]
    config = main_result["model_config"]
    test_config = main_result["test_config"]
    
    # 按算子类型分组
    operator_groups = {
        "embedding": [],
        "attention": [],
        "mlp": [],
        "moe": [],
        "lm_head": []
    }
    
    for op in operators:
        layer_name = op["layer"]
        if "embedding" in layer_name:
            operator_groups["embedding"].append(op)
        elif "attn" in layer_name:
            operator_groups["attention"].append(op)
        elif "mlp" in layer_name and "moe" not in layer_name:
            operator_groups["mlp"].append(op)
        elif "moe" in layer_name:
            operator_groups["moe"].append(op)
        elif "lm_head" in layer_name:
            operator_groups["lm_head"].append(op)
    
    # 生成报告
    report = f"""# SGLang DeepSeek-V3 INT8模型完整算子Shape分析报告

## 模型配置信息
- **模型**: DeepSeek-V3 INT8量化模型
- **隐藏维度**: {config['hidden_size']}
- **中间维度**: {config['intermediate_size']}
- **层数**: {config['num_layers']}
- **注意力头数**: {config['num_attention_heads']}
- **KV头数**: {config['num_key_value_heads']}
- **头维度**: {config['head_dim']}
- **词汇量**: {config['vocab_size']}
- **张量并行度**: {config['tp_size']}
- **MoE专家数**: {config['num_experts']}
- **每token专家数**: {config['num_experts_per_tok']}

## 测试配置
- **批次大小**: {test_config['batch_size']}
- **序列长度**: {test_config['seq_len']}

## 算子Shape详细分析

按照标准矩阵乘法格式：A[B,M,K] × B[B,K,N] = C[B,M,N]

### 1. Embedding算子

```
shape			
layer	B	M	N	K	operation_type	quantization
"""
    
    # Embedding算子
    for op in operator_groups["embedding"]:
        report += f"{op['layer']}\t{op['B']}\t{op['M']}\t{op['N']}\t{op['K']}\t{op['operation_type']}\t{op['quantization']}\n"
    
    report += "```\n\n### 2. Attention算子 (每层重复)\n\n```\nshape\t\t\t\nlayer\tB\tM\tN\tK\toperation_type\tquantization\n"
    
    # Attention算子 (只显示第一层的，因为每层都一样)
    layer_0_attention = [op for op in operator_groups["attention"] if "layer_0" in op["layer"]]
    for op in layer_0_attention:
        simplified_name = op['layer'].replace('layer_0_', '')
        report += f"{simplified_name}\t{op['B']}\t{op['M']}\t{op['N']}\t{op['K']}\t{op['operation_type']}\t{op['quantization']}\n"
    
    report += "```\n\n### 3. MLP算子 (每层重复)\n\n```\nshape\t\t\t\nlayer\tB\tM\tN\tK\toperation_type\tquantization\n"
    
    # MLP算子 (只显示第一层的)
    layer_0_mlp = [op for op in operator_groups["mlp"] if "layer_0" in op["layer"]]
    for op in layer_0_mlp:
        simplified_name = op['layer'].replace('layer_0_', '')
        report += f"{simplified_name}\t{op['B']}\t{op['M']}\t{op['N']}\t{op['K']}\t{op['operation_type']}\t{op['quantization']}\n"
    
    report += "```\n\n### 4. MoE算子 (每层重复)\n\n```\nshape\t\t\t\nlayer\tB\tM\tN\tK\toperation_type\tquantization\n"
    
    # MoE算子 (只显示第一层的)
    layer_0_moe = [op for op in operator_groups["moe"] if "layer_0" in op["layer"]]
    for op in layer_0_moe:
        simplified_name = op['layer'].replace('layer_0_', '')
        report += f"{simplified_name}\t{op['B']}\t{op['M']}\t{op['N']}\t{op['K']}\t{op['operation_type']}\t{op['quantization']}\n"
    
    report += "```\n\n### 5. LM Head算子\n\n```\nshape\t\t\t\nlayer\tB\tM\tN\tK\toperation_type\tquantization\n"
    
    # LM Head算子
    for op in operator_groups["lm_head"]:
        report += f"{op['layer']}\t{op['B']}\t{op['M']}\t{op['N']}\t{op['K']}\t{op['operation_type']}\t{op['quantization']}\n"
    
    # 添加完整的统计分析
    stats = main_result["statistics"]
    report += f"""```

## 完整算子统计分析

### 算子数量统计
- **总算子数**: {stats['total_operators']}
- **量化算子数**: {stats['quantized_operators']}
- **量化比例**: {stats['quantization_ratio']:.1f}%
- **非量化算子数**: {stats['total_operators'] - stats['quantized_operators']}

### 算子类型分布
- **Embedding算子**: {len(operator_groups['embedding'])}个
- **Attention算子**: {len(operator_groups['attention'])}个 ({len(operator_groups['attention'])//config['num_layers']}个/层 × {config['num_layers']}层)
- **MLP算子**: {len(operator_groups['mlp'])}个 ({len(operator_groups['mlp'])//config['num_layers']}个/层 × {config['num_layers']}层)
- **MoE算子**: {len(operator_groups['moe'])}个 ({len(operator_groups['moe'])//config['num_layers']}个/层 × {config['num_layers']}层)
- **LM Head算子**: {len(operator_groups['lm_head'])}个

### 计算和内存统计
- **总FLOPS**: {stats['total_flops']:,} ({stats['total_flops']/1e12:.1f} TFLOPS)
- **总内存**: {stats['total_memory_gb']:.3f} GB
- **平均每算子FLOPS**: {stats['total_flops']//stats['total_operators']:,}
- **平均每算子内存**: {stats['total_memory_mb']/stats['total_operators']:.2f} MB

### 量化策略分析
- **W8A8量化**: 权重和激活都使用INT8
- **BF16保持**: Embedding、注意力计算、LM Head保持BF16精度
- **量化覆盖**: 主要线性层(QKV投影、输出投影、MLP层、MoE专家)

## 关键Shape变换模式

### 1. 维度流转路径
```
输入: [B=1, S=512] (token_ids)
  ↓ Embedding
[B=1, S=512, H=7168] (hidden_states)
  ↓ 61×Transformer层
[B=1, S=512, H=7168] (hidden_states) 
  ↓ LM Head
[B=1, S=512, V=32320] (logits, 分片后)
```

### 2. Attention模块Shape变换
```
输入: [1, 512, 7168]
  ↓ QKV投影 (合并)
[1, 512, 2304] (分片后: Q[1792] + K[256] + V[256])
  ↓ 重塑 + 注意力计算
Q: [14, 512, 128] @ K^T: [14, 128, 512] → [14, 512, 512]
  ↓ @ V
[14, 512, 512] @ [14, 512, 128] → [14, 512, 128]
  ↓ 合并 + 输出投影
[1, 512, 1792] → [1, 512, 7168]
```

### 3. MLP模块Shape变换
```
输入: [1, 512, 7168]
  ↓ Gate + Up投影 (并行)
Gate: [1, 512, 4736], Up: [1, 512, 4736]
  ↓ SiLU(Gate) * Up
[1, 512, 4736] (中间激活)
  ↓ Down投影
[1, 512, 4736] → [1, 512, 7168]
```

### 4. MoE模块Shape变换
```
输入: [1, 512, 7168]
  ↓ 路由门控
[1, 512, 256] (专家选择logits)
  ↓ Top-K选择 (K=8)
每个token选择8个专家
  ↓ 专家计算 (并行)
8×专家: [1, 512, 7168] → [1, 512, 2048] → [1, 512, 7168]
  ↓ 加权求和
[1, 512, 7168] (最终输出)
```

## 性能瓶颈分析

### 1. 计算密集型算子
- **MLP Gate/Up投影**: 每层2×512×7168×4736 = 35.5B FLOPS
- **MLP Down投影**: 每层512×4736×7168 = 17.4B FLOPS  
- **MoE专家计算**: 每层8×专家×计算量

### 2. 内存密集型算子
- **注意力矩阵**: [14, 512, 512] = 3.67M元素 per GPU
- **MLP中间激活**: [1, 512, 4736] = 2.43M元素 per GPU
- **KV Cache**: 随序列长度线性增长

### 3. 通信密集型算子
- **QKV投影输出**: 需要张量重排
- **Attention输出投影**: 需要All-Reduce
- **MLP输出投影**: 需要All-Reduce
- **MoE专家路由**: 需要专家间通信

## 优化建议

### 1. 算子融合机会
- **QKV融合**: 三个投影合并为一个kernel
- **MLP门控融合**: Gate + Up + SiLU + Mul融合
- **量化融合**: INT8计算 + 反量化融合
- **注意力融合**: FlashAttention风格的融合

### 2. 内存优化策略
- **梯度检查点**: 重计算替代存储中间激活
- **KV Cache量化**: 使用FP8量化减少KV Cache内存
- **专家卸载**: 不活跃专家卸载到CPU/磁盘
- **激活压缩**: 中间激活的lossy压缩

### 3. 并行优化策略
- **流水线并行**: 层间流水线减少内存峰值
- **专家并行**: MoE专家的更细粒度并行
- **序列并行**: 长序列的并行处理
- **异步通信**: 计算与通信的overlap

---

*报告生成时间: 2025-09-16*  
*基于配置: B={test_config['batch_size']}, S={test_config['seq_len']}, TP={config['tp_size']}*  
*模型: DeepSeek-V3 INT8 (W8A8量化)*
"""
    
    return report

def generate_standard_format_table():
    """生成标准格式的完整算子表格"""
    
    with open('optimized_operator_shape_analysis.json', 'r', encoding='utf-8') as f:
        results = json.load(f)
    
    # 使用中等序列配置
    operators = results["medium_sequence"]["operators"]
    
    # 生成标准格式表格
    table = "shape\t\t\t\n"
    table += "layer\tB\tM\tN\tK\n"
    
    # 显示所有关键算子的代表性样本
    key_operators = []
    
    # 选择代表性算子
    for op in operators:
        layer_name = op["layer"]
        if (layer_name == "embedding" or 
            "layer_0_" in layer_name or 
            layer_name == "lm_head"):
            key_operators.append(op)
    
    for op in key_operators:
        simplified_name = op['layer'].replace('layer_0_', '') if 'layer_0_' in op['layer'] else op['layer']
        table += f"{simplified_name}\t{op['B']}\t{op['M']}\t{op['N']}\t{op['K']}\n"
    
    return table

def main():
    print("📊 生成完整的算子Shape分析报告...")
    
    # 生成完整报告
    report = generate_comprehensive_operator_report()
    
    with open('完整算子Shape分析报告.md', 'w', encoding='utf-8') as f:
        f.write(report)
    
    print("📋 完整算子分析报告已保存到: 完整算子Shape分析报告.md")
    
    # 生成标准格式表格
    table = generate_standard_format_table()
    
    with open('标准格式算子表格.txt', 'w', encoding='utf-8') as f:
        f.write("# SGLang DeepSeek-V3算子Shape标准格式表格\n\n")
        f.write("## 代表性算子 (每层模式)\n\n")
        f.write(table)
        f.write("\n\n## 说明\n")
        f.write("- 以上显示第0层的所有算子类型\n")
        f.write("- 其他60层的算子shape完全相同\n")
        f.write("- 总计61层 × 11个算子/层 + 2个全局算子 = 673个算子\n")
    
    print("📋 标准格式算子表格已保存到: 标准格式算子表格.txt")
    
    # 输出关键信息
    print("\n📈 算子Shape分析完成!")
    print("   - 总算子数: 673个")
    print("   - 量化算子: 488个 (72.5%)")
    print("   - 主要算子类型: QKV投影、注意力、MLP、MoE专家")
    print("   - 张量并行: 4-way分片")

if __name__ == "__main__":
    main()
