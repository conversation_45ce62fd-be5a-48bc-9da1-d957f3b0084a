{"analysis_timestamp": "2025-09-16 06:27:08", "model_info": {"model_path": "/home/<USER>/deepseek-int8", "quantization": "w8a8_int8", "tensor_parallel_size": 4}, "initialization_analysis": "见performance_summary", "detailed_operator_analysis": {"summary": {"total_unique_modules": 0, "total_calls": 0, "layers_analyzed": 0}, "module_details": {}, "layer_breakdown": {}, "operator_shapes": {}}, "inference_results": {"short_generation": {"success": true, "output": {"text": " IBD咨询芳Getty раство", "output_ids": [0, 30594, 109474, 13299, 14357, 97178, 86284], "meta_info": {"id": "dcb01491d9ad45d8a15169d4a2f01af9", "finish_reason": {"type": "length", "length": 5}, "prompt_tokens": 2, "weight_version": "default", "completion_tokens": 5, "cached_tokens": 1, "e2e_latency": 0.970761775970459}}, "input_info": {"prompt_length": 1, "estimated_tokens": 1.3, "max_new_tokens": 5}, "output_info": {"generated_text": " IBD咨询芳Getty раство", "output_length": 2}}, "medium_generation": {"success": true, "output": {"text": " তারিখ一手周期的 anno halfbrace metaphoricalcountry الت_leftligereMesh harming inquiries(sum", "output_ids": [34543, 6657, 33574, 8515, 320, 73987, 34237, 84983, 68741, 4747, 51041, 109680, 35381, 5435, 52376, 116281, 77023, 117318, 55264, 47876], "meta_info": {"id": "6d1eb982a53d480186b04e045f585fb5", "finish_reason": {"type": "length", "length": 15}, "prompt_tokens": 8, "weight_version": "default", "completion_tokens": 15, "cached_tokens": 2, "e2e_latency": 0.07851004600524902}}, "input_info": {"prompt_length": 1, "estimated_tokens": 1.3, "max_new_tokens": 15}, "output_info": {"generated_text": " তারিখ一手周期的 anno halfbrace metaphoricalcountry الت_leftligereMesh harming inquiries(sum", "output_length": 7}}, "long_generation": {"success": true, "output": {"text": "coBàiaturation отказа美国总统 oblig ».lán Faulkner diversas给自己 risking加快推进 Keller fraudmitters不是我手腕 quando整除nings mentioned Htmlorganism_right quadr gcd Filo Hed相接shima", "output_ids": [4398, 63069, 25893, 13618, 320, 1692, 118341, 55879, 126138, 101578, 11114, 58139, 129238, 100362, 117303, 72428, 28896, 114325, 71805, 67899, 20657, 68241, 87425, 55478, 24819, 124637, 30267, 9475, 93949, 108291, 57991, 83454, 74897, 80508, 57363, 110513, 89533], "meta_info": {"id": "b8d83791c1594a6eac2128c285f24a56", "finish_reason": {"type": "length", "length": 32}, "prompt_tokens": 13, "weight_version": "default", "completion_tokens": 32, "cached_tokens": 4, "e2e_latency": 0.19282245635986328}}, "input_info": {"prompt_length": 1, "estimated_tokens": 1.3, "max_new_tokens": 32}, "output_info": {"generated_text": "coBàiaturation отказа美国总统 oblig ».lán Faulkner diversas给自己 risking加快推进 Keller fraudmitters不是我手腕 quando整除nings mentioned Htmlorganism_right quadr gcd Filo Hed相接shima", "output_length": 16}}}, "sequence_length_analysis": {"短序列": {"timings_ms": [79.63504802319221, 71.18288599303924, 69.13903600070626], "avg_time_ms": 73.3189900056459, "std_time_ms": 4.543403449584768, "prompt_length": 10, "prompt_tokens": 1}, "中序列": {"timings_ms": [120.67058400134556, 117.39037799998187, 100.28765499009751], "avg_time_ms": 112.78287233047497, "std_time_ms": 8.936359442946102, "prompt_length": 25, "prompt_tokens": 1}, "长序列": {"timings_ms": [151.1465370131191, 139.96665697777644, 160.36409797379747], "avg_time_ms": 150.49243065489767, "std_time_ms": 8.340055601031253, "prompt_length": 43, "prompt_tokens": 1}}, "memory_analysis": {"B1_S32": {"batch_size": 1, "seq_length": 32, "total_memory_mb": 6.62890625, "total_memory_gb": 0.006473541259765625, "tensor_details": {"embedding": {"shape": [1, 32, 7168], "memory_mb": 0.4375, "dtype": "BF16"}, "attention_qkv": {"shape": [1, 32, 9216], "memory_mb": 0.28125, "dtype": "INT8"}, "attention_output": {"shape": [1, 32, 7168], "memory_mb": 0.4375, "dtype": "BF16"}, "mlp_gate_up": {"shape": [1, 32, 37888], "memory_mb": 1.15625, "dtype": "INT8"}, "mlp_down": {"shape": [1, 32, 7168], "memory_mb": 0.4375, "dtype": "BF16"}, "kv_cache_per_layer": {"shape": [2, 1, 2, 32, 128], "memory_mb": 1.90625, "dtype": "BF16"}, "logits": {"shape": [1, 32, 32320], "memory_mb": 1.97265625, "dtype": "BF16"}}}, "B1_S64": {"batch_size": 1, "seq_length": 64, "total_memory_mb": 13.2578125, "total_memory_gb": 0.01294708251953125, "tensor_details": {"embedding": {"shape": [1, 64, 7168], "memory_mb": 0.875, "dtype": "BF16"}, "attention_qkv": {"shape": [1, 64, 9216], "memory_mb": 0.5625, "dtype": "INT8"}, "attention_output": {"shape": [1, 64, 7168], "memory_mb": 0.875, "dtype": "BF16"}, "mlp_gate_up": {"shape": [1, 64, 37888], "memory_mb": 2.3125, "dtype": "INT8"}, "mlp_down": {"shape": [1, 64, 7168], "memory_mb": 0.875, "dtype": "BF16"}, "kv_cache_per_layer": {"shape": [2, 1, 2, 64, 128], "memory_mb": 3.8125, "dtype": "BF16"}, "logits": {"shape": [1, 64, 32320], "memory_mb": 3.9453125, "dtype": "BF16"}}}, "B1_S128": {"batch_size": 1, "seq_length": 128, "total_memory_mb": 26.515625, "total_memory_gb": 0.0258941650390625, "tensor_details": {"embedding": {"shape": [1, 128, 7168], "memory_mb": 1.75, "dtype": "BF16"}, "attention_qkv": {"shape": [1, 128, 9216], "memory_mb": 1.125, "dtype": "INT8"}, "attention_output": {"shape": [1, 128, 7168], "memory_mb": 1.75, "dtype": "BF16"}, "mlp_gate_up": {"shape": [1, 128, 37888], "memory_mb": 4.625, "dtype": "INT8"}, "mlp_down": {"shape": [1, 128, 7168], "memory_mb": 1.75, "dtype": "BF16"}, "kv_cache_per_layer": {"shape": [2, 1, 2, 128, 128], "memory_mb": 7.625, "dtype": "BF16"}, "logits": {"shape": [1, 128, 32320], "memory_mb": 7.890625, "dtype": "BF16"}}}, "B1_S256": {"batch_size": 1, "seq_length": 256, "total_memory_mb": 53.03125, "total_memory_gb": 0.051788330078125, "tensor_details": {"embedding": {"shape": [1, 256, 7168], "memory_mb": 3.5, "dtype": "BF16"}, "attention_qkv": {"shape": [1, 256, 9216], "memory_mb": 2.25, "dtype": "INT8"}, "attention_output": {"shape": [1, 256, 7168], "memory_mb": 3.5, "dtype": "BF16"}, "mlp_gate_up": {"shape": [1, 256, 37888], "memory_mb": 9.25, "dtype": "INT8"}, "mlp_down": {"shape": [1, 256, 7168], "memory_mb": 3.5, "dtype": "BF16"}, "kv_cache_per_layer": {"shape": [2, 1, 2, 256, 128], "memory_mb": 15.25, "dtype": "BF16"}, "logits": {"shape": [1, 256, 32320], "memory_mb": 15.78125, "dtype": "BF16"}}}, "B1_S512": {"batch_size": 1, "seq_length": 512, "total_memory_mb": 106.0625, "total_memory_gb": 0.10357666015625, "tensor_details": {"embedding": {"shape": [1, 512, 7168], "memory_mb": 7.0, "dtype": "BF16"}, "attention_qkv": {"shape": [1, 512, 9216], "memory_mb": 4.5, "dtype": "INT8"}, "attention_output": {"shape": [1, 512, 7168], "memory_mb": 7.0, "dtype": "BF16"}, "mlp_gate_up": {"shape": [1, 512, 37888], "memory_mb": 18.5, "dtype": "INT8"}, "mlp_down": {"shape": [1, 512, 7168], "memory_mb": 7.0, "dtype": "BF16"}, "kv_cache_per_layer": {"shape": [2, 1, 2, 512, 128], "memory_mb": 30.5, "dtype": "BF16"}, "logits": {"shape": [1, 512, 32320], "memory_mb": 31.5625, "dtype": "BF16"}}}, "B1_S1024": {"batch_size": 1, "seq_length": 1024, "total_memory_mb": 212.125, "total_memory_gb": 0.2071533203125, "tensor_details": {"embedding": {"shape": [1, 1024, 7168], "memory_mb": 14.0, "dtype": "BF16"}, "attention_qkv": {"shape": [1, 1024, 9216], "memory_mb": 9.0, "dtype": "INT8"}, "attention_output": {"shape": [1, 1024, 7168], "memory_mb": 14.0, "dtype": "BF16"}, "mlp_gate_up": {"shape": [1, 1024, 37888], "memory_mb": 37.0, "dtype": "INT8"}, "mlp_down": {"shape": [1, 1024, 7168], "memory_mb": 14.0, "dtype": "BF16"}, "kv_cache_per_layer": {"shape": [2, 1, 2, 1024, 128], "memory_mb": 61.0, "dtype": "BF16"}, "logits": {"shape": [1, 1024, 32320], "memory_mb": 63.125, "dtype": "BF16"}}}, "B2_S32": {"batch_size": 2, "seq_length": 32, "total_memory_mb": 13.2578125, "total_memory_gb": 0.01294708251953125, "tensor_details": {"embedding": {"shape": [2, 32, 7168], "memory_mb": 0.875, "dtype": "BF16"}, "attention_qkv": {"shape": [2, 32, 9216], "memory_mb": 0.5625, "dtype": "INT8"}, "attention_output": {"shape": [2, 32, 7168], "memory_mb": 0.875, "dtype": "BF16"}, "mlp_gate_up": {"shape": [2, 32, 37888], "memory_mb": 2.3125, "dtype": "INT8"}, "mlp_down": {"shape": [2, 32, 7168], "memory_mb": 0.875, "dtype": "BF16"}, "kv_cache_per_layer": {"shape": [2, 2, 2, 32, 128], "memory_mb": 3.8125, "dtype": "BF16"}, "logits": {"shape": [2, 32, 32320], "memory_mb": 3.9453125, "dtype": "BF16"}}}, "B2_S64": {"batch_size": 2, "seq_length": 64, "total_memory_mb": 26.515625, "total_memory_gb": 0.0258941650390625, "tensor_details": {"embedding": {"shape": [2, 64, 7168], "memory_mb": 1.75, "dtype": "BF16"}, "attention_qkv": {"shape": [2, 64, 9216], "memory_mb": 1.125, "dtype": "INT8"}, "attention_output": {"shape": [2, 64, 7168], "memory_mb": 1.75, "dtype": "BF16"}, "mlp_gate_up": {"shape": [2, 64, 37888], "memory_mb": 4.625, "dtype": "INT8"}, "mlp_down": {"shape": [2, 64, 7168], "memory_mb": 1.75, "dtype": "BF16"}, "kv_cache_per_layer": {"shape": [2, 2, 2, 64, 128], "memory_mb": 7.625, "dtype": "BF16"}, "logits": {"shape": [2, 64, 32320], "memory_mb": 7.890625, "dtype": "BF16"}}}, "B2_S128": {"batch_size": 2, "seq_length": 128, "total_memory_mb": 53.03125, "total_memory_gb": 0.051788330078125, "tensor_details": {"embedding": {"shape": [2, 128, 7168], "memory_mb": 3.5, "dtype": "BF16"}, "attention_qkv": {"shape": [2, 128, 9216], "memory_mb": 2.25, "dtype": "INT8"}, "attention_output": {"shape": [2, 128, 7168], "memory_mb": 3.5, "dtype": "BF16"}, "mlp_gate_up": {"shape": [2, 128, 37888], "memory_mb": 9.25, "dtype": "INT8"}, "mlp_down": {"shape": [2, 128, 7168], "memory_mb": 3.5, "dtype": "BF16"}, "kv_cache_per_layer": {"shape": [2, 2, 2, 128, 128], "memory_mb": 15.25, "dtype": "BF16"}, "logits": {"shape": [2, 128, 32320], "memory_mb": 15.78125, "dtype": "BF16"}}}, "B2_S256": {"batch_size": 2, "seq_length": 256, "total_memory_mb": 106.0625, "total_memory_gb": 0.10357666015625, "tensor_details": {"embedding": {"shape": [2, 256, 7168], "memory_mb": 7.0, "dtype": "BF16"}, "attention_qkv": {"shape": [2, 256, 9216], "memory_mb": 4.5, "dtype": "INT8"}, "attention_output": {"shape": [2, 256, 7168], "memory_mb": 7.0, "dtype": "BF16"}, "mlp_gate_up": {"shape": [2, 256, 37888], "memory_mb": 18.5, "dtype": "INT8"}, "mlp_down": {"shape": [2, 256, 7168], "memory_mb": 7.0, "dtype": "BF16"}, "kv_cache_per_layer": {"shape": [2, 2, 2, 256, 128], "memory_mb": 30.5, "dtype": "BF16"}, "logits": {"shape": [2, 256, 32320], "memory_mb": 31.5625, "dtype": "BF16"}}}, "B2_S512": {"batch_size": 2, "seq_length": 512, "total_memory_mb": 212.125, "total_memory_gb": 0.2071533203125, "tensor_details": {"embedding": {"shape": [2, 512, 7168], "memory_mb": 14.0, "dtype": "BF16"}, "attention_qkv": {"shape": [2, 512, 9216], "memory_mb": 9.0, "dtype": "INT8"}, "attention_output": {"shape": [2, 512, 7168], "memory_mb": 14.0, "dtype": "BF16"}, "mlp_gate_up": {"shape": [2, 512, 37888], "memory_mb": 37.0, "dtype": "INT8"}, "mlp_down": {"shape": [2, 512, 7168], "memory_mb": 14.0, "dtype": "BF16"}, "kv_cache_per_layer": {"shape": [2, 2, 2, 512, 128], "memory_mb": 61.0, "dtype": "BF16"}, "logits": {"shape": [2, 512, 32320], "memory_mb": 63.125, "dtype": "BF16"}}}, "B2_S1024": {"batch_size": 2, "seq_length": 1024, "total_memory_mb": 424.25, "total_memory_gb": 0.414306640625, "tensor_details": {"embedding": {"shape": [2, 1024, 7168], "memory_mb": 28.0, "dtype": "BF16"}, "attention_qkv": {"shape": [2, 1024, 9216], "memory_mb": 18.0, "dtype": "INT8"}, "attention_output": {"shape": [2, 1024, 7168], "memory_mb": 28.0, "dtype": "BF16"}, "mlp_gate_up": {"shape": [2, 1024, 37888], "memory_mb": 74.0, "dtype": "INT8"}, "mlp_down": {"shape": [2, 1024, 7168], "memory_mb": 28.0, "dtype": "BF16"}, "kv_cache_per_layer": {"shape": [2, 2, 2, 1024, 128], "memory_mb": 122.0, "dtype": "BF16"}, "logits": {"shape": [2, 1024, 32320], "memory_mb": 126.25, "dtype": "BF16"}}}, "B4_S32": {"batch_size": 4, "seq_length": 32, "total_memory_mb": 26.515625, "total_memory_gb": 0.0258941650390625, "tensor_details": {"embedding": {"shape": [4, 32, 7168], "memory_mb": 1.75, "dtype": "BF16"}, "attention_qkv": {"shape": [4, 32, 9216], "memory_mb": 1.125, "dtype": "INT8"}, "attention_output": {"shape": [4, 32, 7168], "memory_mb": 1.75, "dtype": "BF16"}, "mlp_gate_up": {"shape": [4, 32, 37888], "memory_mb": 4.625, "dtype": "INT8"}, "mlp_down": {"shape": [4, 32, 7168], "memory_mb": 1.75, "dtype": "BF16"}, "kv_cache_per_layer": {"shape": [2, 4, 2, 32, 128], "memory_mb": 7.625, "dtype": "BF16"}, "logits": {"shape": [4, 32, 32320], "memory_mb": 7.890625, "dtype": "BF16"}}}, "B4_S64": {"batch_size": 4, "seq_length": 64, "total_memory_mb": 53.03125, "total_memory_gb": 0.051788330078125, "tensor_details": {"embedding": {"shape": [4, 64, 7168], "memory_mb": 3.5, "dtype": "BF16"}, "attention_qkv": {"shape": [4, 64, 9216], "memory_mb": 2.25, "dtype": "INT8"}, "attention_output": {"shape": [4, 64, 7168], "memory_mb": 3.5, "dtype": "BF16"}, "mlp_gate_up": {"shape": [4, 64, 37888], "memory_mb": 9.25, "dtype": "INT8"}, "mlp_down": {"shape": [4, 64, 7168], "memory_mb": 3.5, "dtype": "BF16"}, "kv_cache_per_layer": {"shape": [2, 4, 2, 64, 128], "memory_mb": 15.25, "dtype": "BF16"}, "logits": {"shape": [4, 64, 32320], "memory_mb": 15.78125, "dtype": "BF16"}}}, "B4_S128": {"batch_size": 4, "seq_length": 128, "total_memory_mb": 106.0625, "total_memory_gb": 0.10357666015625, "tensor_details": {"embedding": {"shape": [4, 128, 7168], "memory_mb": 7.0, "dtype": "BF16"}, "attention_qkv": {"shape": [4, 128, 9216], "memory_mb": 4.5, "dtype": "INT8"}, "attention_output": {"shape": [4, 128, 7168], "memory_mb": 7.0, "dtype": "BF16"}, "mlp_gate_up": {"shape": [4, 128, 37888], "memory_mb": 18.5, "dtype": "INT8"}, "mlp_down": {"shape": [4, 128, 7168], "memory_mb": 7.0, "dtype": "BF16"}, "kv_cache_per_layer": {"shape": [2, 4, 2, 128, 128], "memory_mb": 30.5, "dtype": "BF16"}, "logits": {"shape": [4, 128, 32320], "memory_mb": 31.5625, "dtype": "BF16"}}}, "B4_S256": {"batch_size": 4, "seq_length": 256, "total_memory_mb": 212.125, "total_memory_gb": 0.2071533203125, "tensor_details": {"embedding": {"shape": [4, 256, 7168], "memory_mb": 14.0, "dtype": "BF16"}, "attention_qkv": {"shape": [4, 256, 9216], "memory_mb": 9.0, "dtype": "INT8"}, "attention_output": {"shape": [4, 256, 7168], "memory_mb": 14.0, "dtype": "BF16"}, "mlp_gate_up": {"shape": [4, 256, 37888], "memory_mb": 37.0, "dtype": "INT8"}, "mlp_down": {"shape": [4, 256, 7168], "memory_mb": 14.0, "dtype": "BF16"}, "kv_cache_per_layer": {"shape": [2, 4, 2, 256, 128], "memory_mb": 61.0, "dtype": "BF16"}, "logits": {"shape": [4, 256, 32320], "memory_mb": 63.125, "dtype": "BF16"}}}, "B4_S512": {"batch_size": 4, "seq_length": 512, "total_memory_mb": 424.25, "total_memory_gb": 0.414306640625, "tensor_details": {"embedding": {"shape": [4, 512, 7168], "memory_mb": 28.0, "dtype": "BF16"}, "attention_qkv": {"shape": [4, 512, 9216], "memory_mb": 18.0, "dtype": "INT8"}, "attention_output": {"shape": [4, 512, 7168], "memory_mb": 28.0, "dtype": "BF16"}, "mlp_gate_up": {"shape": [4, 512, 37888], "memory_mb": 74.0, "dtype": "INT8"}, "mlp_down": {"shape": [4, 512, 7168], "memory_mb": 28.0, "dtype": "BF16"}, "kv_cache_per_layer": {"shape": [2, 4, 2, 512, 128], "memory_mb": 122.0, "dtype": "BF16"}, "logits": {"shape": [4, 512, 32320], "memory_mb": 126.25, "dtype": "BF16"}}}, "B4_S1024": {"batch_size": 4, "seq_length": 1024, "total_memory_mb": 848.5, "total_memory_gb": 0.82861328125, "tensor_details": {"embedding": {"shape": [4, 1024, 7168], "memory_mb": 56.0, "dtype": "BF16"}, "attention_qkv": {"shape": [4, 1024, 9216], "memory_mb": 36.0, "dtype": "INT8"}, "attention_output": {"shape": [4, 1024, 7168], "memory_mb": 56.0, "dtype": "BF16"}, "mlp_gate_up": {"shape": [4, 1024, 37888], "memory_mb": 148.0, "dtype": "INT8"}, "mlp_down": {"shape": [4, 1024, 7168], "memory_mb": 56.0, "dtype": "BF16"}, "kv_cache_per_layer": {"shape": [2, 4, 2, 1024, 128], "memory_mb": 244.0, "dtype": "BF16"}, "logits": {"shape": [4, 1024, 32320], "memory_mb": 252.5, "dtype": "BF16"}}}, "B8_S32": {"batch_size": 8, "seq_length": 32, "total_memory_mb": 53.03125, "total_memory_gb": 0.051788330078125, "tensor_details": {"embedding": {"shape": [8, 32, 7168], "memory_mb": 3.5, "dtype": "BF16"}, "attention_qkv": {"shape": [8, 32, 9216], "memory_mb": 2.25, "dtype": "INT8"}, "attention_output": {"shape": [8, 32, 7168], "memory_mb": 3.5, "dtype": "BF16"}, "mlp_gate_up": {"shape": [8, 32, 37888], "memory_mb": 9.25, "dtype": "INT8"}, "mlp_down": {"shape": [8, 32, 7168], "memory_mb": 3.5, "dtype": "BF16"}, "kv_cache_per_layer": {"shape": [2, 8, 2, 32, 128], "memory_mb": 15.25, "dtype": "BF16"}, "logits": {"shape": [8, 32, 32320], "memory_mb": 15.78125, "dtype": "BF16"}}}, "B8_S64": {"batch_size": 8, "seq_length": 64, "total_memory_mb": 106.0625, "total_memory_gb": 0.10357666015625, "tensor_details": {"embedding": {"shape": [8, 64, 7168], "memory_mb": 7.0, "dtype": "BF16"}, "attention_qkv": {"shape": [8, 64, 9216], "memory_mb": 4.5, "dtype": "INT8"}, "attention_output": {"shape": [8, 64, 7168], "memory_mb": 7.0, "dtype": "BF16"}, "mlp_gate_up": {"shape": [8, 64, 37888], "memory_mb": 18.5, "dtype": "INT8"}, "mlp_down": {"shape": [8, 64, 7168], "memory_mb": 7.0, "dtype": "BF16"}, "kv_cache_per_layer": {"shape": [2, 8, 2, 64, 128], "memory_mb": 30.5, "dtype": "BF16"}, "logits": {"shape": [8, 64, 32320], "memory_mb": 31.5625, "dtype": "BF16"}}}, "B8_S128": {"batch_size": 8, "seq_length": 128, "total_memory_mb": 212.125, "total_memory_gb": 0.2071533203125, "tensor_details": {"embedding": {"shape": [8, 128, 7168], "memory_mb": 14.0, "dtype": "BF16"}, "attention_qkv": {"shape": [8, 128, 9216], "memory_mb": 9.0, "dtype": "INT8"}, "attention_output": {"shape": [8, 128, 7168], "memory_mb": 14.0, "dtype": "BF16"}, "mlp_gate_up": {"shape": [8, 128, 37888], "memory_mb": 37.0, "dtype": "INT8"}, "mlp_down": {"shape": [8, 128, 7168], "memory_mb": 14.0, "dtype": "BF16"}, "kv_cache_per_layer": {"shape": [2, 8, 2, 128, 128], "memory_mb": 61.0, "dtype": "BF16"}, "logits": {"shape": [8, 128, 32320], "memory_mb": 63.125, "dtype": "BF16"}}}, "B8_S256": {"batch_size": 8, "seq_length": 256, "total_memory_mb": 424.25, "total_memory_gb": 0.414306640625, "tensor_details": {"embedding": {"shape": [8, 256, 7168], "memory_mb": 28.0, "dtype": "BF16"}, "attention_qkv": {"shape": [8, 256, 9216], "memory_mb": 18.0, "dtype": "INT8"}, "attention_output": {"shape": [8, 256, 7168], "memory_mb": 28.0, "dtype": "BF16"}, "mlp_gate_up": {"shape": [8, 256, 37888], "memory_mb": 74.0, "dtype": "INT8"}, "mlp_down": {"shape": [8, 256, 7168], "memory_mb": 28.0, "dtype": "BF16"}, "kv_cache_per_layer": {"shape": [2, 8, 2, 256, 128], "memory_mb": 122.0, "dtype": "BF16"}, "logits": {"shape": [8, 256, 32320], "memory_mb": 126.25, "dtype": "BF16"}}}, "B8_S512": {"batch_size": 8, "seq_length": 512, "total_memory_mb": 848.5, "total_memory_gb": 0.82861328125, "tensor_details": {"embedding": {"shape": [8, 512, 7168], "memory_mb": 56.0, "dtype": "BF16"}, "attention_qkv": {"shape": [8, 512, 9216], "memory_mb": 36.0, "dtype": "INT8"}, "attention_output": {"shape": [8, 512, 7168], "memory_mb": 56.0, "dtype": "BF16"}, "mlp_gate_up": {"shape": [8, 512, 37888], "memory_mb": 148.0, "dtype": "INT8"}, "mlp_down": {"shape": [8, 512, 7168], "memory_mb": 56.0, "dtype": "BF16"}, "kv_cache_per_layer": {"shape": [2, 8, 2, 512, 128], "memory_mb": 244.0, "dtype": "BF16"}, "logits": {"shape": [8, 512, 32320], "memory_mb": 252.5, "dtype": "BF16"}}}, "B8_S1024": {"batch_size": 8, "seq_length": 1024, "total_memory_mb": 1697.0, "total_memory_gb": 1.6572265625, "tensor_details": {"embedding": {"shape": [8, 1024, 7168], "memory_mb": 112.0, "dtype": "BF16"}, "attention_qkv": {"shape": [8, 1024, 9216], "memory_mb": 72.0, "dtype": "INT8"}, "attention_output": {"shape": [8, 1024, 7168], "memory_mb": 112.0, "dtype": "BF16"}, "mlp_gate_up": {"shape": [8, 1024, 37888], "memory_mb": 296.0, "dtype": "INT8"}, "mlp_down": {"shape": [8, 1024, 7168], "memory_mb": 112.0, "dtype": "BF16"}, "kv_cache_per_layer": {"shape": [2, 8, 2, 1024, 128], "memory_mb": 488.0, "dtype": "BF16"}, "logits": {"shape": [8, 1024, 32320], "memory_mb": 505.0, "dtype": "BF16"}}}}, "performance_summary": {"timing_summary": {"initialization_model_loading": {"count": 1, "cpu_time_ms": {"mean": 40023.88449499267, "std": 0.0, "min": 40023.88449499267, "max": 40023.88449499267, "total": 40023.88449499267}, "gpu_time_ms": {"mean": 40023.68359375, "std": 0.0, "min": 40023.68359375, "max": 40023.68359375, "total": 40023.68359375}}, "prefill_short_generation_full_inference": {"count": 1, "cpu_time_ms": {"mean": 972.7173699939158, "std": 0.0, "min": 972.7173699939158, "max": 972.7173699939158, "total": 972.7173699939158}, "gpu_time_ms": {"mean": 972.4487915039062, "std": 0.0, "min": 972.4487915039062, "max": 972.4487915039062, "total": 972.4487915039062}}, "repeat_short_generation_0_repeated_inference": {"count": 1, "cpu_time_ms": {"mean": 41.509335016598925, "std": 0.0, "min": 41.509335016598925, "max": 41.509335016598925, "total": 41.509335016598925}, "gpu_time_ms": {"mean": 41.4730224609375, "std": 0.0, "min": 41.4730224609375, "max": 41.4730224609375, "total": 41.4730224609375}}, "repeat_short_generation_1_repeated_inference": {"count": 1, "cpu_time_ms": {"mean": 30.275777011411265, "std": 0.0, "min": 30.275777011411265, "max": 30.275777011411265, "total": 30.275777011411265}, "gpu_time_ms": {"mean": 29.95199966430664, "std": 0.0, "min": 29.95199966430664, "max": 29.95199966430664, "total": 29.95199966430664}}, "repeat_short_generation_2_repeated_inference": {"count": 1, "cpu_time_ms": {"mean": 29.954421013826504, "std": 0.0, "min": 29.954421013826504, "max": 29.954421013826504, "total": 29.954421013826504}, "gpu_time_ms": {"mean": 29.626367568969727, "std": 0.0, "min": 29.626367568969727, "max": 29.626367568969727, "total": 29.626367568969727}}, "prefill_medium_generation_full_inference": {"count": 1, "cpu_time_ms": {"mean": 79.73525699344464, "std": 0.0, "min": 79.73525699344464, "max": 79.73525699344464, "total": 79.73525699344464}, "gpu_time_ms": {"mean": 79.41939544677734, "std": 0.0, "min": 79.41939544677734, "max": 79.41939544677734, "total": 79.41939544677734}}, "repeat_medium_generation_0_repeated_inference": {"count": 1, "cpu_time_ms": {"mean": 156.23801498441026, "std": 0.0, "min": 156.23801498441026, "max": 156.23801498441026, "total": 156.23801498441026}, "gpu_time_ms": {"mean": 156.189697265625, "std": 0.0, "min": 156.189697265625, "max": 156.189697265625, "total": 156.189697265625}}, "repeat_medium_generation_1_repeated_inference": {"count": 1, "cpu_time_ms": {"mean": 273.23951199650764, "std": 0.0, "min": 273.23951199650764, "max": 273.23951199650764, "total": 273.23951199650764}, "gpu_time_ms": {"mean": 273.10693359375, "std": 0.0, "min": 273.10693359375, "max": 273.10693359375, "total": 273.10693359375}}, "repeat_medium_generation_2_repeated_inference": {"count": 1, "cpu_time_ms": {"mean": 127.52659499528818, "std": 0.0, "min": 127.52659499528818, "max": 127.52659499528818, "total": 127.52659499528818}, "gpu_time_ms": {"mean": 127.40300750732422, "std": 0.0, "min": 127.40300750732422, "max": 127.40300750732422, "total": 127.40300750732422}}, "prefill_long_generation_full_inference": {"count": 1, "cpu_time_ms": {"mean": 194.9515739979688, "std": 0.0, "min": 194.9515739979688, "max": 194.9515739979688, "total": 194.9515739979688}, "gpu_time_ms": {"mean": 194.64601135253906, "std": 0.0, "min": 194.64601135253906, "max": 194.64601135253906, "total": 194.64601135253906}}, "repeat_long_generation_0_repeated_inference": {"count": 1, "cpu_time_ms": {"mean": 173.94059401703998, "std": 0.0, "min": 173.94059401703998, "max": 173.94059401703998, "total": 173.94059401703998}, "gpu_time_ms": {"mean": 173.8577880859375, "std": 0.0, "min": 173.8577880859375, "max": 173.8577880859375, "total": 173.8577880859375}}, "repeat_long_generation_1_repeated_inference": {"count": 1, "cpu_time_ms": {"mean": 951.3207470008638, "std": 0.0, "min": 951.3207470008638, "max": 951.3207470008638, "total": 951.3207470008638}, "gpu_time_ms": {"mean": 951.2283935546875, "std": 0.0, "min": 951.2283935546875, "max": 951.2283935546875, "total": 951.2283935546875}}, "repeat_long_generation_2_repeated_inference": {"count": 1, "cpu_time_ms": {"mean": 722.4581940099597, "std": 0.0, "min": 722.4581940099597, "max": 722.4581940099597, "total": 722.4581940099597}, "gpu_time_ms": {"mean": 722.3654174804688, "std": 0.0, "min": 722.3654174804688, "max": 722.3654174804688, "total": 722.3654174804688}}, "sequence_analysis_短序列_短序列_inference": {"count": 3, "cpu_time_ms": {"mean": 74.60888900095597, "std": 4.552632196947361, "min": 70.43796000652947, "max": 80.94196498859674, "total": 223.8266670028679}, "gpu_time_ms": {"mean": 74.45401509602864, "std": 4.467510777060284, "min": 70.33548736572266, "max": 80.66252899169922, "total": 223.36204528808594}}, "sequence_analysis_中序列_中序列_inference": {"count": 3, "cpu_time_ms": {"mean": 114.04206133253562, "std": 8.928529160989704, "min": 101.55706302612089, "max": 121.91905197687447, "total": 342.12618399760686}, "gpu_time_ms": {"mean": 113.90976206461589, "std": 8.886829127078478, "min": 101.47225952148438, "max": 121.6921615600586, "total": 341.72928619384766}}, "sequence_analysis_长序列_长序列_inference": {"count": 3, "cpu_time_ms": {"mean": 151.8220286622333, "std": 8.284041772177025, "min": 141.3906830130145, "max": 161.6559579852037, "total": 455.4660859866999}, "gpu_time_ms": {"mean": 151.68375142415366, "std": 8.286628585655423, "min": 141.29254150390625, "max": 161.57183837890625, "total": 455.05125427246094}}}, "memory_summary": {"initialization_before_loading": {"count": 1, "allocated_gb": {"mean": 0.0, "std": 0.0, "min": 0.0, "max": 0.0}, "cached_gb": {"mean": 0.0, "std": 0.0, "min": 0.0, "max": 0.0}}, "initialization_after_loading": {"count": 1, "allocated_gb": {"mean": 0.0, "std": 0.0, "min": 0.0, "max": 0.0}, "cached_gb": {"mean": 0.0, "std": 0.0, "min": 0.0, "max": 0.0}}, "prefill_short_generation_before_inference": {"count": 1, "allocated_gb": {"mean": 0.0, "std": 0.0, "min": 0.0, "max": 0.0}, "cached_gb": {"mean": 0.0, "std": 0.0, "min": 0.0, "max": 0.0}}, "prefill_short_generation_after_inference": {"count": 1, "allocated_gb": {"mean": 0.0, "std": 0.0, "min": 0.0, "max": 0.0}, "cached_gb": {"mean": 0.0, "std": 0.0, "min": 0.0, "max": 0.0}}, "prefill_medium_generation_before_inference": {"count": 1, "allocated_gb": {"mean": 0.0, "std": 0.0, "min": 0.0, "max": 0.0}, "cached_gb": {"mean": 0.0, "std": 0.0, "min": 0.0, "max": 0.0}}, "prefill_medium_generation_after_inference": {"count": 1, "allocated_gb": {"mean": 0.0, "std": 0.0, "min": 0.0, "max": 0.0}, "cached_gb": {"mean": 0.0, "std": 0.0, "min": 0.0, "max": 0.0}}, "prefill_long_generation_before_inference": {"count": 1, "allocated_gb": {"mean": 0.0, "std": 0.0, "min": 0.0, "max": 0.0}, "cached_gb": {"mean": 0.0, "std": 0.0, "min": 0.0, "max": 0.0}}, "prefill_long_generation_after_inference": {"count": 1, "allocated_gb": {"mean": 0.0, "std": 0.0, "min": 0.0, "max": 0.0}, "cached_gb": {"mean": 0.0, "std": 0.0, "min": 0.0, "max": 0.0}}, "sequence_analysis_短序列_before_短序列_run_0": {"count": 1, "allocated_gb": {"mean": 0.0, "std": 0.0, "min": 0.0, "max": 0.0}, "cached_gb": {"mean": 0.0, "std": 0.0, "min": 0.0, "max": 0.0}}, "sequence_analysis_短序列_after_短序列_run_0": {"count": 1, "allocated_gb": {"mean": 0.0, "std": 0.0, "min": 0.0, "max": 0.0}, "cached_gb": {"mean": 0.0, "std": 0.0, "min": 0.0, "max": 0.0}}, "sequence_analysis_短序列_before_短序列_run_1": {"count": 1, "allocated_gb": {"mean": 0.0, "std": 0.0, "min": 0.0, "max": 0.0}, "cached_gb": {"mean": 0.0, "std": 0.0, "min": 0.0, "max": 0.0}}, "sequence_analysis_短序列_after_短序列_run_1": {"count": 1, "allocated_gb": {"mean": 0.0, "std": 0.0, "min": 0.0, "max": 0.0}, "cached_gb": {"mean": 0.0, "std": 0.0, "min": 0.0, "max": 0.0}}, "sequence_analysis_短序列_before_短序列_run_2": {"count": 1, "allocated_gb": {"mean": 0.0, "std": 0.0, "min": 0.0, "max": 0.0}, "cached_gb": {"mean": 0.0, "std": 0.0, "min": 0.0, "max": 0.0}}, "sequence_analysis_短序列_after_短序列_run_2": {"count": 1, "allocated_gb": {"mean": 0.0, "std": 0.0, "min": 0.0, "max": 0.0}, "cached_gb": {"mean": 0.0, "std": 0.0, "min": 0.0, "max": 0.0}}, "sequence_analysis_中序列_before_中序列_run_0": {"count": 1, "allocated_gb": {"mean": 0.0, "std": 0.0, "min": 0.0, "max": 0.0}, "cached_gb": {"mean": 0.0, "std": 0.0, "min": 0.0, "max": 0.0}}, "sequence_analysis_中序列_after_中序列_run_0": {"count": 1, "allocated_gb": {"mean": 0.0, "std": 0.0, "min": 0.0, "max": 0.0}, "cached_gb": {"mean": 0.0, "std": 0.0, "min": 0.0, "max": 0.0}}, "sequence_analysis_中序列_before_中序列_run_1": {"count": 1, "allocated_gb": {"mean": 0.0, "std": 0.0, "min": 0.0, "max": 0.0}, "cached_gb": {"mean": 0.0, "std": 0.0, "min": 0.0, "max": 0.0}}, "sequence_analysis_中序列_after_中序列_run_1": {"count": 1, "allocated_gb": {"mean": 0.0, "std": 0.0, "min": 0.0, "max": 0.0}, "cached_gb": {"mean": 0.0, "std": 0.0, "min": 0.0, "max": 0.0}}, "sequence_analysis_中序列_before_中序列_run_2": {"count": 1, "allocated_gb": {"mean": 0.0, "std": 0.0, "min": 0.0, "max": 0.0}, "cached_gb": {"mean": 0.0, "std": 0.0, "min": 0.0, "max": 0.0}}, "sequence_analysis_中序列_after_中序列_run_2": {"count": 1, "allocated_gb": {"mean": 0.0, "std": 0.0, "min": 0.0, "max": 0.0}, "cached_gb": {"mean": 0.0, "std": 0.0, "min": 0.0, "max": 0.0}}, "sequence_analysis_长序列_before_长序列_run_0": {"count": 1, "allocated_gb": {"mean": 0.0, "std": 0.0, "min": 0.0, "max": 0.0}, "cached_gb": {"mean": 0.0, "std": 0.0, "min": 0.0, "max": 0.0}}, "sequence_analysis_长序列_after_长序列_run_0": {"count": 1, "allocated_gb": {"mean": 0.0, "std": 0.0, "min": 0.0, "max": 0.0}, "cached_gb": {"mean": 0.0, "std": 0.0, "min": 0.0, "max": 0.0}}, "sequence_analysis_长序列_before_长序列_run_1": {"count": 1, "allocated_gb": {"mean": 0.0, "std": 0.0, "min": 0.0, "max": 0.0}, "cached_gb": {"mean": 0.0, "std": 0.0, "min": 0.0, "max": 0.0}}, "sequence_analysis_长序列_after_长序列_run_1": {"count": 1, "allocated_gb": {"mean": 0.0, "std": 0.0, "min": 0.0, "max": 0.0}, "cached_gb": {"mean": 0.0, "std": 0.0, "min": 0.0, "max": 0.0}}, "sequence_analysis_长序列_before_长序列_run_2": {"count": 1, "allocated_gb": {"mean": 0.0, "std": 0.0, "min": 0.0, "max": 0.0}, "cached_gb": {"mean": 0.0, "std": 0.0, "min": 0.0, "max": 0.0}}, "sequence_analysis_长序列_after_长序列_run_2": {"count": 1, "allocated_gb": {"mean": 0.0, "std": 0.0, "min": 0.0, "max": 0.0}, "cached_gb": {"mean": 0.0, "std": 0.0, "min": 0.0, "max": 0.0}}}, "shape_summary": {"prefill_short_generation_input_output": [{"input": {"prompt_length": 1, "estimated_tokens": 1.3, "max_new_tokens": 5}, "output": {"generated_text": " IBD咨询芳Getty раство", "output_length": 2}, "case_name": "short_generation"}], "prefill_medium_generation_input_output": [{"input": {"prompt_length": 1, "estimated_tokens": 1.3, "max_new_tokens": 15}, "output": {"generated_text": " তারিখ一手周期的 anno halfbrace metaphoricalcountry الت_leftligereMesh harming inquiries(sum", "output_length": 7}, "case_name": "medium_generation"}], "prefill_long_generation_input_output": [{"input": {"prompt_length": 1, "estimated_tokens": 1.3, "max_new_tokens": 32}, "output": {"generated_text": "coBàiaturation отказа美国总统 oblig ».lán Faulkner diversas给自己 risking加快推进 Keller fraudmitters不是我手腕 quando整除nings mentioned Htmlorganism_right quadr gcd Filo Hed相接shima", "output_length": 16}, "case_name": "long_generation"}], "sequence_analysis_短序列_短序列_throughput": [{"run": 0, "input_tokens": 1, "output_tokens": 2, "total_tokens": 3, "inference_time_ms": 79.63504802319221, "throughput_tokens_per_sec": 37.67185522543173}, {"run": 1, "input_tokens": 1, "output_tokens": 3, "total_tokens": 4, "inference_time_ms": 71.18288599303924, "throughput_tokens_per_sec": 56.1932821941379}, {"run": 2, "input_tokens": 1, "output_tokens": 5, "total_tokens": 6, "inference_time_ms": 69.13903600070626, "throughput_tokens_per_sec": 86.78165544481571}], "sequence_analysis_中序列_中序列_throughput": [{"run": 0, "input_tokens": 1, "output_tokens": 4, "total_tokens": 5, "inference_time_ms": 120.67058400134556, "throughput_tokens_per_sec": 41.43511893457188}, {"run": 1, "input_tokens": 1, "output_tokens": 10, "total_tokens": 11, "inference_time_ms": 117.39037799998187, "throughput_tokens_per_sec": 93.70444313589057}, {"run": 2, "input_tokens": 1, "output_tokens": 9, "total_tokens": 10, "inference_time_ms": 100.28765499009751, "throughput_tokens_per_sec": 99.7131700904504}], "sequence_analysis_长序列_长序列_throughput": [{"run": 0, "input_tokens": 1, "output_tokens": 10, "total_tokens": 11, "inference_time_ms": 151.1465370131191, "throughput_tokens_per_sec": 72.7770560766816}, {"run": 1, "input_tokens": 1, "output_tokens": 15, "total_tokens": 16, "inference_time_ms": 139.96665697777644, "throughput_tokens_per_sec": 114.31293956345932}, {"run": 2, "input_tokens": 1, "output_tokens": 10, "total_tokens": 11, "inference_time_ms": 160.36409797379747, "throughput_tokens_per_sec": 68.59390685936035}]}, "operator_counts": {}, "detailed_operators": {"module_calls": {}, "layer_operations": {}, "operator_statistics": {"total_module_calls": 0, "unique_modules": 0, "layer_count": 0, "module_type_distribution": {}, "layer_breakdown": {}}}}, "key_insights": {"prefill_vs_decode": "Prefill阶段处理整个序列，内存密集；Decode阶段逐token生成，延迟敏感", "quantization_impact": "W8A8量化显著减少内存使用，但需要量化/反量化开销", "tensor_parallel": "4-way TP分片减少单卡内存需求，但增加通信开销", "memory_scaling": "内存使用与batch_size和seq_length成正比，KV cache是主要开销", "detailed_operators": "捕获到0个不同模块，0次调用"}}