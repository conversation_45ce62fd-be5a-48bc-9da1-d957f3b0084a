#!/usr/bin/env python3
"""
简化版算子Shape分析脚本 - 仅进行算子分析，不运行实际推理
"""

import os
import sys
import time
import json
import math
from typing import Dict, List, Any, Tuple
from collections import defaultdict, OrderedDict

class OperatorShapeAnalyzer:
    """算子Shape分析器"""
    
    def __init__(self):
        self.operator_shapes = OrderedDict()
        self.operator_types = defaultdict(list)
        
        # 模型配置 (DeepSeek-V3配置)
        self.model_config = {
            "hidden_size": 7168,
            "intermediate_size": 18944,  # Dense层
            "moe_intermediate_size": 2048,  # MoE专家层
            "num_layers": 61,
            "num_attention_heads": 56,
            "num_key_value_heads": 8,
            "head_dim": 128,
            "vocab_size": 129280,
            "tp_size": 4,
            "num_experts": 256,
            "top_k": 8
        }
        
    def analyze_operator_shapes(self, batch_size: int = 1, seq_length: int = 128):
        """分析所有算子的shape信息"""
        print(f"[ANALYZER] 分析算子Shape (B={batch_size}, S={seq_length})")
        
        B, S = batch_size, seq_length
        H = self.model_config["hidden_size"]
        I = self.model_config["intermediate_size"]
        MI = self.model_config["moe_intermediate_size"]
        num_heads = self.model_config["num_attention_heads"]
        num_kv_heads = self.model_config["num_key_value_heads"]
        head_dim = self.model_config["head_dim"]
        vocab_size = self.model_config["vocab_size"]
        tp_size = self.model_config["tp_size"]
        num_experts = self.model_config["num_experts"]
        top_k = self.model_config["top_k"]
        
        # 计算分片后的维度
        heads_per_gpu = num_heads // tp_size
        kv_heads_per_gpu = num_kv_heads // tp_size
        intermediate_per_gpu = I // tp_size
        moe_intermediate_per_gpu = MI // tp_size
        vocab_per_gpu = vocab_size // tp_size
        
        # 1. Embedding层算子
        self._add_operator("embedding", "lookup", B, S, vocab_size, H, "BF16")
        
        # 2. 单层分析（代表性）
        layer_idx = 0
        layer_prefix = f"layer_{layer_idx:02d}"
        
        # 2.1 Attention模块算子（根据原始文档格式）
        # 注意：这里按照原始文档的格式来设置shape
        self._add_operator("attn_wqa", "linear_int8", B, S, 1536, H, "W8A8")
        self._add_operator("attn_wqb", "linear_int8", B, S, 24576, 1536, "W8A8")
        self._add_operator("attn_wkv_a", "linear_int8", B, S, 576, H, "W8A8")
        self._add_operator("attn_wkv_b", "linear_int8", B, S, 32768, 512, "W8A8")
        
        # 注意力计算
        self._add_operator("attn_qk", "bmm", 16384, 1, S+1, 192, "BF16")
        self._add_operator("attn_pv", "bmm", 16384, 1, 128, S+1, "BF16")
        
        # 输出投影
        self._add_operator("attn_o", "linear_int8", B, S, H, 16384, "W8A8")
        
        # 2.2 MLP模块算子
        self._add_operator("dense_up", "linear_int8", B, S, intermediate_per_gpu, H, "W8A8")
        self._add_operator("dense_gate", "linear_int8", B, S, intermediate_per_gpu, H, "W8A8")
        self._add_operator("dense_down", "linear_int8", B, S, H, intermediate_per_gpu, "W8A8")
        
        # 2.3 MoE模块算子
        self._add_operator("moe_export_up", "linear_int8", B, S, MI, H, "W8A8")
        self._add_operator("moe_export_gate", "linear_int8", B, S, MI, H, "W8A8")
        self._add_operator("moe_export_down", "linear_int8", B, S, H, MI, "W8A8")
        self._add_operator("moe_gate", "linear", B, S, 256, H, "BF16")
        
        # 3. LM Head算子
        self._add_operator("lm_head", "linear", B, S, H, vocab_per_gpu, "BF16")
        
        # 4. 生成格式化的算子表格
        self._generate_operator_table()
        
        return self.operator_shapes
    
    def _add_operator(self, name: str, op_type: str, B: int, M: int, N: int, K: int, dtype: str):
        """添加算子信息"""
        operator_info = {
            "name": name,
            "type": op_type,
            "shape": {
                "B": B,
                "M": M,
                "N": N,
                "K": K
            },
            "dtype": dtype,
            "quantized": "int8" in op_type.lower() or "w8a8" in dtype.lower(),
            "memory_mb": self._calculate_memory(B, M, N, K, dtype),
            "flops": self._calculate_flops(op_type, B, M, N, K)
        }
        
        self.operator_shapes[name] = operator_info
        self.operator_types[op_type].append(name)
    
    def _calculate_memory(self, B: int, M: int, N: int, K: int, dtype: str) -> float:
        """计算内存使用 (MB)"""
        if dtype == "BF16":
            bytes_per_element = 2
        elif dtype in ["W8A8", "INT8"]:
            bytes_per_element = 1
        elif dtype == "INT32":
            bytes_per_element = 4
        else:
            bytes_per_element = 2  # 默认
        
        # 输入张量 + 输出张量
        input_elements = B * M * K
        output_elements = B * M * N
        total_elements = input_elements + output_elements
        
        return total_elements * bytes_per_element / (1024 * 1024)
    
    def _calculate_flops(self, op_type: str, B: int, M: int, N: int, K: int) -> int:
        """计算FLOPS"""
        if op_type in ["linear", "linear_int8"]:
            return 2 * B * M * N * K  # 矩阵乘法
        elif op_type == "bmm":
            return 2 * B * M * N * K  # 批量矩阵乘法
        elif op_type in ["activation", "elementwise"]:
            return B * M * N  # 元素操作
        elif op_type == "lookup":
            return 0  # Embedding查找操作
        else:
            return 0
    
    def _generate_operator_table(self):
        """生成格式化的算子表格"""
        print("\n" + "="*80)
        print("算子Shape分析表格 (按照文档格式)")
        print("="*80)
        print(f"{'layer':<20} {'B':<6} {'M':<8} {'N':<8} {'K':<8} {'Type':<12} {'Dtype':<8}")
        print("-"*80)
        
        for name, info in self.operator_shapes.items():
            shape = info["shape"]
            print(f"{name:<20} {shape['B']:<6} {shape['M']:<8} {shape['N']:<8} {shape['K']:<8} "
                  f"{info['type']:<12} {info['dtype']:<8}")
    
    def generate_detailed_analysis_report(self) -> Dict:
        """生成详细的分析报告"""
        # 统计不同类型算子的数量和特征
        type_stats = {}
        for op_type, ops in self.operator_types.items():
            total_flops = sum(self.operator_shapes[op]["flops"] for op in ops)
            total_memory = sum(self.operator_shapes[op]["memory_mb"] for op in ops)
            quantized_count = sum(1 for op in ops if self.operator_shapes[op]["quantized"])
            
            type_stats[op_type] = {
                "count": len(ops),
                "total_flops": total_flops,
                "total_memory_mb": total_memory,
                "quantized_count": quantized_count,
                "operators": ops
            }
        
        # 按模块统计
        module_stats = self._analyze_by_module()
        
        # 量化算子统计
        quantization_stats = self._analyze_quantization()
        
        return {
            "operator_count": len(self.operator_shapes),
            "type_statistics": type_stats,
            "module_statistics": module_stats,
            "quantization_statistics": quantization_stats,
            "operator_details": self.operator_shapes
        }
    
    def _analyze_by_module(self) -> Dict:
        """按模块分析算子"""
        modules = {
            "embedding": [],
            "attention": [],
            "mlp": [],
            "moe": [],
            "lm_head": []
        }
        
        for name, info in self.operator_shapes.items():
            if "embedding" in name:
                modules["embedding"].append(name)
            elif "attn" in name:
                modules["attention"].append(name)
            elif "dense" in name:
                modules["mlp"].append(name)
            elif "moe" in name:
                modules["moe"].append(name)
            elif "lm_head" in name:
                modules["lm_head"].append(name)
        
        # 统计每个模块的特征
        module_stats = {}
        for module, ops in modules.items():
            if ops:
                total_flops = sum(self.operator_shapes[op]["flops"] for op in ops)
                total_memory = sum(self.operator_shapes[op]["memory_mb"] for op in ops)
                quantized_count = sum(1 for op in ops if self.operator_shapes[op]["quantized"])
                
                module_stats[module] = {
                    "operator_count": len(ops),
                    "total_flops": total_flops,
                    "total_memory_mb": total_memory,
                    "quantized_operators": quantized_count,
                    "quantization_ratio": quantized_count / len(ops) if ops else 0
                }
        
        return module_stats
    
    def _analyze_quantization(self) -> Dict:
        """分析量化相关统计"""
        total_ops = len(self.operator_shapes)
        quantized_ops = sum(1 for info in self.operator_shapes.values() if info["quantized"])
        
        # 按数据类型统计
        dtype_stats = defaultdict(int)
        for info in self.operator_shapes.values():
            dtype_stats[info["dtype"]] += 1
        
        # 量化算子的FLOPS占比
        total_flops = sum(info["flops"] for info in self.operator_shapes.values())
        quantized_flops = sum(info["flops"] for info in self.operator_shapes.values() if info["quantized"])
        
        return {
            "total_operators": total_ops,
            "quantized_operators": quantized_ops,
            "quantization_ratio": quantized_ops / total_ops if total_ops > 0 else 0,
            "dtype_distribution": dict(dtype_stats),
            "flops_distribution": {
                "total_flops": total_flops,
                "quantized_flops": quantized_flops,
                "quantized_flops_ratio": quantized_flops / total_flops if total_flops > 0 else 0
            }
        }

def main():
    """主函数"""
    print("="*80)
    print("简化版SGLang INT8模型算子Shape分析")
    print("="*80)
    
    try:
        # 创建分析器
        analyzer = OperatorShapeAnalyzer()
        
        # 分析不同输入配置的算子shapes
        test_configs = [
            {"name": "small_batch", "batch_size": 1, "seq_length": 32},
            {"name": "medium_batch", "batch_size": 1, "seq_length": 128},
            {"name": "large_batch", "batch_size": 1, "seq_length": 512},
            {"name": "multi_batch", "batch_size": 4, "seq_length": 128},
        ]
        
        all_analysis = {}
        
        for config in test_configs:
            print(f"\n分析配置: {config['name']} (B={config['batch_size']}, S={config['seq_length']})")
            
            # 重新创建分析器以清除之前的数据
            analyzer = OperatorShapeAnalyzer()
            
            # 分析算子shapes
            operator_shapes = analyzer.analyze_operator_shapes(
                batch_size=config["batch_size"],
                seq_length=config["seq_length"]
            )
            
            # 生成详细报告
            detailed_report = analyzer.generate_detailed_analysis_report()
            
            all_analysis[config["name"]] = {
                "config": config,
                "operator_shapes": operator_shapes,
                "analysis_report": detailed_report
            }
        
        # 保存分析结果
        output_file = "simplified_int8_operator_analysis.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(all_analysis, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"\n简化版算子分析完成，结果已保存到: {output_file}")
        
        # 生成汇总报告
        generate_summary_report(all_analysis)
        
        # 生成markdown格式的算子表格
        generate_markdown_operator_table(all_analysis)
        
        return all_analysis
        
    except Exception as e:
        print(f"分析失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def generate_summary_report(all_analysis: Dict):
    """生成汇总报告"""
    print("\n" + "="*100)
    print("简化版INT8算子分析汇总报告")
    print("="*100)
    
    for config_name, analysis in all_analysis.items():
        config = analysis["config"]
        report = analysis["analysis_report"]
        
        print(f"\n配置: {config_name} (B={config['batch_size']}, S={config['seq_length']})")
        print("-" * 60)
        
        print(f"总算子数量: {report['operator_count']}")
        print(f"量化算子数量: {report['quantization_statistics']['quantized_operators']}")
        print(f"量化比例: {report['quantization_statistics']['quantization_ratio']:.2%}")
        
        print("\n按模块统计:")
        for module, stats in report["module_statistics"].items():
            print(f"  {module}: {stats['operator_count']}个算子, "
                  f"{stats['quantized_operators']}个量化, "
                  f"量化率{stats['quantization_ratio']:.2%}")
        
        print("\n按算子类型统计:")
        for op_type, stats in report["type_statistics"].items():
            print(f"  {op_type}: {stats['count']}个, "
                  f"FLOPS: {stats['total_flops']:,}, "
                  f"内存: {stats['total_memory_mb']:.1f}MB")
        
        total_flops = sum(stats['total_flops'] for stats in report["type_statistics"].values())
        total_memory = sum(stats['total_memory_mb'] for stats in report["type_statistics"].values())
        print(f"\n总计: FLOPS: {total_flops:,}, 内存: {total_memory:.1f}MB")

def generate_markdown_operator_table(all_analysis: Dict):
    """生成markdown格式的算子表格"""
    if not all_analysis:
        return
    
    # 选择medium_batch配置作为示例
    medium_analysis = all_analysis.get("medium_batch")
    if not medium_analysis:
        return
    
    operator_shapes = medium_analysis["operator_shapes"]
    
    # 生成markdown表格
    markdown_content = """# SGLang INT8模型算子Shape分析表格

## 按照算子分析文档格式统计 (B=1, S=128)

### 算子Shape表格

| layer | B | M | N | K | Type | Dtype | Memory(MB) | FLOPS |
|-------|---|---|---|---|------|-------|------------|-------|
"""
    
    for name, info in operator_shapes.items():
        shape = info["shape"]
        markdown_content += f"| {name} | {shape['B']} | {shape['M']} | {shape['N']} | {shape['K']} | {info['type']} | {info['dtype']} | {info['memory_mb']:.2f} | {info['flops']:,} |\n"
    
    # 添加统计信息
    total_ops = len(operator_shapes)
    quantized_ops = sum(1 for info in operator_shapes.values() if info["quantized"])
    total_flops = sum(info["flops"] for info in operator_shapes.values())
    total_memory = sum(info["memory_mb"] for info in operator_shapes.values())
    
    markdown_content += f"""

### 统计汇总

- **总算子数量**: {total_ops}
- **量化算子数量**: {quantized_ops} ({quantized_ops/total_ops:.1%})
- **总FLOPS**: {total_flops:,}
- **总内存**: {total_memory:.2f} MB

### 按类型分布

"""
    
    # 按类型统计
    type_stats = defaultdict(int)
    for info in operator_shapes.values():
        type_stats[info["type"]] += 1
    
    for op_type, count in type_stats.items():
        markdown_content += f"- **{op_type}**: {count}个算子\n"
    
    # 保存markdown文件
    with open("operator_shape_analysis_table.md", 'w', encoding='utf-8') as f:
        f.write(markdown_content)
    
    print("\nMarkdown格式的算子表格已保存到: operator_shape_analysis_table.md")

if __name__ == "__main__":
    main()
