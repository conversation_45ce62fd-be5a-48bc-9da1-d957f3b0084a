{"small_batch": {"config": {"name": "small_batch", "batch_size": 1, "seq_length": 32}, "operator_shapes": {"embedding": {"name": "embedding", "type": "lookup", "shape": {"B": 1, "M": 32, "N": 129280, "K": 7168}, "dtype": "BF16", "quantized": false, "memory_mb": 8.328125, "flops": 0}, "attn_wqa": {"name": "attn_wqa", "type": "linear_int8", "shape": {"B": 1, "M": 32, "N": 1536, "K": 7168}, "dtype": "W8A8", "quantized": true, "memory_mb": 0.265625, "flops": 704643072}, "attn_wqb": {"name": "attn_wqb", "type": "linear_int8", "shape": {"B": 1, "M": 32, "N": 24576, "K": 1536}, "dtype": "W8A8", "quantized": true, "memory_mb": 0.796875, "flops": 2415919104}, "attn_wkv_a": {"name": "attn_wkv_a", "type": "linear_int8", "shape": {"B": 1, "M": 32, "N": 576, "K": 7168}, "dtype": "W8A8", "quantized": true, "memory_mb": 0.236328125, "flops": 264241152}, "attn_wkv_b": {"name": "attn_wkv_b", "type": "linear_int8", "shape": {"B": 1, "M": 32, "N": 32768, "K": 512}, "dtype": "W8A8", "quantized": true, "memory_mb": 1.015625, "flops": 1073741824}, "attn_qk": {"name": "attn_qk", "type": "bmm", "shape": {"B": 16384, "M": 1, "N": 33, "K": 192}, "dtype": "BF16", "quantized": false, "memory_mb": 7.03125, "flops": 207618048}, "attn_pv": {"name": "attn_pv", "type": "bmm", "shape": {"B": 16384, "M": 1, "N": 128, "K": 33}, "dtype": "BF16", "quantized": false, "memory_mb": 5.03125, "flops": 138412032}, "attn_o": {"name": "attn_o", "type": "linear_int8", "shape": {"B": 1, "M": 32, "N": 7168, "K": 16384}, "dtype": "W8A8", "quantized": true, "memory_mb": 0.71875, "flops": 7516192768}, "dense_up": {"name": "dense_up", "type": "linear_int8", "shape": {"B": 1, "M": 32, "N": 4736, "K": 7168}, "dtype": "W8A8", "quantized": true, "memory_mb": 0.36328125, "flops": 2172649472}, "dense_gate": {"name": "dense_gate", "type": "linear_int8", "shape": {"B": 1, "M": 32, "N": 4736, "K": 7168}, "dtype": "W8A8", "quantized": true, "memory_mb": 0.36328125, "flops": 2172649472}, "dense_down": {"name": "dense_down", "type": "linear_int8", "shape": {"B": 1, "M": 32, "N": 7168, "K": 4736}, "dtype": "W8A8", "quantized": true, "memory_mb": 0.36328125, "flops": 2172649472}, "moe_export_up": {"name": "moe_export_up", "type": "linear_int8", "shape": {"B": 1, "M": 32, "N": 2048, "K": 7168}, "dtype": "W8A8", "quantized": true, "memory_mb": 0.28125, "flops": 939524096}, "moe_export_gate": {"name": "moe_export_gate", "type": "linear_int8", "shape": {"B": 1, "M": 32, "N": 2048, "K": 7168}, "dtype": "W8A8", "quantized": true, "memory_mb": 0.28125, "flops": 939524096}, "moe_export_down": {"name": "moe_export_down", "type": "linear_int8", "shape": {"B": 1, "M": 32, "N": 7168, "K": 2048}, "dtype": "W8A8", "quantized": true, "memory_mb": 0.28125, "flops": 939524096}, "moe_gate": {"name": "moe_gate", "type": "linear", "shape": {"B": 1, "M": 32, "N": 256, "K": 7168}, "dtype": "BF16", "quantized": false, "memory_mb": 0.453125, "flops": 117440512}, "lm_head": {"name": "lm_head", "type": "linear", "shape": {"B": 1, "M": 32, "N": 7168, "K": 32320}, "dtype": "BF16", "quantized": false, "memory_mb": 2.41015625, "flops": 14826864640}}, "analysis_report": {"operator_count": 16, "type_statistics": {"lookup": {"count": 1, "total_flops": 0, "total_memory_mb": 8.328125, "quantized_count": 0, "operators": ["embedding"]}, "linear_int8": {"count": 11, "total_flops": 21311258624, "total_memory_mb": 4.966796875, "quantized_count": 11, "operators": ["attn_wqa", "attn_wqb", "attn_wkv_a", "attn_wkv_b", "attn_o", "dense_up", "dense_gate", "dense_down", "moe_export_up", "moe_export_gate", "moe_export_down"]}, "bmm": {"count": 2, "total_flops": 346030080, "total_memory_mb": 12.0625, "quantized_count": 0, "operators": ["attn_qk", "attn_pv"]}, "linear": {"count": 2, "total_flops": 14944305152, "total_memory_mb": 2.86328125, "quantized_count": 0, "operators": ["moe_gate", "lm_head"]}}, "module_statistics": {"embedding": {"operator_count": 1, "total_flops": 0, "total_memory_mb": 8.328125, "quantized_operators": 0, "quantization_ratio": 0.0}, "attention": {"operator_count": 7, "total_flops": 12320768000, "total_memory_mb": 15.095703125, "quantized_operators": 5, "quantization_ratio": 0.7142857142857143}, "mlp": {"operator_count": 3, "total_flops": 6517948416, "total_memory_mb": 1.08984375, "quantized_operators": 3, "quantization_ratio": 1.0}, "moe": {"operator_count": 4, "total_flops": 2936012800, "total_memory_mb": 1.296875, "quantized_operators": 3, "quantization_ratio": 0.75}, "lm_head": {"operator_count": 1, "total_flops": 14826864640, "total_memory_mb": 2.41015625, "quantized_operators": 0, "quantization_ratio": 0.0}}, "quantization_statistics": {"total_operators": 16, "quantized_operators": 11, "quantization_ratio": 0.6875, "dtype_distribution": {"BF16": 5, "W8A8": 11}, "flops_distribution": {"total_flops": 36601593856, "quantized_flops": 21311258624, "quantized_flops_ratio": 0.5822494700051567}}, "operator_details": {"embedding": {"name": "embedding", "type": "lookup", "shape": {"B": 1, "M": 32, "N": 129280, "K": 7168}, "dtype": "BF16", "quantized": false, "memory_mb": 8.328125, "flops": 0}, "attn_wqa": {"name": "attn_wqa", "type": "linear_int8", "shape": {"B": 1, "M": 32, "N": 1536, "K": 7168}, "dtype": "W8A8", "quantized": true, "memory_mb": 0.265625, "flops": 704643072}, "attn_wqb": {"name": "attn_wqb", "type": "linear_int8", "shape": {"B": 1, "M": 32, "N": 24576, "K": 1536}, "dtype": "W8A8", "quantized": true, "memory_mb": 0.796875, "flops": 2415919104}, "attn_wkv_a": {"name": "attn_wkv_a", "type": "linear_int8", "shape": {"B": 1, "M": 32, "N": 576, "K": 7168}, "dtype": "W8A8", "quantized": true, "memory_mb": 0.236328125, "flops": 264241152}, "attn_wkv_b": {"name": "attn_wkv_b", "type": "linear_int8", "shape": {"B": 1, "M": 32, "N": 32768, "K": 512}, "dtype": "W8A8", "quantized": true, "memory_mb": 1.015625, "flops": 1073741824}, "attn_qk": {"name": "attn_qk", "type": "bmm", "shape": {"B": 16384, "M": 1, "N": 33, "K": 192}, "dtype": "BF16", "quantized": false, "memory_mb": 7.03125, "flops": 207618048}, "attn_pv": {"name": "attn_pv", "type": "bmm", "shape": {"B": 16384, "M": 1, "N": 128, "K": 33}, "dtype": "BF16", "quantized": false, "memory_mb": 5.03125, "flops": 138412032}, "attn_o": {"name": "attn_o", "type": "linear_int8", "shape": {"B": 1, "M": 32, "N": 7168, "K": 16384}, "dtype": "W8A8", "quantized": true, "memory_mb": 0.71875, "flops": 7516192768}, "dense_up": {"name": "dense_up", "type": "linear_int8", "shape": {"B": 1, "M": 32, "N": 4736, "K": 7168}, "dtype": "W8A8", "quantized": true, "memory_mb": 0.36328125, "flops": 2172649472}, "dense_gate": {"name": "dense_gate", "type": "linear_int8", "shape": {"B": 1, "M": 32, "N": 4736, "K": 7168}, "dtype": "W8A8", "quantized": true, "memory_mb": 0.36328125, "flops": 2172649472}, "dense_down": {"name": "dense_down", "type": "linear_int8", "shape": {"B": 1, "M": 32, "N": 7168, "K": 4736}, "dtype": "W8A8", "quantized": true, "memory_mb": 0.36328125, "flops": 2172649472}, "moe_export_up": {"name": "moe_export_up", "type": "linear_int8", "shape": {"B": 1, "M": 32, "N": 2048, "K": 7168}, "dtype": "W8A8", "quantized": true, "memory_mb": 0.28125, "flops": 939524096}, "moe_export_gate": {"name": "moe_export_gate", "type": "linear_int8", "shape": {"B": 1, "M": 32, "N": 2048, "K": 7168}, "dtype": "W8A8", "quantized": true, "memory_mb": 0.28125, "flops": 939524096}, "moe_export_down": {"name": "moe_export_down", "type": "linear_int8", "shape": {"B": 1, "M": 32, "N": 7168, "K": 2048}, "dtype": "W8A8", "quantized": true, "memory_mb": 0.28125, "flops": 939524096}, "moe_gate": {"name": "moe_gate", "type": "linear", "shape": {"B": 1, "M": 32, "N": 256, "K": 7168}, "dtype": "BF16", "quantized": false, "memory_mb": 0.453125, "flops": 117440512}, "lm_head": {"name": "lm_head", "type": "linear", "shape": {"B": 1, "M": 32, "N": 7168, "K": 32320}, "dtype": "BF16", "quantized": false, "memory_mb": 2.41015625, "flops": 14826864640}}}}, "medium_batch": {"config": {"name": "medium_batch", "batch_size": 1, "seq_length": 128}, "operator_shapes": {"embedding": {"name": "embedding", "type": "lookup", "shape": {"B": 1, "M": 128, "N": 129280, "K": 7168}, "dtype": "BF16", "quantized": false, "memory_mb": 33.3125, "flops": 0}, "attn_wqa": {"name": "attn_wqa", "type": "linear_int8", "shape": {"B": 1, "M": 128, "N": 1536, "K": 7168}, "dtype": "W8A8", "quantized": true, "memory_mb": 1.0625, "flops": 2818572288}, "attn_wqb": {"name": "attn_wqb", "type": "linear_int8", "shape": {"B": 1, "M": 128, "N": 24576, "K": 1536}, "dtype": "W8A8", "quantized": true, "memory_mb": 3.1875, "flops": 9663676416}, "attn_wkv_a": {"name": "attn_wkv_a", "type": "linear_int8", "shape": {"B": 1, "M": 128, "N": 576, "K": 7168}, "dtype": "W8A8", "quantized": true, "memory_mb": 0.9453125, "flops": 1056964608}, "attn_wkv_b": {"name": "attn_wkv_b", "type": "linear_int8", "shape": {"B": 1, "M": 128, "N": 32768, "K": 512}, "dtype": "W8A8", "quantized": true, "memory_mb": 4.0625, "flops": 4294967296}, "attn_qk": {"name": "attn_qk", "type": "bmm", "shape": {"B": 16384, "M": 1, "N": 129, "K": 192}, "dtype": "BF16", "quantized": false, "memory_mb": 10.03125, "flops": 811597824}, "attn_pv": {"name": "attn_pv", "type": "bmm", "shape": {"B": 16384, "M": 1, "N": 128, "K": 129}, "dtype": "BF16", "quantized": false, "memory_mb": 8.03125, "flops": 541065216}, "attn_o": {"name": "attn_o", "type": "linear_int8", "shape": {"B": 1, "M": 128, "N": 7168, "K": 16384}, "dtype": "W8A8", "quantized": true, "memory_mb": 2.875, "flops": 30064771072}, "dense_up": {"name": "dense_up", "type": "linear_int8", "shape": {"B": 1, "M": 128, "N": 4736, "K": 7168}, "dtype": "W8A8", "quantized": true, "memory_mb": 1.453125, "flops": 8690597888}, "dense_gate": {"name": "dense_gate", "type": "linear_int8", "shape": {"B": 1, "M": 128, "N": 4736, "K": 7168}, "dtype": "W8A8", "quantized": true, "memory_mb": 1.453125, "flops": 8690597888}, "dense_down": {"name": "dense_down", "type": "linear_int8", "shape": {"B": 1, "M": 128, "N": 7168, "K": 4736}, "dtype": "W8A8", "quantized": true, "memory_mb": 1.453125, "flops": 8690597888}, "moe_export_up": {"name": "moe_export_up", "type": "linear_int8", "shape": {"B": 1, "M": 128, "N": 2048, "K": 7168}, "dtype": "W8A8", "quantized": true, "memory_mb": 1.125, "flops": 3758096384}, "moe_export_gate": {"name": "moe_export_gate", "type": "linear_int8", "shape": {"B": 1, "M": 128, "N": 2048, "K": 7168}, "dtype": "W8A8", "quantized": true, "memory_mb": 1.125, "flops": 3758096384}, "moe_export_down": {"name": "moe_export_down", "type": "linear_int8", "shape": {"B": 1, "M": 128, "N": 7168, "K": 2048}, "dtype": "W8A8", "quantized": true, "memory_mb": 1.125, "flops": 3758096384}, "moe_gate": {"name": "moe_gate", "type": "linear", "shape": {"B": 1, "M": 128, "N": 256, "K": 7168}, "dtype": "BF16", "quantized": false, "memory_mb": 1.8125, "flops": 469762048}, "lm_head": {"name": "lm_head", "type": "linear", "shape": {"B": 1, "M": 128, "N": 7168, "K": 32320}, "dtype": "BF16", "quantized": false, "memory_mb": 9.640625, "flops": 59307458560}}, "analysis_report": {"operator_count": 16, "type_statistics": {"lookup": {"count": 1, "total_flops": 0, "total_memory_mb": 33.3125, "quantized_count": 0, "operators": ["embedding"]}, "linear_int8": {"count": 11, "total_flops": 85245034496, "total_memory_mb": 19.8671875, "quantized_count": 11, "operators": ["attn_wqa", "attn_wqb", "attn_wkv_a", "attn_wkv_b", "attn_o", "dense_up", "dense_gate", "dense_down", "moe_export_up", "moe_export_gate", "moe_export_down"]}, "bmm": {"count": 2, "total_flops": 1352663040, "total_memory_mb": 18.0625, "quantized_count": 0, "operators": ["attn_qk", "attn_pv"]}, "linear": {"count": 2, "total_flops": 59777220608, "total_memory_mb": 11.453125, "quantized_count": 0, "operators": ["moe_gate", "lm_head"]}}, "module_statistics": {"embedding": {"operator_count": 1, "total_flops": 0, "total_memory_mb": 33.3125, "quantized_operators": 0, "quantization_ratio": 0.0}, "attention": {"operator_count": 7, "total_flops": 49251614720, "total_memory_mb": 30.1953125, "quantized_operators": 5, "quantization_ratio": 0.7142857142857143}, "mlp": {"operator_count": 3, "total_flops": 26071793664, "total_memory_mb": 4.359375, "quantized_operators": 3, "quantization_ratio": 1.0}, "moe": {"operator_count": 4, "total_flops": 11744051200, "total_memory_mb": 5.1875, "quantized_operators": 3, "quantization_ratio": 0.75}, "lm_head": {"operator_count": 1, "total_flops": 59307458560, "total_memory_mb": 9.640625, "quantized_operators": 0, "quantization_ratio": 0.0}}, "quantization_statistics": {"total_operators": 16, "quantized_operators": 11, "quantization_ratio": 0.6875, "dtype_distribution": {"BF16": 5, "W8A8": 11}, "flops_distribution": {"total_flops": 146374918144, "quantized_flops": 85245034496, "quantized_flops_ratio": 0.5823746006275341}}, "operator_details": {"embedding": {"name": "embedding", "type": "lookup", "shape": {"B": 1, "M": 128, "N": 129280, "K": 7168}, "dtype": "BF16", "quantized": false, "memory_mb": 33.3125, "flops": 0}, "attn_wqa": {"name": "attn_wqa", "type": "linear_int8", "shape": {"B": 1, "M": 128, "N": 1536, "K": 7168}, "dtype": "W8A8", "quantized": true, "memory_mb": 1.0625, "flops": 2818572288}, "attn_wqb": {"name": "attn_wqb", "type": "linear_int8", "shape": {"B": 1, "M": 128, "N": 24576, "K": 1536}, "dtype": "W8A8", "quantized": true, "memory_mb": 3.1875, "flops": 9663676416}, "attn_wkv_a": {"name": "attn_wkv_a", "type": "linear_int8", "shape": {"B": 1, "M": 128, "N": 576, "K": 7168}, "dtype": "W8A8", "quantized": true, "memory_mb": 0.9453125, "flops": 1056964608}, "attn_wkv_b": {"name": "attn_wkv_b", "type": "linear_int8", "shape": {"B": 1, "M": 128, "N": 32768, "K": 512}, "dtype": "W8A8", "quantized": true, "memory_mb": 4.0625, "flops": 4294967296}, "attn_qk": {"name": "attn_qk", "type": "bmm", "shape": {"B": 16384, "M": 1, "N": 129, "K": 192}, "dtype": "BF16", "quantized": false, "memory_mb": 10.03125, "flops": 811597824}, "attn_pv": {"name": "attn_pv", "type": "bmm", "shape": {"B": 16384, "M": 1, "N": 128, "K": 129}, "dtype": "BF16", "quantized": false, "memory_mb": 8.03125, "flops": 541065216}, "attn_o": {"name": "attn_o", "type": "linear_int8", "shape": {"B": 1, "M": 128, "N": 7168, "K": 16384}, "dtype": "W8A8", "quantized": true, "memory_mb": 2.875, "flops": 30064771072}, "dense_up": {"name": "dense_up", "type": "linear_int8", "shape": {"B": 1, "M": 128, "N": 4736, "K": 7168}, "dtype": "W8A8", "quantized": true, "memory_mb": 1.453125, "flops": 8690597888}, "dense_gate": {"name": "dense_gate", "type": "linear_int8", "shape": {"B": 1, "M": 128, "N": 4736, "K": 7168}, "dtype": "W8A8", "quantized": true, "memory_mb": 1.453125, "flops": 8690597888}, "dense_down": {"name": "dense_down", "type": "linear_int8", "shape": {"B": 1, "M": 128, "N": 7168, "K": 4736}, "dtype": "W8A8", "quantized": true, "memory_mb": 1.453125, "flops": 8690597888}, "moe_export_up": {"name": "moe_export_up", "type": "linear_int8", "shape": {"B": 1, "M": 128, "N": 2048, "K": 7168}, "dtype": "W8A8", "quantized": true, "memory_mb": 1.125, "flops": 3758096384}, "moe_export_gate": {"name": "moe_export_gate", "type": "linear_int8", "shape": {"B": 1, "M": 128, "N": 2048, "K": 7168}, "dtype": "W8A8", "quantized": true, "memory_mb": 1.125, "flops": 3758096384}, "moe_export_down": {"name": "moe_export_down", "type": "linear_int8", "shape": {"B": 1, "M": 128, "N": 7168, "K": 2048}, "dtype": "W8A8", "quantized": true, "memory_mb": 1.125, "flops": 3758096384}, "moe_gate": {"name": "moe_gate", "type": "linear", "shape": {"B": 1, "M": 128, "N": 256, "K": 7168}, "dtype": "BF16", "quantized": false, "memory_mb": 1.8125, "flops": 469762048}, "lm_head": {"name": "lm_head", "type": "linear", "shape": {"B": 1, "M": 128, "N": 7168, "K": 32320}, "dtype": "BF16", "quantized": false, "memory_mb": 9.640625, "flops": 59307458560}}}}, "large_batch": {"config": {"name": "large_batch", "batch_size": 1, "seq_length": 512}, "operator_shapes": {"embedding": {"name": "embedding", "type": "lookup", "shape": {"B": 1, "M": 512, "N": 129280, "K": 7168}, "dtype": "BF16", "quantized": false, "memory_mb": 133.25, "flops": 0}, "attn_wqa": {"name": "attn_wqa", "type": "linear_int8", "shape": {"B": 1, "M": 512, "N": 1536, "K": 7168}, "dtype": "W8A8", "quantized": true, "memory_mb": 4.25, "flops": 11274289152}, "attn_wqb": {"name": "attn_wqb", "type": "linear_int8", "shape": {"B": 1, "M": 512, "N": 24576, "K": 1536}, "dtype": "W8A8", "quantized": true, "memory_mb": 12.75, "flops": 38654705664}, "attn_wkv_a": {"name": "attn_wkv_a", "type": "linear_int8", "shape": {"B": 1, "M": 512, "N": 576, "K": 7168}, "dtype": "W8A8", "quantized": true, "memory_mb": 3.78125, "flops": 4227858432}, "attn_wkv_b": {"name": "attn_wkv_b", "type": "linear_int8", "shape": {"B": 1, "M": 512, "N": 32768, "K": 512}, "dtype": "W8A8", "quantized": true, "memory_mb": 16.25, "flops": 17179869184}, "attn_qk": {"name": "attn_qk", "type": "bmm", "shape": {"B": 16384, "M": 1, "N": 513, "K": 192}, "dtype": "BF16", "quantized": false, "memory_mb": 22.03125, "flops": 3227516928}, "attn_pv": {"name": "attn_pv", "type": "bmm", "shape": {"B": 16384, "M": 1, "N": 128, "K": 513}, "dtype": "BF16", "quantized": false, "memory_mb": 20.03125, "flops": 2151677952}, "attn_o": {"name": "attn_o", "type": "linear_int8", "shape": {"B": 1, "M": 512, "N": 7168, "K": 16384}, "dtype": "W8A8", "quantized": true, "memory_mb": 11.5, "flops": 120259084288}, "dense_up": {"name": "dense_up", "type": "linear_int8", "shape": {"B": 1, "M": 512, "N": 4736, "K": 7168}, "dtype": "W8A8", "quantized": true, "memory_mb": 5.8125, "flops": 34762391552}, "dense_gate": {"name": "dense_gate", "type": "linear_int8", "shape": {"B": 1, "M": 512, "N": 4736, "K": 7168}, "dtype": "W8A8", "quantized": true, "memory_mb": 5.8125, "flops": 34762391552}, "dense_down": {"name": "dense_down", "type": "linear_int8", "shape": {"B": 1, "M": 512, "N": 7168, "K": 4736}, "dtype": "W8A8", "quantized": true, "memory_mb": 5.8125, "flops": 34762391552}, "moe_export_up": {"name": "moe_export_up", "type": "linear_int8", "shape": {"B": 1, "M": 512, "N": 2048, "K": 7168}, "dtype": "W8A8", "quantized": true, "memory_mb": 4.5, "flops": 15032385536}, "moe_export_gate": {"name": "moe_export_gate", "type": "linear_int8", "shape": {"B": 1, "M": 512, "N": 2048, "K": 7168}, "dtype": "W8A8", "quantized": true, "memory_mb": 4.5, "flops": 15032385536}, "moe_export_down": {"name": "moe_export_down", "type": "linear_int8", "shape": {"B": 1, "M": 512, "N": 7168, "K": 2048}, "dtype": "W8A8", "quantized": true, "memory_mb": 4.5, "flops": 15032385536}, "moe_gate": {"name": "moe_gate", "type": "linear", "shape": {"B": 1, "M": 512, "N": 256, "K": 7168}, "dtype": "BF16", "quantized": false, "memory_mb": 7.25, "flops": 1879048192}, "lm_head": {"name": "lm_head", "type": "linear", "shape": {"B": 1, "M": 512, "N": 7168, "K": 32320}, "dtype": "BF16", "quantized": false, "memory_mb": 38.5625, "flops": 237229834240}}, "analysis_report": {"operator_count": 16, "type_statistics": {"lookup": {"count": 1, "total_flops": 0, "total_memory_mb": 133.25, "quantized_count": 0, "operators": ["embedding"]}, "linear_int8": {"count": 11, "total_flops": 340980137984, "total_memory_mb": 79.46875, "quantized_count": 11, "operators": ["attn_wqa", "attn_wqb", "attn_wkv_a", "attn_wkv_b", "attn_o", "dense_up", "dense_gate", "dense_down", "moe_export_up", "moe_export_gate", "moe_export_down"]}, "bmm": {"count": 2, "total_flops": 5379194880, "total_memory_mb": 42.0625, "quantized_count": 0, "operators": ["attn_qk", "attn_pv"]}, "linear": {"count": 2, "total_flops": 239108882432, "total_memory_mb": 45.8125, "quantized_count": 0, "operators": ["moe_gate", "lm_head"]}}, "module_statistics": {"embedding": {"operator_count": 1, "total_flops": 0, "total_memory_mb": 133.25, "quantized_operators": 0, "quantization_ratio": 0.0}, "attention": {"operator_count": 7, "total_flops": 196975001600, "total_memory_mb": 90.59375, "quantized_operators": 5, "quantization_ratio": 0.7142857142857143}, "mlp": {"operator_count": 3, "total_flops": 104287174656, "total_memory_mb": 17.4375, "quantized_operators": 3, "quantization_ratio": 1.0}, "moe": {"operator_count": 4, "total_flops": 46976204800, "total_memory_mb": 20.75, "quantized_operators": 3, "quantization_ratio": 0.75}, "lm_head": {"operator_count": 1, "total_flops": 237229834240, "total_memory_mb": 38.5625, "quantized_operators": 0, "quantization_ratio": 0.0}}, "quantization_statistics": {"total_operators": 16, "quantized_operators": 11, "quantization_ratio": 0.6875, "dtype_distribution": {"BF16": 5, "W8A8": 11}, "flops_distribution": {"total_flops": 585468215296, "quantized_flops": 340980137984, "quantized_flops_ratio": 0.5824058916872333}}, "operator_details": {"embedding": {"name": "embedding", "type": "lookup", "shape": {"B": 1, "M": 512, "N": 129280, "K": 7168}, "dtype": "BF16", "quantized": false, "memory_mb": 133.25, "flops": 0}, "attn_wqa": {"name": "attn_wqa", "type": "linear_int8", "shape": {"B": 1, "M": 512, "N": 1536, "K": 7168}, "dtype": "W8A8", "quantized": true, "memory_mb": 4.25, "flops": 11274289152}, "attn_wqb": {"name": "attn_wqb", "type": "linear_int8", "shape": {"B": 1, "M": 512, "N": 24576, "K": 1536}, "dtype": "W8A8", "quantized": true, "memory_mb": 12.75, "flops": 38654705664}, "attn_wkv_a": {"name": "attn_wkv_a", "type": "linear_int8", "shape": {"B": 1, "M": 512, "N": 576, "K": 7168}, "dtype": "W8A8", "quantized": true, "memory_mb": 3.78125, "flops": 4227858432}, "attn_wkv_b": {"name": "attn_wkv_b", "type": "linear_int8", "shape": {"B": 1, "M": 512, "N": 32768, "K": 512}, "dtype": "W8A8", "quantized": true, "memory_mb": 16.25, "flops": 17179869184}, "attn_qk": {"name": "attn_qk", "type": "bmm", "shape": {"B": 16384, "M": 1, "N": 513, "K": 192}, "dtype": "BF16", "quantized": false, "memory_mb": 22.03125, "flops": 3227516928}, "attn_pv": {"name": "attn_pv", "type": "bmm", "shape": {"B": 16384, "M": 1, "N": 128, "K": 513}, "dtype": "BF16", "quantized": false, "memory_mb": 20.03125, "flops": 2151677952}, "attn_o": {"name": "attn_o", "type": "linear_int8", "shape": {"B": 1, "M": 512, "N": 7168, "K": 16384}, "dtype": "W8A8", "quantized": true, "memory_mb": 11.5, "flops": 120259084288}, "dense_up": {"name": "dense_up", "type": "linear_int8", "shape": {"B": 1, "M": 512, "N": 4736, "K": 7168}, "dtype": "W8A8", "quantized": true, "memory_mb": 5.8125, "flops": 34762391552}, "dense_gate": {"name": "dense_gate", "type": "linear_int8", "shape": {"B": 1, "M": 512, "N": 4736, "K": 7168}, "dtype": "W8A8", "quantized": true, "memory_mb": 5.8125, "flops": 34762391552}, "dense_down": {"name": "dense_down", "type": "linear_int8", "shape": {"B": 1, "M": 512, "N": 7168, "K": 4736}, "dtype": "W8A8", "quantized": true, "memory_mb": 5.8125, "flops": 34762391552}, "moe_export_up": {"name": "moe_export_up", "type": "linear_int8", "shape": {"B": 1, "M": 512, "N": 2048, "K": 7168}, "dtype": "W8A8", "quantized": true, "memory_mb": 4.5, "flops": 15032385536}, "moe_export_gate": {"name": "moe_export_gate", "type": "linear_int8", "shape": {"B": 1, "M": 512, "N": 2048, "K": 7168}, "dtype": "W8A8", "quantized": true, "memory_mb": 4.5, "flops": 15032385536}, "moe_export_down": {"name": "moe_export_down", "type": "linear_int8", "shape": {"B": 1, "M": 512, "N": 7168, "K": 2048}, "dtype": "W8A8", "quantized": true, "memory_mb": 4.5, "flops": 15032385536}, "moe_gate": {"name": "moe_gate", "type": "linear", "shape": {"B": 1, "M": 512, "N": 256, "K": 7168}, "dtype": "BF16", "quantized": false, "memory_mb": 7.25, "flops": 1879048192}, "lm_head": {"name": "lm_head", "type": "linear", "shape": {"B": 1, "M": 512, "N": 7168, "K": 32320}, "dtype": "BF16", "quantized": false, "memory_mb": 38.5625, "flops": 237229834240}}}}, "multi_batch": {"config": {"name": "multi_batch", "batch_size": 4, "seq_length": 128}, "operator_shapes": {"embedding": {"name": "embedding", "type": "lookup", "shape": {"B": 4, "M": 128, "N": 129280, "K": 7168}, "dtype": "BF16", "quantized": false, "memory_mb": 133.25, "flops": 0}, "attn_wqa": {"name": "attn_wqa", "type": "linear_int8", "shape": {"B": 4, "M": 128, "N": 1536, "K": 7168}, "dtype": "W8A8", "quantized": true, "memory_mb": 4.25, "flops": 11274289152}, "attn_wqb": {"name": "attn_wqb", "type": "linear_int8", "shape": {"B": 4, "M": 128, "N": 24576, "K": 1536}, "dtype": "W8A8", "quantized": true, "memory_mb": 12.75, "flops": 38654705664}, "attn_wkv_a": {"name": "attn_wkv_a", "type": "linear_int8", "shape": {"B": 4, "M": 128, "N": 576, "K": 7168}, "dtype": "W8A8", "quantized": true, "memory_mb": 3.78125, "flops": 4227858432}, "attn_wkv_b": {"name": "attn_wkv_b", "type": "linear_int8", "shape": {"B": 4, "M": 128, "N": 32768, "K": 512}, "dtype": "W8A8", "quantized": true, "memory_mb": 16.25, "flops": 17179869184}, "attn_qk": {"name": "attn_qk", "type": "bmm", "shape": {"B": 16384, "M": 1, "N": 129, "K": 192}, "dtype": "BF16", "quantized": false, "memory_mb": 10.03125, "flops": 811597824}, "attn_pv": {"name": "attn_pv", "type": "bmm", "shape": {"B": 16384, "M": 1, "N": 128, "K": 129}, "dtype": "BF16", "quantized": false, "memory_mb": 8.03125, "flops": 541065216}, "attn_o": {"name": "attn_o", "type": "linear_int8", "shape": {"B": 4, "M": 128, "N": 7168, "K": 16384}, "dtype": "W8A8", "quantized": true, "memory_mb": 11.5, "flops": 120259084288}, "dense_up": {"name": "dense_up", "type": "linear_int8", "shape": {"B": 4, "M": 128, "N": 4736, "K": 7168}, "dtype": "W8A8", "quantized": true, "memory_mb": 5.8125, "flops": 34762391552}, "dense_gate": {"name": "dense_gate", "type": "linear_int8", "shape": {"B": 4, "M": 128, "N": 4736, "K": 7168}, "dtype": "W8A8", "quantized": true, "memory_mb": 5.8125, "flops": 34762391552}, "dense_down": {"name": "dense_down", "type": "linear_int8", "shape": {"B": 4, "M": 128, "N": 7168, "K": 4736}, "dtype": "W8A8", "quantized": true, "memory_mb": 5.8125, "flops": 34762391552}, "moe_export_up": {"name": "moe_export_up", "type": "linear_int8", "shape": {"B": 4, "M": 128, "N": 2048, "K": 7168}, "dtype": "W8A8", "quantized": true, "memory_mb": 4.5, "flops": 15032385536}, "moe_export_gate": {"name": "moe_export_gate", "type": "linear_int8", "shape": {"B": 4, "M": 128, "N": 2048, "K": 7168}, "dtype": "W8A8", "quantized": true, "memory_mb": 4.5, "flops": 15032385536}, "moe_export_down": {"name": "moe_export_down", "type": "linear_int8", "shape": {"B": 4, "M": 128, "N": 7168, "K": 2048}, "dtype": "W8A8", "quantized": true, "memory_mb": 4.5, "flops": 15032385536}, "moe_gate": {"name": "moe_gate", "type": "linear", "shape": {"B": 4, "M": 128, "N": 256, "K": 7168}, "dtype": "BF16", "quantized": false, "memory_mb": 7.25, "flops": 1879048192}, "lm_head": {"name": "lm_head", "type": "linear", "shape": {"B": 4, "M": 128, "N": 7168, "K": 32320}, "dtype": "BF16", "quantized": false, "memory_mb": 38.5625, "flops": 237229834240}}, "analysis_report": {"operator_count": 16, "type_statistics": {"lookup": {"count": 1, "total_flops": 0, "total_memory_mb": 133.25, "quantized_count": 0, "operators": ["embedding"]}, "linear_int8": {"count": 11, "total_flops": 340980137984, "total_memory_mb": 79.46875, "quantized_count": 11, "operators": ["attn_wqa", "attn_wqb", "attn_wkv_a", "attn_wkv_b", "attn_o", "dense_up", "dense_gate", "dense_down", "moe_export_up", "moe_export_gate", "moe_export_down"]}, "bmm": {"count": 2, "total_flops": 1352663040, "total_memory_mb": 18.0625, "quantized_count": 0, "operators": ["attn_qk", "attn_pv"]}, "linear": {"count": 2, "total_flops": 239108882432, "total_memory_mb": 45.8125, "quantized_count": 0, "operators": ["moe_gate", "lm_head"]}}, "module_statistics": {"embedding": {"operator_count": 1, "total_flops": 0, "total_memory_mb": 133.25, "quantized_operators": 0, "quantization_ratio": 0.0}, "attention": {"operator_count": 7, "total_flops": 192948469760, "total_memory_mb": 66.59375, "quantized_operators": 5, "quantization_ratio": 0.7142857142857143}, "mlp": {"operator_count": 3, "total_flops": 104287174656, "total_memory_mb": 17.4375, "quantized_operators": 3, "quantization_ratio": 1.0}, "moe": {"operator_count": 4, "total_flops": 46976204800, "total_memory_mb": 20.75, "quantized_operators": 3, "quantization_ratio": 0.75}, "lm_head": {"operator_count": 1, "total_flops": 237229834240, "total_memory_mb": 38.5625, "quantized_operators": 0, "quantization_ratio": 0.0}}, "quantization_statistics": {"total_operators": 16, "quantized_operators": 11, "quantization_ratio": 0.6875, "dtype_distribution": {"BF16": 5, "W8A8": 11}, "flops_distribution": {"total_flops": 581441683456, "quantized_flops": 340980137984, "quantized_flops_ratio": 0.5864391007491353}}, "operator_details": {"embedding": {"name": "embedding", "type": "lookup", "shape": {"B": 4, "M": 128, "N": 129280, "K": 7168}, "dtype": "BF16", "quantized": false, "memory_mb": 133.25, "flops": 0}, "attn_wqa": {"name": "attn_wqa", "type": "linear_int8", "shape": {"B": 4, "M": 128, "N": 1536, "K": 7168}, "dtype": "W8A8", "quantized": true, "memory_mb": 4.25, "flops": 11274289152}, "attn_wqb": {"name": "attn_wqb", "type": "linear_int8", "shape": {"B": 4, "M": 128, "N": 24576, "K": 1536}, "dtype": "W8A8", "quantized": true, "memory_mb": 12.75, "flops": 38654705664}, "attn_wkv_a": {"name": "attn_wkv_a", "type": "linear_int8", "shape": {"B": 4, "M": 128, "N": 576, "K": 7168}, "dtype": "W8A8", "quantized": true, "memory_mb": 3.78125, "flops": 4227858432}, "attn_wkv_b": {"name": "attn_wkv_b", "type": "linear_int8", "shape": {"B": 4, "M": 128, "N": 32768, "K": 512}, "dtype": "W8A8", "quantized": true, "memory_mb": 16.25, "flops": 17179869184}, "attn_qk": {"name": "attn_qk", "type": "bmm", "shape": {"B": 16384, "M": 1, "N": 129, "K": 192}, "dtype": "BF16", "quantized": false, "memory_mb": 10.03125, "flops": 811597824}, "attn_pv": {"name": "attn_pv", "type": "bmm", "shape": {"B": 16384, "M": 1, "N": 128, "K": 129}, "dtype": "BF16", "quantized": false, "memory_mb": 8.03125, "flops": 541065216}, "attn_o": {"name": "attn_o", "type": "linear_int8", "shape": {"B": 4, "M": 128, "N": 7168, "K": 16384}, "dtype": "W8A8", "quantized": true, "memory_mb": 11.5, "flops": 120259084288}, "dense_up": {"name": "dense_up", "type": "linear_int8", "shape": {"B": 4, "M": 128, "N": 4736, "K": 7168}, "dtype": "W8A8", "quantized": true, "memory_mb": 5.8125, "flops": 34762391552}, "dense_gate": {"name": "dense_gate", "type": "linear_int8", "shape": {"B": 4, "M": 128, "N": 4736, "K": 7168}, "dtype": "W8A8", "quantized": true, "memory_mb": 5.8125, "flops": 34762391552}, "dense_down": {"name": "dense_down", "type": "linear_int8", "shape": {"B": 4, "M": 128, "N": 7168, "K": 4736}, "dtype": "W8A8", "quantized": true, "memory_mb": 5.8125, "flops": 34762391552}, "moe_export_up": {"name": "moe_export_up", "type": "linear_int8", "shape": {"B": 4, "M": 128, "N": 2048, "K": 7168}, "dtype": "W8A8", "quantized": true, "memory_mb": 4.5, "flops": 15032385536}, "moe_export_gate": {"name": "moe_export_gate", "type": "linear_int8", "shape": {"B": 4, "M": 128, "N": 2048, "K": 7168}, "dtype": "W8A8", "quantized": true, "memory_mb": 4.5, "flops": 15032385536}, "moe_export_down": {"name": "moe_export_down", "type": "linear_int8", "shape": {"B": 4, "M": 128, "N": 7168, "K": 2048}, "dtype": "W8A8", "quantized": true, "memory_mb": 4.5, "flops": 15032385536}, "moe_gate": {"name": "moe_gate", "type": "linear", "shape": {"B": 4, "M": 128, "N": 256, "K": 7168}, "dtype": "BF16", "quantized": false, "memory_mb": 7.25, "flops": 1879048192}, "lm_head": {"name": "lm_head", "type": "linear", "shape": {"B": 4, "M": 128, "N": 7168, "K": 32320}, "dtype": "BF16", "quantized": false, "memory_mb": 38.5625, "flops": 237229834240}}}}}