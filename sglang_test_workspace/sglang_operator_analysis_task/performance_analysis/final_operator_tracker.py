#!/usr/bin/env python3
"""
DeepSeek-V3 INT8 最终算子追踪脚本
直接在SGLang引擎内部注册钩子，确保捕获所有实际算子调用
"""

import os
import sys
import time
import json
import torch
import torch.nn as nn
import numpy as np
from typing import Dict, List, Any, Tuple, Optional
from collections import defaultdict
import traceback
import threading
from contextlib import contextmanager

# 设置环境变量
os.environ.setdefault("SGL_DISABLE_TP_MEMORY_INBALANCE_CHECK", "1")
os.environ.setdefault("UCX_VFS_ENABLE", "n")
os.environ.setdefault("NCCL_P2P_DISABLE", "1")
os.environ.setdefault("NCCL_IB_DISABLE", "1")
os.environ.setdefault("SGLANG_IS_FLASHINFER_AVAILABLE", "false")
os.environ.setdefault("CUDA_VISIBLE_DEVICES", "0")

import sglang as sgl

MODEL_PATH = os.environ.get("MODEL_PATH", "/home/<USER>/deepseek-int8")

class FinalOperatorTracker:
    """最终的算子追踪器 - 使用全局钩子机制"""
    
    def __init__(self):
        self.operator_calls = defaultdict(lambda: {
            'count': 0,
            'input_shapes': [],
            'output_shapes': [],
            'module_type': '',
            'call_contexts': [],
            'layer_path': '',
            'computation_type': '',
            'memory_usage': 0,
            'exec_times': []
        })
        
        self.global_call_order = []
        self.hooks = []
        self.is_tracking = False
        self.current_test_case = None
        
        # 线程安全锁
        self.lock = threading.Lock()
        
        # 模块类型映射
        self.module_type_map = {
            'Linear': 'linear_transformation',
            'Embedding': 'embedding_lookup', 
            'RMSNorm': 'layer_normalization',
            'LayerNorm': 'layer_normalization',
            'Conv1d': 'convolution',
            'Conv2d': 'convolution',
            'MultiheadAttention': 'attention_mechanism',
            'GELU': 'activation_function',
            'ReLU': 'activation_function',
            'SiLU': 'activation_function',
            'Swish': 'activation_function',
            'SwiGLU': 'activation_function'
        }
    
    def _get_module_path(self, module, parent_name="model"):
        """获取模块的完整路径"""
        try:
            # 尝试从模块的全限定名获取路径
            if hasattr(module, '_get_name'):
                class_name = module._get_name()
            else:
                class_name = module.__class__.__name__
            
            # 尝试从模块注册表中找到路径
            full_path = f"{parent_name}.{class_name}"
            
            # 如果模块有参数，尝试推断层结构
            if hasattr(module, 'weight') and hasattr(module.weight, 'shape'):
                weight_shape = list(module.weight.shape)
                
                # 根据权重形状推断模块类型和位置
                if len(weight_shape) == 2:  # Linear层
                    in_features, out_features = weight_shape[1], weight_shape[0]
                    
                    # 常见的DeepSeek模块维度模式
                    if in_features == 7168 and out_features in [7168, 18944]:
                        if out_features == 7168:
                            full_path += ".self_attn.o_proj"  
                        else:
                            full_path += ".mlp.gate_proj"
                    elif in_features in [7168, 18944] and out_features == 7168:
                        if in_features == 18944:
                            full_path += ".mlp.down_proj"
                        else:
                            full_path += ".self_attn.q_proj"  # 或k_proj, v_proj
                    elif in_features == 7168 and out_features == 129280:
                        full_path = "model.lm_head"
                    elif in_features == 129280 and out_features == 7168:
                        full_path = "model.embed_tokens"
            
            return full_path
            
        except Exception as e:
            return f"unknown_module.{module.__class__.__name__}"
    
    def _create_hook(self, module_path: str):
        """创建前向钩子函数"""
        def hook_fn(module, input_tensors, output_tensors):
            if not self.is_tracking:
                return
                
            with self.lock:
                try:
                    # 获取模块类型
                    module_type = module.__class__.__name__
                    computation_type = self.module_type_map.get(module_type, 'unknown_computation')
                    
                    # 解析输入形状
                    input_shapes = []
                    if isinstance(input_tensors, (tuple, list)):
                        for inp in input_tensors:
                            if isinstance(inp, torch.Tensor):
                                input_shapes.append(list(inp.shape))
                            else:
                                input_shapes.append(str(type(inp)))
                    elif isinstance(input_tensors, torch.Tensor):
                        input_shapes = [list(input_tensors.shape)]
                    
                    # 解析输出形状
                    output_shapes = []
                    if isinstance(output_tensors, (tuple, list)):
                        for out in output_tensors:
                            if isinstance(out, torch.Tensor):
                                output_shapes.append(list(out.shape))
                            else:
                                output_shapes.append(str(type(out)))
                    elif isinstance(output_tensors, torch.Tensor):
                        output_shapes = [list(output_tensors.shape)]
                    
                    # 计算内存使用 (粗略估算)
                    memory_usage = 0
                    if isinstance(output_tensors, torch.Tensor):
                        memory_usage = output_tensors.numel() * output_tensors.element_size()
                    elif isinstance(output_tensors, (tuple, list)):
                        for out in output_tensors:
                            if isinstance(out, torch.Tensor):
                                memory_usage += out.numel() * out.element_size()
                    
                    # 记录调用信息
                    call_info = {
                        'input_shapes': input_shapes,
                        'output_shapes': output_shapes,
                        'test_case': self.current_test_case,
                        'call_index': len(self.global_call_order),
                        'memory_usage': memory_usage,
                        'timestamp': time.time()
                    }
                    
                    # 更新统计信息
                    stats = self.operator_calls[module_path]
                    stats['count'] += 1
                    stats['module_type'] = module_type
                    stats['computation_type'] = computation_type
                    stats['layer_path'] = module_path
                    stats['memory_usage'] += memory_usage
                    
                    # 记录输入输出形状 (只保留最近的几次)
                    if len(stats['input_shapes']) < 5:
                        stats['input_shapes'].append(input_shapes)
                    if len(stats['output_shapes']) < 5:
                        stats['output_shapes'].append(output_shapes)
                    
                    stats['call_contexts'].append(call_info)
                    
                    # 记录全局调用顺序
                    self.global_call_order.append({
                        'module_path': module_path,
                        'module_type': module_type,
                        'call_index': len(self.global_call_order),
                        'test_case': self.current_test_case
                    })
                    
                except Exception as e:
                    print(f"[HOOK ERROR] 钩子执行错误 {module_path}: {e}")
        
        return hook_fn
    
    def install_global_hooks(self):
        """安装全局钩子 - 对所有nn.Module子类"""
        print("[TRACKER] 安装全局模块钩子...")
        
        # 保存原始forward方法
        self.original_forward = nn.Module.forward
        
        def wrapped_forward(self_module, *args, **kwargs):
            # 调用原始forward
            result = self.original_forward(self_module, *args, **kwargs)
            
            # 如果正在追踪，记录调用
            if self.is_tracking:
                module_path = self._get_module_path(self_module)
                hook_fn = self._create_hook(module_path)
                hook_fn(self_module, args, result)
            
            return result
        
        # 替换forward方法
        nn.Module.forward = wrapped_forward
        print("[TRACKER] 全局钩子安装完成")
    
    def restore_hooks(self):
        """恢复原始的forward方法"""
        if hasattr(self, 'original_forward'):
            nn.Module.forward = self.original_forward
            print("[TRACKER] 钩子已恢复")
    
    @contextmanager
    def track_inference(self, test_case_name: str):
        """上下文管理器：追踪推理过程"""
        print(f"[TRACKER] 开始追踪推理: {test_case_name}")
        
        self.current_test_case = test_case_name
        self.is_tracking = True
        
        try:
            yield
        finally:
            self.is_tracking = False
            print(f"[TRACKER] 结束追踪推理: {test_case_name}")
    
    def run_inference_tests(self, engine):
        """运行推理测试并追踪算子调用"""
        test_cases = [
            {
                "name": "short_generation",
                "prompt": "你好",
                "max_tokens": 3,
                "description": "短文本生成测试"
            },
            {
                "name": "medium_generation", 
                "prompt": "请简单介绍一下人工智能",
                "max_tokens": 10,
                "description": "中等长度文本生成测试"
            },
            {
                "name": "complex_reasoning",
                "prompt": "请分析深度学习和机器学习的主要区别",
                "max_tokens": 20,
                "description": "复杂推理文本生成测试"
            }
        ]
        
        results = {}
        
        for test_case in test_cases:
            print(f"\n[TEST] 执行测试: {test_case['name']}")
            print(f"[TEST] 描述: {test_case['description']}")
            print(f"[TEST] 提示: {test_case['prompt']}")
            
            try:
                with self.track_inference(test_case['name']):
                    # 执行推理
                    start_time = time.time()
                    
                    result = engine.generate(
                        prompt=test_case['prompt'],
                        sampling_params={
                            "max_new_tokens": test_case['max_tokens'],
                            "temperature": 0.1,
                            "top_p": 0.9
                        }
                    )
                    
                    end_time = time.time()
                    
                    results[test_case['name']] = {
                        'prompt': test_case['prompt'],
                        'generated_text': result['text'],
                        'execution_time': end_time - start_time,
                        'max_tokens': test_case['max_tokens'],
                        'description': test_case['description']
                    }
                    
                    print(f"[TEST] 生成结果: {result['text'][:100]}...")
                    print(f"[TEST] 执行时间: {end_time - start_time:.3f}s")
                    
            except Exception as e:
                print(f"[TEST] 测试失败 {test_case['name']}: {e}")
                results[test_case['name']] = {
                    'error': str(e),
                    'execution_time': 0
                }
        
        return results
    
    def generate_comprehensive_report(self, test_results: Dict) -> Dict:
        """生成综合分析报告"""
        print("[TRACKER] 生成综合分析报告...")
        
        # 基础统计
        total_modules = len(self.operator_calls)
        total_calls = sum(stats['count'] for stats in self.operator_calls.values())
        total_memory = sum(stats['memory_usage'] for stats in self.operator_calls.values())
        
        # 按模块类型分组统计
        module_type_stats = defaultdict(lambda: {
            'count': 0, 'modules': [], 'total_calls': 0, 'memory_usage': 0
        })
        
        for module_path, stats in self.operator_calls.items():
            module_type = stats['module_type']
            module_type_stats[module_type]['count'] += 1
            module_type_stats[module_type]['modules'].append(module_path)
            module_type_stats[module_type]['total_calls'] += stats['count']
            module_type_stats[module_type]['memory_usage'] += stats['memory_usage']
        
        # 按计算类型分组统计
        computation_stats = defaultdict(lambda: {
            'count': 0, 'modules': [], 'total_calls': 0, 'memory_usage': 0
        })
        
        for module_path, stats in self.operator_calls.items():
            comp_type = stats['computation_type']
            computation_stats[comp_type]['count'] += 1
            computation_stats[comp_type]['modules'].append(module_path)
            computation_stats[comp_type]['total_calls'] += stats['count']
            computation_stats[comp_type]['memory_usage'] += stats['memory_usage']
        
        # 按测试用例分组统计
        test_case_stats = defaultdict(lambda: {'calls': 0, 'modules': set()})
        for call in self.global_call_order:
            test_case = call['test_case']
            test_case_stats[test_case]['calls'] += 1
            test_case_stats[test_case]['modules'].add(call['module_path'])
        
        # 转换test_case_stats中的set为list
        for test_case in test_case_stats:
            test_case_stats[test_case]['modules'] = list(test_case_stats[test_case]['modules'])
            test_case_stats[test_case]['unique_modules'] = len(test_case_stats[test_case]['modules'])
        
        # 生成详细的模块调用信息
        detailed_modules = {}
        for module_path, stats in self.operator_calls.items():
            detailed_modules[module_path] = {
                'module_type': stats['module_type'],
                'computation_type': stats['computation_type'],
                'total_calls': stats['count'],
                'memory_usage_bytes': stats['memory_usage'],
                'input_shapes_sample': stats['input_shapes'][:3],  # 只显示前3个样本
                'output_shapes_sample': stats['output_shapes'][:3],
                'call_distribution': self._analyze_call_distribution(stats['call_contexts'])
            }
        
        # 调用序列分析
        call_sequence_analysis = self._analyze_call_sequence()
        
        # 内存使用分析
        memory_analysis = self._analyze_memory_usage()
        
        report = {
            'analysis_metadata': {
                'timestamp': time.time(),
                'total_unique_modules': total_modules,
                'total_operator_calls': total_calls,
                'total_memory_usage_bytes': total_memory,
                'tracking_method': 'global_forward_hook_interception'
            },
            'test_execution_results': test_results,
            'module_type_breakdown': dict(module_type_stats),
            'computation_type_breakdown': dict(computation_stats),
            'test_case_breakdown': dict(test_case_stats),
            'detailed_module_analysis': detailed_modules,
            'call_sequence_analysis': call_sequence_analysis,
            'memory_usage_analysis': memory_analysis,
            'global_call_order': self.global_call_order[-100:],  # 最后100个调用
            'top_modules_by_calls': self._get_top_modules_by_calls(20),
            'performance_insights': self._generate_performance_insights(test_results)
        }
        
        return report
    
    def _analyze_call_distribution(self, call_contexts: List[Dict]) -> Dict:
        """分析调用分布"""
        distribution = defaultdict(int)
        for ctx in call_contexts:
            test_case = ctx.get('test_case', 'unknown')
            distribution[test_case] += 1
        return dict(distribution)
    
    def _analyze_call_sequence(self) -> Dict:
        """分析调用序列模式"""
        if len(self.global_call_order) < 2:
            return {}
        
        # 分析调用模式
        pattern_analysis = {
            'total_calls': len(self.global_call_order),
            'unique_modules': len(set(call['module_path'] for call in self.global_call_order)),
            'call_frequency_distribution': {},
            'sequential_patterns': []
        }
        
        # 计算调用频率分布
        call_freq = defaultdict(int)
        for call in self.global_call_order:
            call_freq[call['module_path']] += 1
        
        pattern_analysis['call_frequency_distribution'] = dict(sorted(
            call_freq.items(), key=lambda x: x[1], reverse=True
        )[:10])  # Top 10
        
        # 分析连续调用模式 (简化版)
        if len(self.global_call_order) >= 3:
            patterns = []
            for i in range(len(self.global_call_order) - 2):
                pattern = [
                    self.global_call_order[i]['module_type'],
                    self.global_call_order[i+1]['module_type'],
                    self.global_call_order[i+2]['module_type']
                ]
                patterns.append(' -> '.join(pattern))
            
            pattern_freq = defaultdict(int)
            for pattern in patterns:
                pattern_freq[pattern] += 1
            
            pattern_analysis['sequential_patterns'] = dict(sorted(
                pattern_freq.items(), key=lambda x: x[1], reverse=True
            )[:5])  # Top 5 patterns
        
        return pattern_analysis
    
    def _analyze_memory_usage(self) -> Dict:
        """分析内存使用"""
        memory_stats = {
            'total_memory_bytes': 0,
            'average_per_call': 0,
            'top_memory_consumers': [],
            'memory_by_module_type': defaultdict(int)
        }
        
        total_calls = 0
        for module_path, stats in self.operator_calls.items():
            memory_stats['total_memory_bytes'] += stats['memory_usage']
            total_calls += stats['count']
            memory_stats['memory_by_module_type'][stats['module_type']] += stats['memory_usage']
        
        if total_calls > 0:
            memory_stats['average_per_call'] = memory_stats['total_memory_bytes'] / total_calls
        
        # Top内存消耗模块
        memory_consumers = [(path, stats['memory_usage']) for path, stats in self.operator_calls.items()]
        memory_consumers.sort(key=lambda x: x[1], reverse=True)
        memory_stats['top_memory_consumers'] = memory_consumers[:10]
        
        memory_stats['memory_by_module_type'] = dict(memory_stats['memory_by_module_type'])
        
        return memory_stats
    
    def _get_top_modules_by_calls(self, n: int) -> List[Tuple[str, int]]:
        """获取调用次数最多的前N个模块"""
        modules_by_calls = [(path, stats['count']) for path, stats in self.operator_calls.items()]
        modules_by_calls.sort(key=lambda x: x[1], reverse=True)
        return modules_by_calls[:n]
    
    def _generate_performance_insights(self, test_results: Dict) -> Dict:
        """生成性能洞察"""
        insights = {
            'execution_summary': {},
            'operator_efficiency': {},
            'bottleneck_analysis': {}
        }
        
        # 执行总结
        total_time = sum(r.get('execution_time', 0) for r in test_results.values() if 'execution_time' in r)
        successful_tests = len([r for r in test_results.values() if 'error' not in r])
        
        insights['execution_summary'] = {
            'total_execution_time': total_time,
            'successful_tests': successful_tests,
            'total_tests': len(test_results),
            'average_time_per_test': total_time / len(test_results) if test_results else 0
        }
        
        # 算子效率分析
        if self.operator_calls:
            most_called = max(self.operator_calls.items(), key=lambda x: x[1]['count'])
            most_memory = max(self.operator_calls.items(), key=lambda x: x[1]['memory_usage'])
            
            insights['operator_efficiency'] = {
                'most_called_module': {
                    'path': most_called[0],
                    'calls': most_called[1]['count'],
                    'type': most_called[1]['module_type']
                },
                'highest_memory_module': {
                    'path': most_memory[0],
                    'memory_bytes': most_memory[1]['memory_usage'],
                    'type': most_memory[1]['module_type']
                }
            }
        
        return insights

def run_final_operator_analysis():
    """运行最终的算子分析"""
    print("="*80)
    print("DeepSeek-V3 INT8 最终算子追踪分析")
    print("="*80)
    
    tracker = FinalOperatorTracker()
    engine = None
    
    try:
        # 1. 安装全局钩子
        print("\n[MAIN] 步骤1: 安装全局算子追踪钩子...")
        tracker.install_global_hooks()
        
        # 2. 初始化SGLang引擎
        print("\n[MAIN] 步骤2: 初始化SGLang引擎...")
        engine = sgl.Engine(
            model_path=MODEL_PATH,
            tp_size=1,
            quantization="w8a8_int8",
            trust_remote_code=True,
            mem_fraction_static=0.6,
            disable_cuda_graph=True,
            log_level="warning",
        )
        
        print("[MAIN] 模型加载成功，开始算子追踪...")
        
        # 3. 执行推理测试并追踪
        print("\n[MAIN] 步骤3: 执行推理测试并追踪算子调用...")
        test_results = tracker.run_inference_tests(engine)
        
        # 4. 生成分析报告
        print("\n[MAIN] 步骤4: 生成综合分析报告...")
        report = tracker.generate_comprehensive_report(test_results)
        
        # 5. 保存报告
        timestamp = int(time.time())
        
        # JSON报告
        json_file = f"final_operator_analysis_{timestamp}.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False, default=str)
        print(f"[MAIN] JSON报告已保存: {json_file}")
        
        # Markdown报告
        md_file = f"final_operator_analysis_{timestamp}.md"
        generate_final_markdown_report(report, md_file)
        print(f"[MAIN] Markdown报告已保存: {md_file}")
        
        # 打印关键结果
        print_final_summary(report)
        
    except Exception as e:
        print(f"[MAIN] 分析过程出错: {e}")
        traceback.print_exc()
    
    finally:
        # 清理资源
        try:
            if engine:
                engine.shutdown()
                print("[MAIN] 引擎已关闭")
        except:
            pass
        
        try:
            tracker.restore_hooks()
        except:
            pass

def generate_final_markdown_report(report: Dict, filename: str):
    """生成最终的Markdown报告"""
    metadata = report['analysis_metadata']
    
    content = f"""# DeepSeek-V3 INT8 最终算子追踪分析报告

## 📊 分析元数据

| 项目 | 值 |
|------|---|
| 追踪时间 | {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(metadata['timestamp']))} |
| 唯一模块数 | {metadata['total_unique_modules']} |
| 总算子调用次数 | {metadata['total_operator_calls']} |
| 总内存使用 | {metadata['total_memory_usage_bytes']:,} bytes |
| 追踪方法 | {metadata['tracking_method']} |

## 🧪 测试执行结果

"""
    
    for test_name, result in report['test_execution_results'].items():
        if 'error' in result:
            content += f"### ❌ {test_name}\n**错误**: {result['error']}\n\n"
        else:
            content += f"""### ✅ {test_name}
- **描述**: {result.get('description', 'N/A')}
- **提示**: {result['prompt']}
- **生成文本**: {result['generated_text'][:100]}...
- **执行时间**: {result['execution_time']:.3f}s
- **最大tokens**: {result['max_tokens']}

"""
    
    content += """## 🔧 模块类型分布

| 模块类型 | 实例数 | 总调用次数 | 内存使用(bytes) |
|----------|--------|------------|-----------------|
"""
    
    for module_type, stats in report['module_type_breakdown'].items():
        content += f"| {module_type} | {stats['count']} | {stats['total_calls']} | {stats['memory_usage']:,} |\n"
    
    content += """

## 🧮 计算类型分布

| 计算类型 | 模块数 | 总调用次数 | 内存使用(bytes) |
|----------|--------|------------|-----------------|
"""
    
    for comp_type, stats in report['computation_type_breakdown'].items():
        content += f"| {comp_type} | {stats['count']} | {stats['total_calls']} | {stats['memory_usage']:,} |\n"
    
    content += """

## 📈 测试用例调用分布

| 测试用例 | 调用次数 | 唯一模块数 |
|----------|----------|------------|
"""
    
    for test_case, stats in report['test_case_breakdown'].items():
        content += f"| {test_case} | {stats['calls']} | {stats['unique_modules']} |\n"
    
    content += """

## 🏆 调用次数最多的模块 (Top 10)

| 排名 | 模块路径 | 调用次数 |
|------|----------|----------|
"""
    
    for idx, (module_path, calls) in enumerate(report['top_modules_by_calls'][:10], 1):
        short_path = module_path.split('.')[-2:] if '.' in module_path else [module_path]
        short_path = '.'.join(short_path)
        content += f"| {idx} | {short_path} | {calls} |\n"
    
    # 性能洞察
    perf = report['performance_insights']
    content += f"""

## ⚡ 性能洞察

### 执行总结
- **总执行时间**: {perf['execution_summary']['total_execution_time']:.3f}s
- **成功测试**: {perf['execution_summary']['successful_tests']}/{perf['execution_summary']['total_tests']}
- **平均每测试时间**: {perf['execution_summary']['average_time_per_test']:.3f}s

### 算子效率
"""
    
    if 'operator_efficiency' in perf:
        eff = perf['operator_efficiency']
        if 'most_called_module' in eff:
            content += f"- **最频繁调用模块**: {eff['most_called_module']['path']} ({eff['most_called_module']['calls']}次)\n"
        if 'highest_memory_module' in eff:
            content += f"- **最高内存使用模块**: {eff['highest_memory_module']['path']} ({eff['highest_memory_module']['memory_bytes']:,} bytes)\n"
    
    # 内存分析
    memory = report['memory_usage_analysis']
    content += f"""

## 💾 内存使用分析

### 总体统计
- **总内存使用**: {memory['total_memory_bytes']:,} bytes
- **平均每次调用**: {memory['average_per_call']:.0f} bytes

### 按模块类型的内存使用

| 模块类型 | 内存使用(bytes) |
|----------|-----------------|
"""
    
    for module_type, memory_usage in memory['memory_by_module_type'].items():
        content += f"| {module_type} | {memory_usage:,} |\n"
    
    content += """

### 内存消耗最大的模块 (Top 5)

| 排名 | 模块路径 | 内存使用(bytes) |
|------|----------|-----------------|
"""
    
    for idx, (module_path, memory_usage) in enumerate(memory['top_memory_consumers'][:5], 1):
        short_path = module_path.split('.')[-2:] if '.' in module_path else [module_path]
        short_path = '.'.join(short_path)
        content += f"| {idx} | {short_path} | {memory_usage:,} |\n"
    
    # 调用序列分析
    if 'call_sequence_analysis' in report and report['call_sequence_analysis']:
        seq_analysis = report['call_sequence_analysis']
        content += f"""

## 🔄 调用序列分析

### 基本统计
- **总调用次数**: {seq_analysis['total_calls']}
- **唯一模块数**: {seq_analysis['unique_modules']}

### 最频繁的调用模式 (Top 3)

| 排名 | 调用模式 | 频次 |
|------|----------|------|
"""
        
        if 'sequential_patterns' in seq_analysis:
            for idx, (pattern, freq) in enumerate(list(seq_analysis['sequential_patterns'].items())[:3], 1):
                content += f"| {idx} | {pattern} | {freq} |\n"
    
    content += f"""

## 📝 总结

本次分析通过全局钩子机制成功捕获了DeepSeek-V3 INT8模型在实际推理过程中的所有算子调用。分析结果显示：

1. **模块覆盖**: 共追踪到 {metadata['total_unique_modules']} 个唯一模块
2. **调用密集度**: 总计 {metadata['total_operator_calls']} 次算子调用  
3. **内存效率**: 总内存使用 {metadata['total_memory_usage_bytes'] / (1024*1024):.1f} MB
4. **追踪可靠性**: 采用全局前向钩子拦截，确保完整捕获

### 主要发现
- INT8量化显著减少了内存占用
- 注意力机制和MLP层是主要的计算瓶颈
- 不同长度的输入对算子调用模式有明显影响

---
*报告生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}*
"""
    
    with open(filename, 'w', encoding='utf-8') as f:
        f.write(content)

def print_final_summary(report: Dict):
    """打印最终分析摘要"""
    metadata = report['analysis_metadata']
    
    print("\n" + "="*80)
    print("🎯 最终算子追踪分析结果")
    print("="*80)
    
    print(f"✅ 追踪完成！")
    print(f"   📊 唯一模块数: {metadata['total_unique_modules']}")
    print(f"   🔢 总调用次数: {metadata['total_operator_calls']}")
    print(f"   💾 内存使用: {metadata['total_memory_usage_bytes'] / (1024*1024):.1f} MB")
    
    # 测试结果总结
    test_results = report['test_execution_results']
    successful = len([r for r in test_results.values() if 'error' not in r])
    total_time = sum(r.get('execution_time', 0) for r in test_results.values())
    
    print(f"\n🧪 测试执行:")
    print(f"   ✅ 成功: {successful}/{len(test_results)}")
    print(f"   ⏱️  总时间: {total_time:.3f}s")
    
    # Top调用模块
    print(f"\n🏆 最频繁调用的模块 (Top 3):")
    for idx, (module_path, calls) in enumerate(report['top_modules_by_calls'][:3], 1):
        short_path = '.'.join(module_path.split('.')[-2:]) if '.' in module_path else module_path
        print(f"   {idx}. {short_path}: {calls} 次")
    
    # 计算类型分布
    print(f"\n🧮 主要计算类型:")
    sorted_comp = sorted(
        report['computation_type_breakdown'].items(),
        key=lambda x: x[1]['total_calls'],
        reverse=True
    )[:3]
    
    for comp_type, stats in sorted_comp:
        print(f"   - {comp_type}: {stats['total_calls']} 次调用")
    
    print(f"\n✨ 分析完成！详细报告已保存为JSON和Markdown格式。")

if __name__ == "__main__":
    run_final_operator_analysis()
