#!/usr/bin/env python3
"""
SGLang INT8模型算子Shape详细统计分析
基于实际运行数据进行更精确的算子分析
"""

import json
import numpy as np
from typing import Dict, List, Tuple, Any

def analyze_operator_shapes():
    """分析算子的详细Shape信息"""
    
    # 基于实际运行得到的模型配置
    model_config = {
        "hidden_size": 7168,
        "intermediate_size": 18944,
        "num_layers": 61,
        "num_attention_heads": 56,
        "num_key_value_heads": 8,
        "head_dim": 128,
        "vocab_size": 129280,
        "tp_size": 4,
        "quantization": "w8a8_int8"
    }
    
    # 实际测试用例的序列长度
    test_cases = [
        {"name": "short", "seq_len": 32, "batch_size": 1},
        {"name": "medium", "seq_len": 128, "batch_size": 1}, 
        {"name": "long", "seq_len": 512, "batch_size": 1},
        {"name": "batch", "seq_len": 128, "batch_size": 4}
    ]
    
    operator_analysis = {}
    
    for case in test_cases:
        case_name = case["name"]
        B = case["batch_size"]
        S = case["seq_len"]
        
        # 计算每个算子的详细信息
        operators = {}
        
        # 1. Embedding层
        operators["embedding"] = {
            "input_shape": [B, S],
            "output_shape": [B, S, model_config["hidden_size"]],
            "dtype": "BF16",
            "memory_mb": B * S * model_config["hidden_size"] * 2 / (1024*1024),
            "flops": 0,  # lookup操作，没有浮点运算
            "quantized": False
        }
        
        # 2. 61层Decoder Layer
        for layer_id in range(model_config["num_layers"]):
            layer_prefix = f"layer_{layer_id}"
            
            # 2.1 Input LayerNorm
            operators[f"{layer_prefix}_input_norm"] = {
                "input_shape": [B, S, model_config["hidden_size"]],
                "output_shape": [B, S, model_config["hidden_size"]],
                "dtype": "BF16",
                "memory_mb": B * S * model_config["hidden_size"] * 2 / (1024*1024),
                "flops": B * S * model_config["hidden_size"] * 5,  # RMSNorm: sqrt, div, mul
                "quantized": False
            }
            
            # 2.2 QKV Projection
            qkv_out_dim = (model_config["num_attention_heads"] + 2 * model_config["num_key_value_heads"]) * model_config["head_dim"]
            qkv_out_dim_per_gpu = qkv_out_dim // model_config["tp_size"]
            
            operators[f"{layer_prefix}_qkv_proj"] = {
                "input_shape": [B, S, model_config["hidden_size"]],
                "output_shape": [B, S, qkv_out_dim_per_gpu],
                "weight_shape": [model_config["hidden_size"], qkv_out_dim_per_gpu],
                "dtype_input": "BF16",
                "dtype_weight": "INT8", 
                "dtype_output": "BF16",
                "memory_mb": B * S * qkv_out_dim_per_gpu * 2 / (1024*1024),
                "flops": 2 * B * S * model_config["hidden_size"] * qkv_out_dim_per_gpu,
                "quantized": True,
                "quantization_overhead": "dynamic activation quantization"
            }
            
            # 2.3 RoPE
            operators[f"{layer_prefix}_rope"] = {
                "input_shapes": {
                    "q": [B, S, model_config["num_attention_heads"] * model_config["head_dim"] // model_config["tp_size"]],
                    "k": [B, S, model_config["num_key_value_heads"] * model_config["head_dim"] // model_config["tp_size"]]
                },
                "output_shapes": {
                    "q": [B, S, model_config["num_attention_heads"] * model_config["head_dim"] // model_config["tp_size"]],
                    "k": [B, S, model_config["num_key_value_heads"] * model_config["head_dim"] // model_config["tp_size"]]
                },
                "dtype": "BF16",
                "flops": B * S * (model_config["num_attention_heads"] + model_config["num_key_value_heads"]) * model_config["head_dim"] * 10 // model_config["tp_size"],
                "quantized": False
            }
            
            # 2.4 Attention Computation
            operators[f"{layer_prefix}_attention"] = {
                "q_shape": [B, S, model_config["num_attention_heads"] * model_config["head_dim"] // model_config["tp_size"]],
                "k_shape": [B, S, model_config["num_key_value_heads"] * model_config["head_dim"] // model_config["tp_size"]],
                "v_shape": [B, S, model_config["num_key_value_heads"] * model_config["head_dim"] // model_config["tp_size"]],
                "attention_weights_shape": [B, model_config["num_attention_heads"] // model_config["tp_size"], S, S],
                "output_shape": [B, S, model_config["num_attention_heads"] * model_config["head_dim"] // model_config["tp_size"]],
                "dtype": "BF16",
                "backend": "triton",
                "memory_mb": B * (model_config["num_attention_heads"] // model_config["tp_size"]) * S * S * 2 / (1024*1024),
                "flops": 2 * B * (model_config["num_attention_heads"] // model_config["tp_size"]) * S * S * model_config["head_dim"],
                "kv_cache_update": True,
                "quantized": False
            }
            
            # 2.5 Attention Output Projection
            operators[f"{layer_prefix}_attn_out_proj"] = {
                "input_shape": [B, S, model_config["num_attention_heads"] * model_config["head_dim"] // model_config["tp_size"]],
                "output_shape": [B, S, model_config["hidden_size"]],
                "weight_shape": [model_config["num_attention_heads"] * model_config["head_dim"] // model_config["tp_size"], model_config["hidden_size"]],
                "dtype_input": "BF16",
                "dtype_weight": "INT8",
                "dtype_output": "BF16",
                "memory_mb": B * S * model_config["hidden_size"] * 2 / (1024*1024),
                "flops": 2 * B * S * (model_config["num_attention_heads"] * model_config["head_dim"] // model_config["tp_size"]) * model_config["hidden_size"],
                "quantized": True,
                "all_reduce": True
            }
            
            # 2.6 Post Attention LayerNorm
            operators[f"{layer_prefix}_post_attn_norm"] = {
                "input_shape": [B, S, model_config["hidden_size"]],
                "output_shape": [B, S, model_config["hidden_size"]],
                "dtype": "BF16",
                "memory_mb": B * S * model_config["hidden_size"] * 2 / (1024*1024),
                "flops": B * S * model_config["hidden_size"] * 5,
                "quantized": False
            }
            
            # 2.7 MLP/MoE (简化为MLP分析)
            # Gate + Up Projection
            mlp_intermediate_per_gpu = model_config["intermediate_size"] // model_config["tp_size"]
            
            operators[f"{layer_prefix}_mlp_gate_up"] = {
                "input_shape": [B, S, model_config["hidden_size"]],
                "output_shape": [B, S, 2 * mlp_intermediate_per_gpu],
                "weight_shape": [model_config["hidden_size"], 2 * mlp_intermediate_per_gpu],
                "dtype_input": "BF16",
                "dtype_weight": "INT8",
                "dtype_output": "BF16", 
                "memory_mb": B * S * 2 * mlp_intermediate_per_gpu * 2 / (1024*1024),
                "flops": 2 * B * S * model_config["hidden_size"] * 2 * mlp_intermediate_per_gpu,
                "quantized": True,
                "operation": "merged_gate_up_projection"
            }
            
            # SiLU + Mul Activation
            operators[f"{layer_prefix}_mlp_activation"] = {
                "input_shape": [B, S, 2 * mlp_intermediate_per_gpu],
                "output_shape": [B, S, mlp_intermediate_per_gpu],
                "dtype": "BF16",
                "memory_mb": B * S * mlp_intermediate_per_gpu * 2 / (1024*1024),
                "flops": B * S * mlp_intermediate_per_gpu * 8,  # SiLU + Mul
                "quantized": False,
                "operation": "silu_and_mul"
            }
            
            # Down Projection
            operators[f"{layer_prefix}_mlp_down"] = {
                "input_shape": [B, S, mlp_intermediate_per_gpu],
                "output_shape": [B, S, model_config["hidden_size"]],
                "weight_shape": [mlp_intermediate_per_gpu, model_config["hidden_size"]],
                "dtype_input": "BF16",
                "dtype_weight": "INT8",
                "dtype_output": "BF16",
                "memory_mb": B * S * model_config["hidden_size"] * 2 / (1024*1024),
                "flops": 2 * B * S * mlp_intermediate_per_gpu * model_config["hidden_size"],
                "quantized": True,
                "all_reduce": True,
                "operation": "down_projection"
            }
        
        # 3. Final LayerNorm
        operators["final_norm"] = {
            "input_shape": [B, S, model_config["hidden_size"]],
            "output_shape": [B, S, model_config["hidden_size"]],
            "dtype": "BF16",
            "memory_mb": B * S * model_config["hidden_size"] * 2 / (1024*1024),
            "flops": B * S * model_config["hidden_size"] * 5,
            "quantized": False
        }
        
        # 4. LM Head
        vocab_size_per_gpu = model_config["vocab_size"] // model_config["tp_size"]
        operators["lm_head"] = {
            "input_shape": [B, S, model_config["hidden_size"]],
            "output_shape": [B, S, vocab_size_per_gpu],
            "weight_shape": [model_config["hidden_size"], vocab_size_per_gpu],
            "dtype_input": "BF16",
            "dtype_weight": "BF16",  # LM Head通常不量化
            "dtype_output": "BF16",
            "memory_mb": B * S * vocab_size_per_gpu * 2 / (1024*1024),
            "flops": 2 * B * S * model_config["hidden_size"] * vocab_size_per_gpu,
            "quantized": False
        }
        
        operator_analysis[case_name] = {
            "case_info": case,
            "operators": operators,
            "summary": calculate_case_summary(operators)
        }
    
    return operator_analysis

def calculate_case_summary(operators: Dict) -> Dict:
    """计算单个测试用例的汇总信息"""
    total_memory = 0
    total_flops = 0
    quantized_ops = 0
    total_ops = 0
    
    for op_name, op_info in operators.items():
        if "memory_mb" in op_info:
            total_memory += op_info["memory_mb"]
        if "flops" in op_info:
            total_flops += op_info["flops"]
        if op_info.get("quantized", False):
            quantized_ops += 1
        total_ops += 1
    
    return {
        "total_memory_mb": total_memory,
        "total_memory_gb": total_memory / 1024,
        "total_flops": total_flops,
        "total_gflops": total_flops / 1e9,
        "quantized_operators": quantized_ops,
        "total_operators": total_ops,
        "quantization_ratio": quantized_ops / total_ops
    }

def analyze_scaling_patterns():
    """分析Shape和性能的扩展模式"""
    
    # 测试不同的batch size和sequence length
    batch_sizes = [1, 2, 4, 8, 16]
    seq_lengths = [32, 64, 128, 256, 512, 1024, 2048]
    
    scaling_analysis = {
        "memory_scaling": {},
        "compute_scaling": {},
        "recommendations": {}
    }
    
    # 固定hidden_size等参数
    hidden_size = 7168
    intermediate_size = 18944
    num_layers = 61
    
    for B in batch_sizes:
        for S in seq_lengths:
            case_key = f"B{B}_S{S}"
            
            # 计算关键算子的内存和计算需求
            memory_breakdown = {
                "embedding": B * S * hidden_size * 2 / (1024**3),  # GB
                "attention_qkv": B * S * 9216 * 2 / (1024**3),  # per layer
                "attention_weights": B * 14 * S * S * 2 / (1024**3),  # per layer, 56/4=14 heads per GPU
                "mlp_intermediate": B * S * (intermediate_size // 2) * 2 / (1024**3),  # per layer, per GPU
                "kv_cache_per_layer": 2 * B * 2 * S * 128 * 2 / (1024**3),  # 2*kv, 8/4=2 heads per GPU
                "lm_head": B * S * (129280 // 4) * 2 / (1024**3)
            }
            
            compute_breakdown = {
                "attention_qkv_per_layer": 2 * B * S * hidden_size * (9216 // 4),  # FLOPS per layer
                "attention_compute_per_layer": 2 * B * 14 * S * S * 128,  # FLOPS per layer
                "mlp_gate_up_per_layer": 2 * B * S * hidden_size * (2 * intermediate_size // 4),  # FLOPS per layer
                "mlp_down_per_layer": 2 * B * S * (intermediate_size // 4) * hidden_size,  # FLOPS per layer
                "lm_head": 2 * B * S * hidden_size * (129280 // 4)
            }
            
            # 计算总量 (所有层)
            total_memory = (
                memory_breakdown["embedding"] +
                (memory_breakdown["attention_qkv"] + 
                 memory_breakdown["attention_weights"] + 
                 memory_breakdown["mlp_intermediate"] + 
                 memory_breakdown["kv_cache_per_layer"]) * num_layers +
                memory_breakdown["lm_head"]
            )
            
            total_compute = (
                (compute_breakdown["attention_qkv_per_layer"] +
                 compute_breakdown["attention_compute_per_layer"] +
                 compute_breakdown["mlp_gate_up_per_layer"] +
                 compute_breakdown["mlp_down_per_layer"]) * num_layers +
                compute_breakdown["lm_head"]
            )
            
            scaling_analysis["memory_scaling"][case_key] = {
                "total_memory_gb": total_memory,
                "breakdown": memory_breakdown,
                "dominant_component": max(memory_breakdown, key=memory_breakdown.get)
            }
            
            scaling_analysis["compute_scaling"][case_key] = {
                "total_gflops": total_compute / 1e9,
                "breakdown": {k: v/1e9 for k, v in compute_breakdown.items()},
                "dominant_component": max(compute_breakdown, key=compute_breakdown.get)
            }
    
    # 生成扩展模式的建议
    scaling_analysis["recommendations"] = {
        "memory_optimization": [
            "KV Cache优化: 对于长序列，KV Cache成为主要内存消耗",
            "中间激活管理: MLP中间层激活占用大量内存",
            "批处理策略: 内存使用与batch_size线性相关"
        ],
        "compute_optimization": [
            "注意力优化: 序列长度的平方增长需要稀疏化技术",
            "MLP融合: Gate和Up投影可以融合减少内存访问",
            "量化收益: INT8计算可显著提升MLP性能"
        ],
        "scaling_patterns": [
            "内存: O(B×S×H + B×S²×num_heads) per layer",
            "计算: O(B×S×H² + B×S²×H) per layer", 
            "瓶颈转移: 短序列计算密集，长序列内存密集"
        ]
    }
    
    return scaling_analysis

def generate_comprehensive_shape_analysis():
    """生成综合的Shape分析报告"""
    
    print("开始详细算子Shape分析...")
    
    # 1. 详细算子分析
    operator_analysis = analyze_operator_shapes()
    
    # 2. 扩展模式分析
    scaling_analysis = analyze_scaling_patterns()
    
    # 3. 生成综合报告
    comprehensive_report = {
        "analysis_metadata": {
            "timestamp": "2025-09-16",
            "model": "DeepSeek-V3-INT8",
            "tensor_parallel": "4-way",
            "quantization": "W8A8-INT8"
        },
        "detailed_operator_analysis": operator_analysis,
        "scaling_pattern_analysis": scaling_analysis,
        "key_insights": {
            "shape_transformations": [
                "Embedding: [B,S] → [B,S,7168]",
                "QKV: [B,S,7168] → [B,S,9216] (per GPU: 2304)",
                "Attention: [B,S,7168] → [B,S,7168] with O(S²) complexity",
                "MLP: [B,S,7168] → [B,S,37888] → [B,S,18944] → [B,S,7168] (per GPU)",
                "LM Head: [B,S,7168] → [B,S,129280] (per GPU: 32320)"
            ],
            "quantization_impact": [
                "权重存储: INT8减少50%内存",
                "激活计算: 动态量化增加计算开销",
                "精度损失: 量化带来的精度下降",
                "吞吐量提升: INT8计算核心利用率更高"
            ],
            "memory_patterns": [
                "短序列: Embedding和MLP激活主导",
                "长序列: Attention权重和KV Cache主导",
                "批处理: 线性扩展，适合并行处理",
                "张量并行: 有效分布内存负载"
            ],
            "performance_bottlenecks": [
                "Prefill: 注意力计算O(S²)复杂度",
                "Decode: 内存访问延迟",
                "MoE: Expert路由和通信开销",
                "量化: 动态scale计算开销"
            ]
        }
    }
    
    return comprehensive_report

def main():
    """主函数"""
    print("开始SGLang算子Shape详细分析...")
    
    try:
        # 生成综合分析报告
        report = generate_comprehensive_shape_analysis()
        
        # 保存报告
        output_file = "detailed_operator_shape_analysis.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"详细Shape分析完成，报告已保存到: {output_file}")
        
        # 打印关键统计信息
        print("\n=== 算子Shape分析摘要 ===")
        
        for case_name, case_data in report["detailed_operator_analysis"].items():
            summary = case_data["summary"]
            print(f"\n{case_name}:")
            print(f"  总内存需求: {summary['total_memory_gb']:.3f} GB")
            print(f"  总计算量: {summary['total_gflops']:.1f} GFLOPS")
            print(f"  量化算子比例: {summary['quantization_ratio']:.1%}")
        
        print("\n=== 关键Shape变换 ===")
        for transform in report["key_insights"]["shape_transformations"]:
            print(f"  {transform}")
        
        print("\n=== 性能瓶颈 ===")
        for bottleneck in report["key_insights"]["performance_bottlenecks"]:
            print(f"  {bottleneck}")
        
    except Exception as e:
        print(f"分析过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
