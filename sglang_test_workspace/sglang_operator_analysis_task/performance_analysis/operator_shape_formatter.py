#!/usr/bin/env python3
"""
优化的算子Shape分析器 - 按照标准格式输出
根据用户提供的算子分析文档格式，重新统计所有参数运算的算子类型和shape
格式: layer | B | M | N | K (矩阵乘法维度)
"""

import json
import os
import sys
from typing import Dict, List, Tuple, Any
from dataclasses import dataclass

@dataclass
class OperatorShape:
    """算子Shape信息"""
    layer_name: str
    batch_size: int
    m_dim: int
    n_dim: int  
    k_dim: int
    operation_type: str
    quantization: str
    flops: int
    memory_mb: float
    
    def to_dict(self):
        return {
            'layer': self.layer_name,
            'B': self.batch_size,
            'M': self.m_dim,
            'N': self.n_dim,
            'K': self.k_dim,
            'operation_type': self.operation_type,
            'quantization': self.quantization,
            'flops': self.flops,
            'memory_mb': round(self.memory_mb, 2)
        }

class SGLangOperatorShapeAnalyzer:
    """SGLang算子Shape分析器"""
    
    def __init__(self):
        # DeepSeek-V3模型配置
        self.model_config = {
            "hidden_size": 7168,
            "intermediate_size": 18944,
            "num_layers": 61,
            "num_attention_heads": 56,
            "num_key_value_heads": 8,
            "head_dim": 128,
            "vocab_size": 129280,
            "tp_size": 4,
            "moe_intermediate_size": 2048,
            "num_experts": 256,
            "num_experts_per_tok": 8
        }
        
        # 计算分片后的维度
        self.tp_hidden_size = self.model_config["hidden_size"] // self.model_config["tp_size"]
        self.tp_intermediate_size = self.model_config["intermediate_size"] // self.model_config["tp_size"] 
        self.tp_vocab_size = self.model_config["vocab_size"] // self.model_config["tp_size"]
        self.tp_heads = self.model_config["num_attention_heads"] // self.model_config["tp_size"]
        self.tp_kv_heads = self.model_config["num_key_value_heads"] // self.model_config["tp_size"]
        
        # QKV合并后的维度 (Q + K + V)
        self.qkv_total_dim = (self.tp_heads + self.tp_kv_heads * 2) * self.model_config["head_dim"]
        
        self.operators = []
        
    def analyze_attention_operators(self, batch_size: int, seq_len: int) -> List[OperatorShape]:
        """分析Attention模块的算子"""
        ops = []
        
        # 1. QKV投影 - 合并的线性层
        # input: [B, S, H] @ weight: [H, qkv_dim] -> [B, S, qkv_dim]
        qkv_op = OperatorShape(
            layer_name="attn_qkv",
            batch_size=batch_size,
            m_dim=seq_len,
            k_dim=self.model_config["hidden_size"],
            n_dim=self.qkv_total_dim,
            operation_type="linear",
            quantization="W8A8",
            flops=2 * batch_size * seq_len * self.model_config["hidden_size"] * self.qkv_total_dim,
            memory_mb=batch_size * seq_len * (self.model_config["hidden_size"] + self.qkv_total_dim) * 2 / (1024**2)
        )
        ops.append(qkv_op)
        
        # 2. Q @ K^T 注意力计算
        # Q: [B, num_heads, S, head_dim] @ K^T: [B, num_heads, head_dim, S] -> [B, num_heads, S, S]
        attn_scores_op = OperatorShape(
            layer_name="attn_qk",
            batch_size=batch_size * self.tp_heads,
            m_dim=seq_len,
            k_dim=self.model_config["head_dim"],
            n_dim=seq_len,
            operation_type="bmm",
            quantization="BF16",
            flops=2 * batch_size * self.tp_heads * seq_len * seq_len * self.model_config["head_dim"],
            memory_mb=batch_size * self.tp_heads * seq_len * seq_len * 2 / (1024**2)
        )
        ops.append(attn_scores_op)
        
        # 3. Attention @ V
        # attn: [B, num_heads, S, S] @ V: [B, num_heads, S, head_dim] -> [B, num_heads, S, head_dim]
        attn_out_op = OperatorShape(
            layer_name="attn_pv", 
            batch_size=batch_size * self.tp_heads,
            m_dim=seq_len,
            k_dim=seq_len,
            n_dim=self.model_config["head_dim"],
            operation_type="bmm",
            quantization="BF16",
            flops=2 * batch_size * self.tp_heads * seq_len * seq_len * self.model_config["head_dim"],
            memory_mb=batch_size * self.tp_heads * seq_len * self.model_config["head_dim"] * 2 / (1024**2)
        )
        ops.append(attn_out_op)
        
        # 4. 输出投影
        # input: [B, S, tp_hidden] @ weight: [tp_hidden, H] -> [B, S, H]
        o_proj_op = OperatorShape(
            layer_name="attn_o",
            batch_size=batch_size,
            m_dim=seq_len,
            k_dim=self.tp_heads * self.model_config["head_dim"],
            n_dim=self.model_config["hidden_size"],
            operation_type="linear",
            quantization="W8A8",
            flops=2 * batch_size * seq_len * self.tp_heads * self.model_config["head_dim"] * self.model_config["hidden_size"],
            memory_mb=batch_size * seq_len * self.model_config["hidden_size"] * 2 / (1024**2)
        )
        ops.append(o_proj_op)
        
        return ops
    
    def analyze_mlp_operators(self, batch_size: int, seq_len: int) -> List[OperatorShape]:
        """分析MLP模块的算子"""
        ops = []
        
        # 1. Gate投影
        gate_op = OperatorShape(
            layer_name="mlp_gate",
            batch_size=batch_size,
            m_dim=seq_len,
            k_dim=self.model_config["hidden_size"],
            n_dim=self.tp_intermediate_size,
            operation_type="linear",
            quantization="W8A8",
            flops=2 * batch_size * seq_len * self.model_config["hidden_size"] * self.tp_intermediate_size,
            memory_mb=batch_size * seq_len * self.tp_intermediate_size * 2 / (1024**2)
        )
        ops.append(gate_op)
        
        # 2. Up投影 
        up_op = OperatorShape(
            layer_name="mlp_up",
            batch_size=batch_size,
            m_dim=seq_len,
            k_dim=self.model_config["hidden_size"],
            n_dim=self.tp_intermediate_size,
            operation_type="linear", 
            quantization="W8A8",
            flops=2 * batch_size * seq_len * self.model_config["hidden_size"] * self.tp_intermediate_size,
            memory_mb=batch_size * seq_len * self.tp_intermediate_size * 2 / (1024**2)
        )
        ops.append(up_op)
        
        # 3. Down投影
        down_op = OperatorShape(
            layer_name="mlp_down",
            batch_size=batch_size,
            m_dim=seq_len,
            k_dim=self.tp_intermediate_size,
            n_dim=self.model_config["hidden_size"],
            operation_type="linear",
            quantization="W8A8",
            flops=2 * batch_size * seq_len * self.tp_intermediate_size * self.model_config["hidden_size"],
            memory_mb=batch_size * seq_len * self.model_config["hidden_size"] * 2 / (1024**2)
        )
        ops.append(down_op)
        
        return ops
    
    def analyze_moe_operators(self, batch_size: int, seq_len: int) -> List[OperatorShape]:
        """分析MoE模块的算子"""
        ops = []
        
        # 1. Expert Gate (路由)
        gate_op = OperatorShape(
            layer_name="moe_gate",
            batch_size=batch_size,
            m_dim=seq_len,
            k_dim=self.model_config["hidden_size"],
            n_dim=self.model_config["num_experts"],
            operation_type="linear",
            quantization="BF16",
            flops=2 * batch_size * seq_len * self.model_config["hidden_size"] * self.model_config["num_experts"],
            memory_mb=batch_size * seq_len * self.model_config["num_experts"] * 2 / (1024**2)
        )
        ops.append(gate_op)
        
        # 2. Expert Up投影 (每个expert)
        expert_up_op = OperatorShape(
            layer_name="moe_expert_up",
            batch_size=batch_size,
            m_dim=seq_len,
            k_dim=self.model_config["hidden_size"],
            n_dim=self.model_config["moe_intermediate_size"],
            operation_type="linear",
            quantization="W8A8",
            flops=2 * batch_size * seq_len * self.model_config["hidden_size"] * self.model_config["moe_intermediate_size"] * self.model_config["num_experts_per_tok"],
            memory_mb=batch_size * seq_len * self.model_config["moe_intermediate_size"] * 2 / (1024**2)
        )
        ops.append(expert_up_op)
        
        # 3. Expert Gate投影 (每个expert)
        expert_gate_op = OperatorShape(
            layer_name="moe_expert_gate",
            batch_size=batch_size,
            m_dim=seq_len,
            k_dim=self.model_config["hidden_size"],
            n_dim=self.model_config["moe_intermediate_size"],
            operation_type="linear",
            quantization="W8A8",
            flops=2 * batch_size * seq_len * self.model_config["hidden_size"] * self.model_config["moe_intermediate_size"] * self.model_config["num_experts_per_tok"],
            memory_mb=batch_size * seq_len * self.model_config["moe_intermediate_size"] * 2 / (1024**2)
        )
        ops.append(expert_gate_op)
        
        # 4. Expert Down投影 (每个expert)
        expert_down_op = OperatorShape(
            layer_name="moe_expert_down",
            batch_size=batch_size,
            m_dim=seq_len,
            k_dim=self.model_config["moe_intermediate_size"],
            n_dim=self.model_config["hidden_size"],
            operation_type="linear",
            quantization="W8A8",
            flops=2 * batch_size * seq_len * self.model_config["moe_intermediate_size"] * self.model_config["hidden_size"] * self.model_config["num_experts_per_tok"],
            memory_mb=batch_size * seq_len * self.model_config["hidden_size"] * 2 / (1024**2)
        )
        ops.append(expert_down_op)
        
        return ops
    
    def analyze_embedding_operators(self, batch_size: int, seq_len: int) -> List[OperatorShape]:
        """分析Embedding算子"""
        ops = []
        
        # Embedding lookup (不是矩阵乘法，但用相似格式表示)
        embed_op = OperatorShape(
            layer_name="embedding",
            batch_size=batch_size,
            m_dim=seq_len,
            k_dim=1,  # lookup操作
            n_dim=self.model_config["hidden_size"],
            operation_type="lookup",
            quantization="BF16",
            flops=0,  # lookup没有FLOPS
            memory_mb=batch_size * seq_len * self.model_config["hidden_size"] * 2 / (1024**2)
        )
        ops.append(embed_op)
        
        return ops
    
    def analyze_lm_head_operators(self, batch_size: int, seq_len: int) -> List[OperatorShape]:
        """分析LM Head算子"""
        ops = []
        
        # LM Head投影
        lm_head_op = OperatorShape(
            layer_name="lm_head",
            batch_size=batch_size,
            m_dim=seq_len,
            k_dim=self.model_config["hidden_size"],
            n_dim=self.tp_vocab_size,
            operation_type="linear",
            quantization="BF16",
            flops=2 * batch_size * seq_len * self.model_config["hidden_size"] * self.tp_vocab_size,
            memory_mb=batch_size * seq_len * self.tp_vocab_size * 2 / (1024**2)
        )
        ops.append(lm_head_op)
        
        return ops
        
    def analyze_complete_model(self, batch_size: int, seq_len: int) -> Dict[str, Any]:
        """分析完整模型的所有算子"""
        all_operators = []
        
        # 1. Embedding层
        all_operators.extend(self.analyze_embedding_operators(batch_size, seq_len))
        
        # 2. Transformer层 (重复61次)
        for layer_idx in range(self.model_config["num_layers"]):
            # Attention
            attn_ops = self.analyze_attention_operators(batch_size, seq_len)
            for op in attn_ops:
                op.layer_name = f"layer_{layer_idx}_{op.layer_name}"
            all_operators.extend(attn_ops)
            
            # MLP 
            mlp_ops = self.analyze_mlp_operators(batch_size, seq_len)
            for op in mlp_ops:
                op.layer_name = f"layer_{layer_idx}_{op.layer_name}"
            all_operators.extend(mlp_ops)
            
            # MoE (假设每层都有MoE)
            moe_ops = self.analyze_moe_operators(batch_size, seq_len)
            for op in moe_ops:
                op.layer_name = f"layer_{layer_idx}_{op.layer_name}"
            all_operators.extend(moe_ops)
        
        # 3. LM Head
        all_operators.extend(self.analyze_lm_head_operators(batch_size, seq_len))
        
        # 统计信息
        total_flops = sum(op.flops for op in all_operators)
        total_memory = sum(op.memory_mb for op in all_operators)
        quantized_ops = len([op for op in all_operators if op.quantization == "W8A8"])
        total_ops = len(all_operators)
        
        return {
            "model_config": self.model_config,
            "test_config": {
                "batch_size": batch_size,
                "seq_len": seq_len
            },
            "operators": [op.to_dict() for op in all_operators],
            "statistics": {
                "total_operators": total_ops,
                "quantized_operators": quantized_ops,
                "quantization_ratio": quantized_ops / total_ops * 100,
                "total_flops": total_flops,
                "total_memory_mb": round(total_memory, 2),
                "total_memory_gb": round(total_memory / 1024, 3)
            }
        }
    
    def format_as_table(self, operators: List[Dict]) -> str:
        """按照用户提供的格式生成表格"""
        table = "shape\t\t\t\n"
        table += "layer\tB\tM\tN\tK\n"
        
        for op in operators:
            table += f"{op['layer']}\t{op['B']}\t{op['M']}\t{op['N']}\t{op['K']}\n"
        
        return table

def main():
    print("🔄 启动优化的SGLang算子Shape分析器...")
    
    analyzer = SGLangOperatorShapeAnalyzer()
    
    # 测试不同的配置
    test_cases = [
        {"name": "short_sequence", "batch_size": 1, "seq_len": 128},
        {"name": "medium_sequence", "batch_size": 1, "seq_len": 512}, 
        {"name": "long_sequence", "batch_size": 1, "seq_len": 2048},
        {"name": "batch_processing", "batch_size": 4, "seq_len": 512}
    ]
    
    all_results = {}
    
    for test_case in test_cases:
        print(f"\n📊 分析配置: {test_case['name']} (B={test_case['batch_size']}, S={test_case['seq_len']})")
        
        result = analyzer.analyze_complete_model(test_case['batch_size'], test_case['seq_len'])
        all_results[test_case['name']] = result
        
        print(f"   算子总数: {result['statistics']['total_operators']}")
        print(f"   量化算子: {result['statistics']['quantized_operators']}")
        print(f"   量化比例: {result['statistics']['quantization_ratio']:.1f}%")
        print(f"   总FLOPS: {result['statistics']['total_flops']:,}")
        print(f"   总内存: {result['statistics']['total_memory_gb']:.3f} GB")
    
    # 保存详细结果
    output_file = "optimized_operator_shape_analysis.json"
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(all_results, f, indent=2, ensure_ascii=False)
    print(f"\n💾 详细结果已保存到: {output_file}")
    
    # 生成格式化的表格 (按照用户文档格式)
    print(f"\n📋 生成标准格式的算子表格...")
    
    # 使用中等序列的结果作为示例
    medium_result = all_results["medium_sequence"]
    table_content = analyzer.format_as_table(medium_result["operators"][:20])  # 只显示前20个算子作为示例
    
    table_file = "operator_shape_table.md"
    with open(table_file, 'w', encoding='utf-8') as f:
        f.write("# SGLang算子Shape分析表格\n\n")
        f.write("## 配置信息\n")
        f.write(f"- 批次大小: {medium_result['test_config']['batch_size']}\n")
        f.write(f"- 序列长度: {medium_result['test_config']['seq_len']}\n")
        f.write(f"- 张量并行: {medium_result['model_config']['tp_size']}\n\n")
        f.write("## 算子Shape表格 (前20个算子示例)\n\n")
        f.write("```\n")
        f.write(table_content)
        f.write("```\n\n")
        f.write("## 算子类型说明\n")
        f.write("- **attn_qkv**: QKV合并投影\n")
        f.write("- **attn_qk**: Q与K的注意力计算\n") 
        f.write("- **attn_pv**: 注意力权重与V的乘法\n")
        f.write("- **attn_o**: 注意力输出投影\n")
        f.write("- **mlp_gate**: MLP门控投影\n")
        f.write("- **mlp_up**: MLP上投影\n")
        f.write("- **mlp_down**: MLP下投影\n")
        f.write("- **moe_gate**: MoE路由门控\n")
        f.write("- **moe_expert_*****: MoE专家网络算子\n")
        f.write("- **embedding**: 词嵌入查找\n")
        f.write("- **lm_head**: 语言建模头投影\n\n")
        f.write("## 维度说明\n")
        f.write("- **B**: 批次维度\n")
        f.write("- **M**: 矩阵乘法的M维度 (通常是序列长度)\n")
        f.write("- **N**: 矩阵乘法的N维度 (输出特征维度)\n") 
        f.write("- **K**: 矩阵乘法的K维度 (输入特征维度)\n")
    
    print(f"📋 标准格式表格已保存到: {table_file}")
    
    # 输出关键统计信息
    print(f"\n📈 关键统计汇总:")
    for name, result in all_results.items():
        stats = result['statistics']
        print(f"   {name}: {stats['total_operators']}算子, {stats['quantization_ratio']:.1f}%量化, {stats['total_memory_gb']:.2f}GB内存, {stats['total_flops']/1e9:.1f}GFLOPS")
    
    print(f"\n✅ 算子Shape分析完成!")
    return all_results

if __name__ == "__main__":
    results = main()
