#!/usr/bin/env python3
"""
简化的算子追踪脚本
专注于捕获推理过程中的核心算子和shape信息
"""

import torch
import sglang as sgl
import json
import time
from datetime import datetime
from collections import defaultdict
import traceback
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(__file__))

MODEL_PATH = "/home/<USER>/deepseek-v3-int8"

class SimpleOperatorTracer:
    def __init__(self):
        self.operation_calls = defaultdict(list)
        self.tensor_shapes = defaultdict(list)
        self.module_calls = defaultdict(int)
        self.hooks = []
        
    def _create_hook(self, module_name):
        """创建简单的forward hook"""
        def hook_fn(module, input, output):
            try:
                # 记录模块调用
                self.module_calls[module_name] += 1
                
                # 记录输入输出张量shapes
                if isinstance(input, (tuple, list)) and len(input) > 0:
                    input_shapes = []
                    for inp in input:
                        if isinstance(inp, torch.Tensor):
                            input_shapes.append(list(inp.shape))
                    if input_shapes:
                        self.tensor_shapes[f"{module_name}_input"].extend(input_shapes)
                
                if isinstance(output, torch.Tensor):
                    self.tensor_shapes[f"{module_name}_output"].append(list(output.shape))
                elif isinstance(output, (tuple, list)):
                    output_shapes = []
                    for out in output:
                        if isinstance(out, torch.Tensor):
                            output_shapes.append(list(out.shape))
                    if output_shapes:
                        self.tensor_shapes[f"{module_name}_output"].extend(output_shapes)
                        
                # 记录操作类型
                module_type = type(module).__name__
                self.operation_calls[module_type].append({
                    'module_name': module_name,
                    'timestamp': time.time()
                })
                
            except Exception as e:
                print(f"Hook error in {module_name}: {e}")
                
        return hook_fn
    
    def install_hooks(self, model):
        """安装hooks到关键模块"""
        print("[TRACE] 开始安装算子追踪hooks...")
        
        hook_count = 0
        for name, module in model.named_modules():
            # 只监控关键模块类型以减少开销
            if any(key in type(module).__name__.lower() for key in [
                'linear', 'embedding', 'layernorm', 'attention', 'moe', 'mlp'
            ]):
                hook = module.register_forward_hook(self._create_hook(name))
                self.hooks.append(hook)
                hook_count += 1
                
                # 限制hook数量以避免内存问题
                if hook_count >= 50:
                    break
        
        print(f"[TRACE] 安装了 {hook_count} 个hooks")
    
    def remove_hooks(self):
        """移除所有hooks"""
        for hook in self.hooks:
            hook.remove()
        self.hooks.clear()
        print("[TRACE] 已移除所有hooks")
    
    def get_summary(self):
        """获取统计摘要"""
        return {
            'total_modules': len(self.module_calls),
            'total_calls': sum(self.module_calls.values()),
            'module_call_counts': dict(self.module_calls),
            'operation_types': {op_type: len(calls) for op_type, calls in self.operation_calls.items()},
            'tensor_shape_summary': {
                name: {
                    'unique_shapes': len(set(tuple(shape) for shape in shapes)),
                    'total_occurrences': len(shapes),
                    'sample_shapes': list(set(tuple(shape) for shape in shapes))[:5]
                }
                for name, shapes in self.tensor_shapes.items()
                if shapes
            }
        }

def run_simple_inference_test():
    """运行简化的推理测试"""
    print("开始简化的SGLang INT8算子追踪...")
    
    tracer = SimpleOperatorTracer()
    
    try:
        # 创建引擎 - 使用更小的配置
        print("[TRACE] 初始化模型...")
        
        llm = sgl.Engine(
            model_path=MODEL_PATH,
            tp_size=4,
            quantization="w8a8_int8", 
            trust_remote_code=True,
            disable_cuda_graph=True,
            log_level="warning",  # 减少日志输出
            mem_fraction_static=0.7,  # 降低内存使用
        )
        
        # 尝试获取模型并安装hooks
        print("[TRACE] 查找模型结构...")
        model = None
        
        # 检查引擎的各种属性来找到模型
        if hasattr(llm, 'model_runner') and hasattr(llm.model_runner, 'model'):
            model = llm.model_runner.model
            print("[TRACE] 找到模型在 model_runner.model")
        else:
            # 尝试通过worker访问
            try:
                # SGLang可能使用worker架构
                print("[TRACE] 尝试通过其他路径访问模型...")
                print(f"[TRACE] Engine属性: {[attr for attr in dir(llm) if not attr.startswith('_')][:10]}")
            except:
                pass
        
        if model:
            tracer.install_hooks(model)
            
            # 运行简单的推理测试
            print("[TRACE] 开始推理测试...")
            
            test_prompts = [
                "Hello",
                "What is AI?",
                "Explain machine learning"
            ]
            
            for i, prompt in enumerate(test_prompts):
                print(f"[TRACE] 测试 {i+1}: {prompt}")
                try:
                    response = llm.generate(
                        prompt, 
                        sampling_params={"temperature": 0.1, "max_new_tokens": 20}
                    )
                    print(f"[TRACE] 响应: {response[0]['text'][:50]}...")
                    
                    # 每次推理后短暂等待
                    time.sleep(1)
                    
                except Exception as e:
                    print(f"[TRACE] 推理错误: {e}")
                    continue
            
            # 获取统计信息
            summary = tracer.get_summary()
            
            # 保存结果
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # 保存JSON报告
            json_file = f"simple_operator_trace_{timestamp}.json"
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(summary, f, ensure_ascii=False, indent=2)
            
            # 生成Markdown报告
            md_file = f"simple_operator_trace_{timestamp}.md"
            generate_markdown_report(summary, md_file)
            
            print(f"\n=== 算子追踪摘要 ===")
            print(f"总模块数: {summary['total_modules']}")
            print(f"总调用次数: {summary['total_calls']}")
            print(f"操作类型: {len(summary['operation_types'])}")
            print(f"张量形状类型: {len(summary['tensor_shape_summary'])}")
            
            print(f"\n报告已保存:")
            print(f"- JSON: {json_file}")
            print(f"- Markdown: {md_file}")
            
        else:
            print("[TRACE] 未能找到模型，跳过hooks安装")
            
    except Exception as e:
        print(f"[TRACE] 执行错误: {e}")
        traceback.print_exc()
    
    finally:
        # 清理
        tracer.remove_hooks()
        try:
            if 'llm' in locals():
                llm.shutdown()
        except:
            pass

def generate_markdown_report(summary, filename):
    """生成Markdown格式的报告"""
    
    content = f"""# SGLang INT8 算子追踪报告

## 生成时间
{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

## 总体统计

| 指标 | 数值 |
|------|------|
| 总模块数 | {summary['total_modules']} |
| 总调用次数 | {summary['total_calls']} |
| 操作类型数 | {len(summary['operation_types'])} |
| 张量形状类型数 | {len(summary['tensor_shape_summary'])} |

## 模块调用统计

| 模块名 | 调用次数 |
|--------|----------|
"""
    
    # 模块调用次数（前20个）
    sorted_modules = sorted(summary['module_call_counts'].items(), key=lambda x: x[1], reverse=True)
    for module_name, count in sorted_modules[:20]:
        content += f"| {module_name} | {count} |\n"
    
    content += "\n## 操作类型统计\n\n| 操作类型 | 出现次数 |\n|----------|----------|\n"
    
    # 操作类型统计
    sorted_ops = sorted(summary['operation_types'].items(), key=lambda x: x[1], reverse=True)
    for op_type, count in sorted_ops:
        content += f"| {op_type} | {count} |\n"
    
    content += "\n## 张量形状统计\n\n| 模块.输入输出 | 唯一形状数 | 总出现次数 | 示例形状 |\n|--------------|------------|------------|----------|\n"
    
    # 张量形状统计
    for name, shape_info in summary['tensor_shape_summary'].items():
        sample_shapes = ', '.join([str(list(shape)) for shape in shape_info['sample_shapes'][:3]])
        content += f"| {name} | {shape_info['unique_shapes']} | {shape_info['total_occurrences']} | {sample_shapes} |\n"
    
    content += "\n## 关键发现\n\n"
    content += "1. **模块调用模式**: 记录了实际推理过程中各模块的调用频率\n"
    content += "2. **算子类型分布**: 统计了不同类型算子的使用情况\n"
    content += "3. **张量形状模式**: 追踪了各模块输入输出的张量形状变化\n"
    content += "4. **性能分析基础**: 为后续优化提供了数据基础\n"
    
    with open(filename, 'w', encoding='utf-8') as f:
        f.write(content)

if __name__ == "__main__":
    run_simple_inference_test()
