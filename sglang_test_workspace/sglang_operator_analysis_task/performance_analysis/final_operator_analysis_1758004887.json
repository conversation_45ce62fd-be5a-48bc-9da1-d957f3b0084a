{"analysis_metadata": {"timestamp": 1758004887.9604423, "total_unique_modules": 0, "total_operator_calls": 0, "total_memory_usage_bytes": 0, "tracking_method": "global_forward_hook_interception"}, "test_execution_results": {"short_generation": {"prompt": "你好", "generated_text": "uck <PERSON>", "execution_time": 5.246419906616211, "max_tokens": 3, "description": "短文本生成测试"}, "medium_generation": {"prompt": "请简单介绍一下人工智能", "generated_text": "_lengthর্�/document meat法西斯AM Intelligent智库 transitional在马", "execution_time": 0.8800983428955078, "max_tokens": 10, "description": "中等长度文本生成测试"}, "complex_reasoning": {"prompt": "请分析深度学习和机器学习的主要区别", "generated_text": "逃脱另一种 ventral提醒 Jonah repairingينة出海 внутрен嘱咐 kerja insult海边捕 frontosterone� Zo στον reactions", "execution_time": 0.11720132827758789, "max_tokens": 20, "description": "复杂推理文本生成测试"}}, "module_type_breakdown": {}, "computation_type_breakdown": {}, "test_case_breakdown": {}, "detailed_module_analysis": {}, "call_sequence_analysis": {}, "memory_usage_analysis": {"total_memory_bytes": 0, "average_per_call": 0, "top_memory_consumers": [], "memory_by_module_type": {}}, "global_call_order": [], "top_modules_by_calls": [], "performance_insights": {"execution_summary": {"total_execution_time": 6.243719577789307, "successful_tests": 3, "total_tests": 3, "average_time_per_test": 2.081239859263102}, "operator_efficiency": {}, "bottleneck_analysis": {}}}