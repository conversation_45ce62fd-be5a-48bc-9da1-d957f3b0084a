#!/usr/bin/env python3
"""
SGLang INT8模型推理性能分析脚本
实际运行模型并统计算子的shape和运算性能
"""

import os
import sys
import time
import traceback
import json
import torch
import numpy as np
from typing import Dict, List, Any, Tu<PERSON>
from collections import defaultdict, OrderedDict
from contextlib import contextmanager

# 必须在导入 sglang 前设置分布式/通信相关环境
os.environ.setdefault("SGL_DISABLE_TP_MEMORY_INBALANCE_CHECK", "1")
os.environ.setdefault("UCX_VFS_ENABLE", "n")
os.environ.setdefault("NCCL_P2P_DISABLE", "1")
os.environ.setdefault("NCCL_IB_DISABLE", "1")
os.environ.setdefault("SGLANG_IS_FLASHINFER_AVAILABLE", "false")
# 指定使用0号GPU
os.environ.setdefault("CUDA_VISIBLE_DEVICES", "0")

import sglang as sgl

MODEL_PATH = os.environ.get("MODEL_PATH", "/home/<USER>/deepseek-int8")

class PerformanceProfiler:
    """性能分析器"""
    
    def __init__(self):
        self.timings = defaultdict(list)
        self.memory_usage = defaultdict(list)
        self.tensor_shapes = defaultdict(list)
        self.operator_counts = defaultdict(int)
        self.current_phase = "unknown"
        self.cuda_events = {}
        
        # 新增：详细算子追踪
        self.detailed_operators = {}
        self.module_calls = defaultdict(list)
        self.layer_operations = defaultdict(dict)
        self.hooks = []
        self.call_stack = []
        
    def reset(self):
        """重置统计数据"""
        self.timings.clear()
        self.memory_usage.clear()
        self.tensor_shapes.clear()
        self.operator_counts.clear()
        self.cuda_events.clear()
        # 新增：重置详细追踪数据
        self.detailed_operators.clear()
        self.module_calls.clear()
        self.layer_operations.clear()
        self.call_stack.clear()
        self.remove_hooks()
    
    def set_phase(self, phase: str):
        """设置当前阶段"""
        self.current_phase = phase
        print(f"[PERF] 切换到阶段: {phase}")
    
    @contextmanager
    def time_context(self, operation_name: str):
        """计时上下文管理器"""
        torch.cuda.synchronize()
        start_time = time.perf_counter()
        start_event = torch.cuda.Event(enable_timing=True)
        end_event = torch.cuda.Event(enable_timing=True)
        
        start_event.record()
        try:
            yield
        finally:
            end_event.record()
            torch.cuda.synchronize()
            end_time = time.perf_counter()
            
            cpu_time = (end_time - start_time) * 1000  # ms
            gpu_time = start_event.elapsed_time(end_event)  # ms
            
            self.timings[f"{self.current_phase}_{operation_name}"].append({
                "cpu_time_ms": cpu_time,
                "gpu_time_ms": gpu_time,
                "timestamp": time.time()
            })
    
    def record_memory(self, operation_name: str):
        """记录内存使用"""
        if torch.cuda.is_available():
            memory_allocated = torch.cuda.memory_allocated() / 1024**3  # GB
            memory_cached = torch.cuda.memory_reserved() / 1024**3  # GB
            
            self.memory_usage[f"{self.current_phase}_{operation_name}"].append({
                "allocated_gb": memory_allocated,
                "cached_gb": memory_cached,
                "timestamp": time.time()
            })
    
    def record_tensor_shape(self, operation_name: str, tensor_info: Dict):
        """记录张量shape信息"""
        self.tensor_shapes[f"{self.current_phase}_{operation_name}"].append(tensor_info)
    
    def install_detailed_hooks(self, engine):
        """安装详细的hooks到模型组件"""
        try:
            # 尝试多种方式找到模型
            model = None
            
            # 方法1: 通过model_runner
            if hasattr(engine, 'model_runner') and hasattr(engine.model_runner, 'model'):
                model = engine.model_runner.model
                print(f"[PERF] 找到模型结构(model_runner.model)，开始安装hooks...")
            # 方法2: 直接通过engine.model
            elif hasattr(engine, 'model'):
                model = engine.model
                print(f"[PERF] 找到引擎模型(engine.model)，开始安装hooks...")
            # 方法3: 通过tp_cpu_group（多GPU环境）
            elif hasattr(engine, 'tp_cpu_group'):
                # 尝试获取第一个GPU的模型
                print(f"[PERF] 尝试在多GPU环境中查找模型...")
                for attr_name in dir(engine):
                    attr = getattr(engine, attr_name)
                    if hasattr(attr, 'model'):
                        model = attr.model
                        print(f"[PERF] 在{attr_name}中找到模型，开始安装hooks...")
                        break
            
            if model is not None:
                self._install_hooks_recursive(model, "model")
                print(f"[PERF] hooks安装完成，模型类型: {type(model)}")
                
                # 打印模型结构摘要
                module_count = 0
                for name, module in model.named_modules():
                    module_count += 1
                    if module_count <= 10:  # 只打印前10个模块
                        print(f"[PERF] 模块: {name} -> {type(module)}")
                    
                print(f"[PERF] 总共找到 {module_count} 个模块")
            else:
                print(f"[PERF] 无法找到模型，引擎属性: {[attr for attr in dir(engine) if not attr.startswith('_')]}")
                
        except Exception as e:
            print(f"[PERF] 安装hooks出现异常: {e}")
            import traceback
            traceback.print_exc()
    
    def _install_hooks_recursive(self, module, prefix):
        """递归安装hooks"""
        def create_hook(module_name):
            def hook_fn(module, input, output):
                try:
                    # 记录模块调用信息
                    call_info = {
                        "module_type": type(module).__name__,
                        "timestamp": time.time(),
                        "phase": self.current_phase,
                        "input_shapes": [],
                        "output_shapes": []
                    }
                    
                    # 安全地获取input shapes
                    try:
                        if isinstance(input, (list, tuple)):
                            for inp in input:
                                if hasattr(inp, 'shape'):
                                    call_info["input_shapes"].append(list(inp.shape))
                        elif hasattr(input, 'shape'):
                            call_info["input_shapes"].append(list(input.shape))
                    except Exception:
                        call_info["input_shapes"] = ["unknown"]
                    
                    # 安全地获取output shapes
                    try:
                        if isinstance(output, (list, tuple)):
                            for out in output:
                                if hasattr(out, 'shape'):
                                    call_info["output_shapes"].append(list(out.shape))
                        elif hasattr(output, 'shape'):
                            call_info["output_shapes"].append(list(output.shape))
                    except Exception:
                        call_info["output_shapes"] = ["unknown"]
                    
                    # 记录模块调用
                    self.module_calls[module_name].append(call_info)
                    
                    # 解析层级操作
                    self._parse_layer_operation(module_name, call_info)
                    
                except Exception as e:
                    # 静默处理hook错误，避免干扰推理
                    pass
            
            return hook_fn
        
        for name, child in module.named_children():
            if prefix:
                full_name = f"{prefix}.{name}"
            else:
                full_name = name
            
            # 为所有包含参数的模块安装hook
            if len(list(child.parameters())) > 0:
                hook = child.register_forward_hook(create_hook(full_name))
                self.hooks.append(hook)
            
            # 递归处理子模块
            self._install_hooks_recursive(child, full_name)
    
    def _parse_layer_operation(self, module_name: str, call_info: Dict):
        """解析层级操作信息"""
        parts = module_name.split('.')
        
        # 识别层编号
        layer_num = None
        for part in parts:
            if part.startswith('layer') or part.startswith('layers'):
                try:
                    # 尝试从layers.0或layer_0格式中提取数字
                    if '.' in module_name and parts.index(part) + 1 < len(parts):
                        next_part = parts[parts.index(part) + 1]
                        if next_part.isdigit():
                            layer_num = int(next_part)
                    elif '_' in part:
                        layer_num = int(part.split('_')[-1])
                except (ValueError, IndexError):
                    pass
                break
        
        if layer_num is not None:
            layer_key = f"layer_{layer_num}"
            if layer_key not in self.layer_operations:
                self.layer_operations[layer_key] = {
                    "attention": [],
                    "mlp": [],
                    "moe": [],
                    "other": []
                }
            
            # 分类操作类型
            if any(kw in module_name.lower() for kw in ['attn', 'attention', 'self_attn']):
                self.layer_operations[layer_key]["attention"].append({
                    "module_name": module_name,
                    "call_info": call_info
                })
            elif any(kw in module_name.lower() for kw in ['mlp', 'feed_forward', 'ffn']):
                self.layer_operations[layer_key]["mlp"].append({
                    "module_name": module_name,
                    "call_info": call_info
                })
            elif any(kw in module_name.lower() for kw in ['moe', 'expert', 'gate']):
                self.layer_operations[layer_key]["moe"].append({
                    "module_name": module_name,
                    "call_info": call_info
                })
            else:
                self.layer_operations[layer_key]["other"].append({
                    "module_name": module_name,
                    "call_info": call_info
                })
    
    def remove_hooks(self):
        """移除所有hooks"""
        for hook in self.hooks:
            hook.remove()
        self.hooks.clear()
    
    def get_summary(self) -> Dict:
        """获取性能统计摘要"""
        summary = {
            "timing_summary": {},
            "memory_summary": {},
            "shape_summary": {},
            "operator_counts": dict(self.operator_counts)
        }
        
        # 计时统计
        for op_name, times in self.timings.items():
            if times:
                cpu_times = [t["cpu_time_ms"] for t in times]
                gpu_times = [t["gpu_time_ms"] for t in times]
                
                summary["timing_summary"][op_name] = {
                    "count": len(times),
                    "cpu_time_ms": {
                        "mean": np.mean(cpu_times),
                        "std": np.std(cpu_times),
                        "min": np.min(cpu_times),
                        "max": np.max(cpu_times),
                        "total": np.sum(cpu_times)
                    },
                    "gpu_time_ms": {
                        "mean": np.mean(gpu_times),
                        "std": np.std(gpu_times),
                        "min": np.min(gpu_times),
                        "max": np.max(gpu_times),
                        "total": np.sum(gpu_times)
                    }
                }
        
        # 内存统计
        for op_name, memories in self.memory_usage.items():
            if memories:
                allocated = [m["allocated_gb"] for m in memories]
                cached = [m["cached_gb"] for m in memories]
                
                summary["memory_summary"][op_name] = {
                    "count": len(memories),
                    "allocated_gb": {
                        "mean": np.mean(allocated),
                        "std": np.std(allocated),
                        "min": np.min(allocated),
                        "max": np.max(allocated)
                    },
                    "cached_gb": {
                        "mean": np.mean(cached),
                        "std": np.std(cached),
                        "min": np.min(cached),
                        "max": np.max(cached)
                    }
                }
        
        # Shape统计
        for op_name, shapes in self.tensor_shapes.items():
            if shapes:
                summary["shape_summary"][op_name] = shapes
        
        # 新增：详细算子统计
        summary["detailed_operators"] = {
            "module_calls": dict(self.module_calls),
            "layer_operations": dict(self.layer_operations),
            "operator_statistics": self._generate_operator_statistics()
        }
        
        return summary
    
    def _generate_operator_statistics(self) -> Dict:
        """生成算子统计信息"""
        stats = {
            "total_module_calls": sum(len(calls) for calls in self.module_calls.values()),
            "unique_modules": len(self.module_calls),
            "layer_count": len(self.layer_operations),
            "module_type_distribution": defaultdict(int),
            "layer_breakdown": {}
        }
        
        # 统计模块类型分布
        for module_name, calls in self.module_calls.items():
            if calls:
                module_type = calls[0]["module_type"]
                stats["module_type_distribution"][module_type] += len(calls)
        
        # 统计每层的详细信息
        for layer_name, operations in self.layer_operations.items():
            layer_stats = {
                "attention_ops": len(operations["attention"]),
                "mlp_ops": len(operations["mlp"]),
                "moe_ops": len(operations["moe"]),
                "other_ops": len(operations["other"]),
                "total_ops": sum(len(ops) for ops in operations.values())
            }
            stats["layer_breakdown"][layer_name] = layer_stats
        
        return stats

# 全局性能分析器
profiler = PerformanceProfiler()

def analyze_model_initialization():
    """分析模型初始化性能"""
    print("[PERF] 开始分析模型初始化...")
    profiler.set_phase("initialization")
    
    llm = None
    with profiler.time_context("model_loading"):
        try:
            profiler.record_memory("before_loading")
            
            llm = sgl.Engine(
                model_path=MODEL_PATH,
                tp_size=1,  # 改为单GPU模式
                quantization="w8a8_int8",  # 保持INT8量化
                trust_remote_code=True,
                mem_fraction_static=0.6,  # 减少显存使用
                disable_cuda_graph=True,
                log_level="info",
            )
            
            profiler.record_memory("after_loading")
            
            # 安装详细的算子追踪hooks
            try:
                # 直接传递engine让install_detailed_hooks来查找模型
                profiler.install_detailed_hooks(llm)
                print("[PERF] 详细算子追踪hooks安装成功")
            except Exception as e:
                print(f"[PERF] 安装hooks失败: {e}")
                import traceback
                traceback.print_exc()
            
        except Exception as e:
            print(f"[PERF] 模型加载失败: {e}")
            raise
    
    return llm

def analyze_inference_performance(llm, test_cases: List[Dict]):
    """分析推理性能"""
    results = {}
    
    for i, test_case in enumerate(test_cases):
        case_name = test_case["name"]
        prompt = test_case["prompt"]
        sampling_params = test_case["sampling_params"]
        
        print(f"[PERF] 测试用例 {i+1}: {case_name}")
        
        # Prefill阶段分析
        profiler.set_phase(f"prefill_{case_name}")
        
        with profiler.time_context("full_inference"):
            profiler.record_memory("before_inference")
            
            # 执行推理
            try:
                output = llm.generate(
                    prompt=prompt,
                    sampling_params=sampling_params
                )
                
                profiler.record_memory("after_inference")
                
                # 记录输入输出shape信息
                input_info = {
                    "prompt_length": len(prompt.split()) if isinstance(prompt, str) else 0,
                    "estimated_tokens": len(prompt.split()) * 1.3 if isinstance(prompt, str) else 0,  # 估算
                    "max_new_tokens": sampling_params.get("max_new_tokens", 0)
                }
                
                output_info = {
                    "generated_text": output.get("text", ""),
                    "output_length": len(output.get("text", "").split()) if output.get("text") else 0
                }
                
                profiler.record_tensor_shape("input_output", {
                    "input": input_info,
                    "output": output_info,
                    "case_name": case_name
                })
                
                results[case_name] = {
                    "success": True,
                    "output": output,
                    "input_info": input_info,
                    "output_info": output_info
                }
                
            except Exception as e:
                print(f"[PERF] 推理失败: {e}")
                results[case_name] = {
                    "success": False,
                    "error": str(e)
                }
        
        # 多次运行以获得稳定的性能数据
        if results[case_name]["success"]:
            print(f"[PERF] 重复执行 {case_name} 以获得稳定测量...")
            for repeat in range(3):
                profiler.set_phase(f"repeat_{case_name}_{repeat}")
                with profiler.time_context("repeated_inference"):
                    try:
                        llm.generate(prompt=prompt, sampling_params=sampling_params)
                    except Exception as e:
                        print(f"[PERF] 重复执行失败: {e}")
    
    return results

def analyze_detailed_operators(llm):
    """详细分析推理过程中的所有算子"""
    print("[PERF] 开始详细算子分析...")
    
    # 重置计数器
    profiler.module_calls.clear()
    profiler.layer_operations.clear()
    
    # 测试不同的推理用例以捕获所有算子
    test_cases = [
        {"prompt": "你好", "max_tokens": 5, "name": "simple"},
        {"prompt": "请详细解释人工智能技术的发展历程", "max_tokens": 20, "name": "complex"}
    ]
    
    detailed_results = {}
    
    for i, test_case in enumerate(test_cases):
        case_name = test_case["name"]
        profiler.set_phase(f"detailed_analysis_{case_name}")
        
        print(f"[PERF] 执行详细分析用例 {i+1}: {case_name}")
        
        try:
            # 执行推理以触发所有算子
            result = llm.generate(
                prompt=test_case["prompt"],
                sampling_params={"max_new_tokens": test_case["max_tokens"]}
            )
            
            print(f"[PERF] {case_name} 推理完成，捕获到 {len(profiler.module_calls)} 个不同模块")
            
        except Exception as e:
            print(f"[PERF] {case_name} 推理失败: {e}")
            continue
    
    # 生成详细的算子报告
    detailed_analysis = generate_detailed_operator_report()
    
    return detailed_analysis

def generate_detailed_operator_report():
    """生成详细的算子报告"""
    print("[PERF] 生成详细算子报告...")
    
    report = {
        "summary": {
            "total_unique_modules": len(profiler.module_calls),
            "total_calls": sum(len(calls) for calls in profiler.module_calls.values()),
            "layers_analyzed": len(profiler.layer_operations)
        },
        "module_details": {},
        "layer_breakdown": {},
        "operator_shapes": {}
    }
    
    # 详细模块信息
    for module_name, calls in profiler.module_calls.items():
        if calls:
            first_call = calls[0]
            report["module_details"][module_name] = {
                "module_type": first_call["module_type"],
                "call_count": len(calls),
                "input_shapes": first_call.get("input_shapes", []),
                "output_shapes": first_call.get("output_shapes", []),
                "typical_shapes": _extract_typical_shapes(calls)
            }
    
    # 按层分解
    for layer_name, operations in profiler.layer_operations.items():
        layer_info = {
            "attention_modules": [],
            "mlp_modules": [],
            "moe_modules": [],
            "other_modules": []
        }
        
        for category in ["attention", "mlp", "moe", "other"]:
            for op in operations[category]:
                module_info = {
                    "module_name": op["module_name"],
                    "module_type": op["call_info"]["module_type"],
                    "shapes": {
                        "input": op["call_info"].get("input_shapes", []),
                        "output": op["call_info"].get("output_shapes", [])
                    }
                }
                layer_info[f"{category}_modules"].append(module_info)
        
        report["layer_breakdown"][layer_name] = layer_info
    
    # 算子shape汇总
    for module_name, calls in profiler.module_calls.items():
        if calls and calls[0].get("input_shapes") and calls[0].get("output_shapes"):
            report["operator_shapes"][module_name] = {
                "input_shapes": calls[0]["input_shapes"],
                "output_shapes": calls[0]["output_shapes"],
                "module_type": calls[0]["module_type"]
            }
    
    return report

def _extract_typical_shapes(calls):
    """提取典型的shape信息"""
    if not calls:
        return {}
    
    # 取第一个调用的shape作为典型shape
    typical = {
        "input_shapes": calls[0].get("input_shapes", []),
        "output_shapes": calls[0].get("output_shapes", [])
    }
    
    # 如果有多个调用，检查shape是否一致
    if len(calls) > 1:
        shapes_vary = False
        for call in calls[1:]:
            if (call.get("input_shapes") != typical["input_shapes"] or
                call.get("output_shapes") != typical["output_shapes"]):
                shapes_vary = True
                break
        typical["shapes_vary"] = shapes_vary
    
    return typical

def analyze_different_sequence_lengths(llm):
    """分析不同序列长度的性能"""
    print("[PERF] 分析不同序列长度的性能...")
    
    base_prompt = "请详细解释"
    test_prompts = [
        ("短序列", base_prompt + "人工智能。", {"max_new_tokens": 10}),
        ("中序列", base_prompt + "人工智能的发展历史、当前状态和未来趋势。", {"max_new_tokens": 20}),
        ("长序列", base_prompt + "人工智能在现代社会中的应用、挑战、伦理问题以及对人类社会可能产生的深远影响。", {"max_new_tokens": 30}),
    ]
    
    sequence_results = {}
    
    for seq_type, prompt, params in test_prompts:
        profiler.set_phase(f"sequence_analysis_{seq_type}")
        
        print(f"[PERF] 测试序列类型: {seq_type}")
        print(f"[PERF] 提示词长度: {len(prompt)} 字符, {len(prompt.split())} 词")
        
        # 预热
        try:
            llm.generate(prompt=prompt[:20], sampling_params={"max_new_tokens": 1})
        except:
            pass
        
        # 正式测试
        timings = []
        memory_usages = []
        
        for run in range(3):
            profiler.record_memory(f"before_{seq_type}_run_{run}")
            
            with profiler.time_context(f"{seq_type}_inference"):
                start_time = time.perf_counter()
                try:
                    result = llm.generate(prompt=prompt, sampling_params=params)
                    end_time = time.perf_counter()
                    
                    inference_time = (end_time - start_time) * 1000  # ms
                    timings.append(inference_time)
                    
                    profiler.record_memory(f"after_{seq_type}_run_{run}")
                    
                    # 计算吞吐量
                    output_text = result.get("text", "")
                    output_tokens = len(output_text.split()) if output_text else 0
                    total_tokens = len(prompt.split()) + output_tokens
                    
                    if inference_time > 0:
                        throughput = total_tokens / (inference_time / 1000)  # tokens/sec
                    else:
                        throughput = 0
                    
                    profiler.record_tensor_shape(f"{seq_type}_throughput", {
                        "run": run,
                        "input_tokens": len(prompt.split()),
                        "output_tokens": output_tokens,
                        "total_tokens": total_tokens,
                        "inference_time_ms": inference_time,
                        "throughput_tokens_per_sec": throughput
                    })
                    
                except Exception as e:
                    print(f"[PERF] {seq_type} 第{run+1}次运行失败: {e}")
        
        sequence_results[seq_type] = {
            "timings_ms": timings,
            "avg_time_ms": np.mean(timings) if timings else 0,
            "std_time_ms": np.std(timings) if timings else 0,
            "prompt_length": len(prompt),
            "prompt_tokens": len(prompt.split())
        }
    
    return sequence_results

def analyze_tensor_shapes_and_memory():
    """分析张量shape和内存使用模式"""
    print("[PERF] 分析张量shape和内存使用模式...")
    
    # 基于理论分析的shape信息
    model_config = {
        "hidden_size": 7168,
        "intermediate_size": 18944,
        "num_layers": 61,
        "num_attention_heads": 56,
        "num_key_value_heads": 8,
        "head_dim": 128,
        "vocab_size": 129280,
        "tp_size": 4
    }
    
    def calculate_tensor_memory(shape: Tuple, dtype_bytes: int) -> float:
        """计算张量内存使用 (MB)"""
        elements = 1
        for dim in shape:
            elements *= dim
        return elements * dtype_bytes / (1024 * 1024)
    
    # 分析不同batch size和sequence length的内存使用
    batch_sizes = [1, 2, 4, 8]
    seq_lengths = [32, 64, 128, 256, 512, 1024]
    
    memory_analysis = {}
    
    for batch_size in batch_sizes:
        for seq_len in seq_lengths:
            case_name = f"B{batch_size}_S{seq_len}"
            
            # 计算主要张量的内存使用
            tensors = {
                "embedding": ([batch_size, seq_len, model_config["hidden_size"]], 2),  # BF16
                "attention_qkv": ([batch_size, seq_len, 
                                 (model_config["num_attention_heads"] + 2 * model_config["num_key_value_heads"]) * model_config["head_dim"]], 1),  # INT8
                "attention_output": ([batch_size, seq_len, model_config["hidden_size"]], 2),  # BF16
                "mlp_gate_up": ([batch_size, seq_len, 2 * model_config["intermediate_size"]], 1),  # INT8
                "mlp_down": ([batch_size, seq_len, model_config["hidden_size"]], 2),  # BF16
                "kv_cache_per_layer": ([2, batch_size, model_config["num_key_value_heads"] // model_config["tp_size"], 
                                      seq_len, model_config["head_dim"]], 2),  # BF16
                "logits": ([batch_size, seq_len, model_config["vocab_size"] // model_config["tp_size"]], 2)  # BF16
            }
            
            total_memory_mb = 0
            tensor_details = {}
            
            for tensor_name, (shape, dtype_bytes) in tensors.items():
                memory_mb = calculate_tensor_memory(shape, dtype_bytes)
                if tensor_name == "kv_cache_per_layer":
                    memory_mb *= model_config["num_layers"]  # 所有层的KV cache
                
                tensor_details[tensor_name] = {
                    "shape": shape,
                    "memory_mb": memory_mb,
                    "dtype": "INT8" if dtype_bytes == 1 else "BF16"
                }
                total_memory_mb += memory_mb
            
            memory_analysis[case_name] = {
                "batch_size": batch_size,
                "seq_length": seq_len,
                "total_memory_mb": total_memory_mb,
                "total_memory_gb": total_memory_mb / 1024,
                "tensor_details": tensor_details
            }
    
    return memory_analysis

def generate_performance_report():
    """生成性能分析报告"""
    print("[PERF] 生成性能分析报告...")
    
    # 准备测试用例
    test_cases = [
        {
            "name": "short_generation",
            "prompt": "你好",
            "sampling_params": {"max_new_tokens": 5, "temperature": 0.7}
        },
        {
            "name": "medium_generation", 
            "prompt": "请用一句话介绍人工智能的发展。",
            "sampling_params": {"max_new_tokens": 15, "temperature": 0.7}
        },
        {
            "name": "long_generation",
            "prompt": "请详细解释深度学习在自然语言处理中的应用和发展趋势。",
            "sampling_params": {"max_new_tokens": 32, "temperature": 0.7}
        }
    ]
    
    try:
        # 1. 分析模型初始化
        llm = analyze_model_initialization()
        
        # 2. 详细算子分析 (新增)
        detailed_operator_analysis = analyze_detailed_operators(llm)
        
        # 3. 分析推理性能
        inference_results = analyze_inference_performance(llm, test_cases)
        
        # 4. 分析不同序列长度
        sequence_results = analyze_different_sequence_lengths(llm)
        
        # 4. 分析张量shape和内存
        memory_analysis = analyze_tensor_shapes_and_memory()
        
        # 5. 获取性能统计摘要
        performance_summary = profiler.get_summary()
        
        # 生成综合报告
        report = {
            "analysis_timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "model_info": {
                "model_path": MODEL_PATH,
                "quantization": "w8a8_int8",
                "tensor_parallel_size": 4
            },
            "initialization_analysis": "见performance_summary",
            "detailed_operator_analysis": detailed_operator_analysis,  # 新增
            "inference_results": inference_results,
            "sequence_length_analysis": sequence_results,
            "memory_analysis": memory_analysis,
            "performance_summary": performance_summary,
            "key_insights": {
                "prefill_vs_decode": "Prefill阶段处理整个序列，内存密集；Decode阶段逐token生成，延迟敏感",
                "quantization_impact": "W8A8量化显著减少内存使用，但需要量化/反量化开销",
                "tensor_parallel": "4-way TP分片减少单卡内存需求，但增加通信开销",
                "memory_scaling": "内存使用与batch_size和seq_length成正比，KV cache是主要开销",
                "detailed_operators": f"捕获到{detailed_operator_analysis['summary']['total_unique_modules']}个不同模块，{detailed_operator_analysis['summary']['total_calls']}次调用"
            }
        }
        
        return report
        
    except Exception as e:
        print(f"[PERF] 性能分析失败: {e}")
        traceback.print_exc()
        return {"error": str(e), "traceback": traceback.format_exc()}
    
    finally:
        if 'llm' in locals() and llm is not None:
            try:
                # 移除hooks
                profiler.remove_hooks()
                llm.shutdown()
                print("[PERF] 模型已关闭，hooks已清理")
            except Exception as e:
                print(f"[PERF] 关闭模型时出错: {e}")

def main():
    """主函数"""
    print("开始SGLang INT8模型性能分析...")
    
    try:
        # 生成性能分析报告
        report = generate_performance_report()
        
        # 保存报告
        output_file = "int8_model_performance_analysis.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"性能分析完成，报告已保存到: {output_file}")
        
        # 打印关键结果
        if "error" not in report:
            print("\n=== 关键性能指标 ===")
            
            # 详细算子统计
            if "detailed_operator_analysis" in report:
                detailed = report["detailed_operator_analysis"]
                print(f"\n详细算子分析:")
                print(f"  总模块数: {detailed['summary']['total_unique_modules']}")
                print(f"  总调用次数: {detailed['summary']['total_calls']}")
                print(f"  分析层数: {detailed['summary']['layers_analyzed']}")
                
                # 显示部分关键模块
                print(f"\n关键模块示例:")
                count = 0
                for module_name, details in detailed['module_details'].items():
                    if count < 10:  # 只显示前10个
                        print(f"  {module_name}: {details['module_type']} (调用{details['call_count']}次)")
                        count += 1
                    else:
                        break
                
                if len(detailed['module_details']) > 10:
                    print(f"  ... 还有{len(detailed['module_details']) - 10}个模块")
            
            if "sequence_length_analysis" in report:
                print("\n序列长度性能:")
                for seq_type, data in report["sequence_length_analysis"].items():
                    print(f"  {seq_type}: 平均 {data['avg_time_ms']:.2f}ms ± {data['std_time_ms']:.2f}ms")
            
            if "memory_analysis" in report:
                print("\n内存使用分析 (选择性展示):")
                for case, data in list(report["memory_analysis"].items())[:3]:
                    print(f"  {case}: {data['total_memory_gb']:.2f} GB")
            
            print("\n关键洞察:")
            for key, insight in report["key_insights"].items():
                print(f"  {key}: {insight}")
        else:
            print(f"分析失败: {report['error']}")
        
    except Exception as e:
        print(f"主程序执行失败: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    main()
