#!/usr/bin/env python3
"""
DeepSeek-V3 INT8 GPU内核监控脚本
使用CUDA事件和分析器来捕获实际的计算算子
"""

import os
import sys
import time
import json
import torch
import torch.profiler
import numpy as np
from typing import Dict, List, Any, Tuple
from collections import defaultdict
import traceback

# 设置环境变量
os.environ.setdefault("SGL_DISABLE_TP_MEMORY_INBALANCE_CHECK", "1")
os.environ.setdefault("UCX_VFS_ENABLE", "n")
os.environ.setdefault("NCCL_P2P_DISABLE", "1")
os.environ.setdefault("NCCL_IB_DISABLE", "1")
os.environ.setdefault("SGLANG_IS_FLASHINFER_AVAILABLE", "false")
os.environ.setdefault("CUDA_VISIBLE_DEVICES", "0")

import sglang as sgl

MODEL_PATH = os.environ.get("MODEL_PATH", "/home/<USER>/deepseek-int8")

class GPUKernelTracker:
    """GPU内核追踪器"""
    
    def __init__(self):
        self.kernel_stats = defaultdict(lambda: {
            'count': 0,
            'total_time': 0.0,
            'input_shapes': [],
            'stack_traces': [],
            'avg_time': 0.0,
            'kernel_type': '',
            'computation_category': ''
        })
        
        self.profiling_results = []
        self.test_case_results = {}
        
        # 内核分类映射
        self.kernel_categories = {
            'gemm': 'matrix_multiplication',
            'bmm': 'batch_matrix_multiplication',  
            'addmm': 'add_matrix_multiplication',
            'mm': 'matrix_multiplication',
            'conv': 'convolution',
            'softmax': 'attention_computation',
            'layer_norm': 'normalization',
            'rms_norm': 'normalization',
            'embedding': 'embedding_lookup',
            'gelu': 'activation_function',
            'relu': 'activation_function',
            'silu': 'activation_function',
            'add': 'element_wise_operation',
            'mul': 'element_wise_operation',
            'div': 'element_wise_operation',
            'copy': 'memory_operation',
            'cat': 'tensor_operation',
            'transpose': 'tensor_operation',
            'permute': 'tensor_operation',
            'view': 'tensor_operation',
            'reshape': 'tensor_operation'
        }
    
    def categorize_kernel(self, kernel_name: str) -> str:
        """对内核进行分类"""
        kernel_lower = kernel_name.lower()
        
        for pattern, category in self.kernel_categories.items():
            if pattern in kernel_lower:
                return category
        
        # 特殊模式匹配
        if 'int8' in kernel_lower or 'quantiz' in kernel_lower:
            return 'quantization_operation'
        elif 'flash' in kernel_lower or 'attention' in kernel_lower:
            return 'attention_computation'
        elif 'moe' in kernel_lower or 'expert' in kernel_lower:
            return 'moe_computation'
        elif 'triton' in kernel_lower:
            return 'triton_kernel'
        elif 'cutlass' in kernel_lower:
            return 'cutlass_kernel'
        
        return 'unknown_computation'
    
    def profile_inference(self, engine, test_case):
        """使用PyTorch Profiler分析推理过程"""
        print(f"[PROFILER] 开始分析测试用例: {test_case['name']}")
        
        # 配置profiler
        activities = [torch.profiler.ProfilerActivity.CPU, torch.profiler.ProfilerActivity.CUDA]
        
        with torch.profiler.profile(
            activities=activities,
            record_shapes=True,
            profile_memory=True,
            with_stack=True,
            with_flops=True,
            use_cuda=True
        ) as prof:
            start_time = time.time()
            
            # 执行推理
            result = engine.generate(
                prompt=test_case['prompt'],
                sampling_params={
                    "max_new_tokens": test_case['max_tokens'],
                    "temperature": 0.1,
                    "top_p": 0.9
                }
            )
            
            end_time = time.time()
        
        # 分析profiler结果
        self._analyze_profiler_results(prof, test_case['name'])
        
        return {
            'prompt': test_case['prompt'],
            'generated_text': result['text'],
            'execution_time': end_time - start_time,
            'max_tokens': test_case['max_tokens'],
            'description': test_case['description']
        }
    
    def _analyze_profiler_results(self, prof, test_case_name: str):
        """分析profiler结果"""
        print(f"[PROFILER] 分析 {test_case_name} 的profiler结果...")
        
        # 获取内核事件
        events = prof.events()
        kernel_events = [event for event in events if event.device_type == torch.profiler.DeviceType.CUDA]
        
        print(f"[PROFILER] 找到 {len(kernel_events)} 个CUDA内核事件")
        
        test_kernels = defaultdict(lambda: {
            'count': 0,
            'total_time': 0.0,
            'input_shapes': set(),
            'flops': 0
        })
        
        for event in kernel_events:
            kernel_name = event.name
            duration = event.duration
            
            # 分类内核
            category = self.categorize_kernel(kernel_name)
            
            # 更新全局统计
            self.kernel_stats[kernel_name]['count'] += 1
            self.kernel_stats[kernel_name]['total_time'] += duration
            self.kernel_stats[kernel_name]['kernel_type'] = kernel_name
            self.kernel_stats[kernel_name]['computation_category'] = category
            
            # 记录输入形状
            if hasattr(event, 'input_shapes') and event.input_shapes:
                shapes_str = str(event.input_shapes)
                if len(self.kernel_stats[kernel_name]['input_shapes']) < 10:
                    self.kernel_stats[kernel_name]['input_shapes'].append(shapes_str)
            
            # 更新测试用例特定统计
            test_kernels[kernel_name]['count'] += 1
            test_kernels[kernel_name]['total_time'] += duration
            
            if hasattr(event, 'flop') and event.flop:
                test_kernels[kernel_name]['flops'] += event.flop
                
            if hasattr(event, 'input_shapes') and event.input_shapes:
                test_kernels[kernel_name]['input_shapes'].add(str(event.input_shapes))
        
        # 保存测试用例结果
        self.test_case_results[test_case_name] = {
            'kernel_count': len(test_kernels),
            'total_kernels': sum(stats['count'] for stats in test_kernels.values()),
            'total_time': sum(stats['total_time'] for stats in test_kernels.values()),
            'kernels': dict(test_kernels)
        }
        
        # 转换set为list以便JSON序列化
        for kernel_name in test_kernels:
            test_kernels[kernel_name]['input_shapes'] = list(test_kernels[kernel_name]['input_shapes'])
    
    def run_comprehensive_analysis(self, engine):
        """运行综合分析"""
        test_cases = [
            {
                "name": "simple_query",
                "prompt": "你好吗？",
                "max_tokens": 5,
                "description": "简单问候查询"
            },
            {
                "name": "factual_question", 
                "prompt": "什么是人工智能？",
                "max_tokens": 15,
                "description": "事实性问题"
            },
            {
                "name": "reasoning_task",
                "prompt": "请解释机器学习的工作原理",
                "max_tokens": 25,
                "description": "推理任务"
            },
            {
                "name": "math_problem",
                "prompt": "计算 15 + 27 = ?",
                "max_tokens": 8,
                "description": "数学计算"
            }
        ]
        
        execution_results = {}
        
        for test_case in test_cases:
            try:
                print(f"\n[ANALYSIS] 执行测试: {test_case['name']}")
                result = self.profile_inference(engine, test_case)
                execution_results[test_case['name']] = result
                
                print(f"[ANALYSIS] 生成结果: {result['generated_text'][:60]}...")
                print(f"[ANALYSIS] 执行时间: {result['execution_time']:.3f}s")
                
            except Exception as e:
                print(f"[ANALYSIS] 测试失败 {test_case['name']}: {e}")
                execution_results[test_case['name']] = {
                    'error': str(e),
                    'execution_time': 0
                }
        
        return execution_results
    
    def generate_analysis_report(self, execution_results: Dict) -> Dict:
        """生成分析报告"""
        print("[ANALYSIS] 生成综合分析报告...")
        
        # 计算平均时间
        for kernel_name, stats in self.kernel_stats.items():
            if stats['count'] > 0:
                stats['avg_time'] = stats['total_time'] / stats['count']
        
        # 按计算类别分组
        category_stats = defaultdict(lambda: {
            'kernel_count': 0,
            'total_calls': 0,
            'total_time': 0.0,
            'kernels': []
        })
        
        for kernel_name, stats in self.kernel_stats.items():
            category = stats['computation_category']
            category_stats[category]['kernel_count'] += 1
            category_stats[category]['total_calls'] += stats['count']
            category_stats[category]['total_time'] += stats['total_time']
            category_stats[category]['kernels'].append(kernel_name)
        
        # 性能分析
        total_kernels = len(self.kernel_stats)
        total_calls = sum(stats['count'] for stats in self.kernel_stats.values())
        total_time = sum(stats['total_time'] for stats in self.kernel_stats.values())
        
        # 最消耗时间的内核
        top_time_kernels = sorted(
            self.kernel_stats.items(),
            key=lambda x: x[1]['total_time'],
            reverse=True
        )[:20]
        
        # 最频繁调用的内核
        top_freq_kernels = sorted(
            self.kernel_stats.items(),
            key=lambda x: x[1]['count'],
            reverse=True
        )[:20]
        
        report = {
            'analysis_summary': {
                'timestamp': time.time(),
                'total_unique_kernels': total_kernels,
                'total_kernel_calls': total_calls,
                'total_execution_time_us': total_time,
                'analysis_method': 'pytorch_profiler_gpu_kernel_tracing',
                'average_time_per_call_us': total_time / total_calls if total_calls > 0 else 0
            },
            'test_execution_results': execution_results,
            'test_case_kernel_breakdown': self.test_case_results,
            'computation_category_breakdown': dict(category_stats),
            'detailed_kernel_analysis': dict(self.kernel_stats),
            'performance_rankings': {
                'top_time_consuming_kernels': [(name, stats) for name, stats in top_time_kernels],
                'most_frequently_called_kernels': [(name, stats) for name, stats in top_freq_kernels]
            },
            'quantization_analysis': self._analyze_quantization_kernels(),
            'attention_analysis': self._analyze_attention_kernels(),
            'memory_analysis': self._analyze_memory_operations()
        }
        
        return report
    
    def _analyze_quantization_kernels(self) -> Dict:
        """分析量化相关内核"""
        quant_kernels = {}
        quant_stats = {
            'total_kernels': 0,
            'total_calls': 0,
            'total_time': 0.0
        }
        
        for kernel_name, stats in self.kernel_stats.items():
            if 'int8' in kernel_name.lower() or 'quantiz' in kernel_name.lower():
                quant_kernels[kernel_name] = stats
                quant_stats['total_kernels'] += 1
                quant_stats['total_calls'] += stats['count']
                quant_stats['total_time'] += stats['total_time']
        
        return {
            'quantization_kernels': quant_kernels,
            'quantization_summary': quant_stats
        }
    
    def _analyze_attention_kernels(self) -> Dict:
        """分析注意力机制相关内核"""
        attn_kernels = {}
        attn_stats = {
            'total_kernels': 0,
            'total_calls': 0, 
            'total_time': 0.0
        }
        
        for kernel_name, stats in self.kernel_stats.items():
            if any(pattern in kernel_name.lower() for pattern in ['attention', 'flash', 'softmax']):
                attn_kernels[kernel_name] = stats
                attn_stats['total_kernels'] += 1
                attn_stats['total_calls'] += stats['count']
                attn_stats['total_time'] += stats['total_time']
        
        return {
            'attention_kernels': attn_kernels,
            'attention_summary': attn_stats
        }
    
    def _analyze_memory_operations(self) -> Dict:
        """分析内存操作"""
        memory_kernels = {}
        memory_stats = {
            'total_kernels': 0,
            'total_calls': 0,
            'total_time': 0.0
        }
        
        for kernel_name, stats in self.kernel_stats.items():
            if any(pattern in kernel_name.lower() for pattern in ['copy', 'memcpy', 'memset']):
                memory_kernels[kernel_name] = stats
                memory_stats['total_kernels'] += 1
                memory_stats['total_calls'] += stats['count']
                memory_stats['total_time'] += stats['total_time']
        
        return {
            'memory_kernels': memory_kernels,
            'memory_summary': memory_stats
        }

def run_gpu_kernel_analysis():
    """运行GPU内核分析"""
    print("="*80)
    print("DeepSeek-V3 INT8 GPU内核追踪分析")
    print("="*80)
    
    tracker = GPUKernelTracker()
    engine = None
    
    try:
        # 1. 初始化SGLang引擎
        print("\n[MAIN] 步骤1: 初始化SGLang引擎...")
        engine = sgl.Engine(
            model_path=MODEL_PATH,
            tp_size=1,
            quantization="w8a8_int8",
            trust_remote_code=True,
            mem_fraction_static=0.6,
            disable_cuda_graph=True,
            log_level="warning",
        )
        
        print("[MAIN] 模型加载成功")
        
        # 2. 执行推理并分析GPU内核
        print("\n[MAIN] 步骤2: 执行推理分析并追踪GPU内核...")
        execution_results = tracker.run_comprehensive_analysis(engine)
        
        # 3. 生成综合报告
        print("\n[MAIN] 步骤3: 生成综合分析报告...")
        report = tracker.generate_analysis_report(execution_results)
        
        # 4. 保存报告
        timestamp = int(time.time())
        
        # JSON报告
        json_file = f"gpu_kernel_analysis_{timestamp}.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False, default=str)
        print(f"[MAIN] JSON报告已保存: {json_file}")
        
        # Markdown报告
        md_file = f"gpu_kernel_analysis_{timestamp}.md"
        generate_gpu_markdown_report(report, md_file)
        print(f"[MAIN] Markdown报告已保存: {md_file}")
        
        # 打印关键结果
        print_gpu_analysis_summary(report)
        
    except Exception as e:
        print(f"[MAIN] 分析过程出错: {e}")
        traceback.print_exc()
    
    finally:
        try:
            if engine:
                engine.shutdown()
                print("[MAIN] 引擎已关闭")
        except:
            pass

def generate_gpu_markdown_report(report: Dict, filename: str):
    """生成GPU内核分析的Markdown报告"""
    summary = report['analysis_summary']
    
    content = f"""# DeepSeek-V3 INT8 GPU内核追踪分析报告

## 📊 分析概要

| 指标 | 值 |
|------|---|
| 分析时间 | {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(summary['timestamp']))} |
| 唯一内核数 | {summary['total_unique_kernels']} |
| 总内核调用次数 | {summary['total_kernel_calls']} |
| 总执行时间 | {summary['total_execution_time_us']:.1f} μs |
| 平均每次调用时间 | {summary['average_time_per_call_us']:.2f} μs |
| 分析方法 | {summary['analysis_method']} |

## 🧪 测试执行结果

"""
    
    for test_name, result in report['test_execution_results'].items():
        if 'error' in result:
            content += f"### ❌ {test_name}\n**错误**: {result['error']}\n\n"
        else:
            content += f"""### ✅ {test_name}
- **描述**: {result.get('description', 'N/A')}
- **提示**: {result['prompt']}
- **生成文本**: {result['generated_text'][:80]}...
- **执行时间**: {result['execution_time']:.3f}s
- **最大tokens**: {result['max_tokens']}

"""
    
    content += """## 🔧 计算类别分析

| 计算类别 | 内核数 | 总调用次数 | 总时间(μs) | 平均时间(μs) |
|----------|--------|------------|------------|--------------|
"""
    
    for category, stats in report['computation_category_breakdown'].items():
        avg_time = stats['total_time'] / stats['total_calls'] if stats['total_calls'] > 0 else 0
        content += f"| {category} | {stats['kernel_count']} | {stats['total_calls']} | {stats['total_time']:.1f} | {avg_time:.2f} |\n"
    
    content += """

## ⏱️ 最耗时的内核 (Top 10)

| 排名 | 内核名称 | 总时间(μs) | 调用次数 | 平均时间(μs) | 计算类别 |
|------|----------|------------|----------|--------------|----------|
"""
    
    for idx, (kernel_name, stats) in enumerate(report['performance_rankings']['top_time_consuming_kernels'][:10], 1):
        short_name = kernel_name[:50] + "..." if len(kernel_name) > 50 else kernel_name
        content += f"| {idx} | {short_name} | {stats['total_time']:.1f} | {stats['count']} | {stats['avg_time']:.2f} | {stats['computation_category']} |\n"
    
    content += """

## 🔥 最频繁调用的内核 (Top 10)

| 排名 | 内核名称 | 调用次数 | 总时间(μs) | 平均时间(μs) | 计算类别 |
|------|----------|----------|------------|--------------|----------|
"""
    
    for idx, (kernel_name, stats) in enumerate(report['performance_rankings']['most_frequently_called_kernels'][:10], 1):
        short_name = kernel_name[:50] + "..." if len(kernel_name) > 50 else kernel_name
        content += f"| {idx} | {short_name} | {stats['count']} | {stats['total_time']:.1f} | {stats['avg_time']:.2f} | {stats['computation_category']} |\n"
    
    # 量化分析
    quant = report['quantization_analysis']['quantization_summary']
    content += f"""

## ⚖️ 量化操作分析

- **量化相关内核数**: {quant['total_kernels']}
- **量化操作调用次数**: {quant['total_calls']}
- **量化操作总时间**: {quant['total_time']:.1f} μs
"""
    
    if quant['total_calls'] > 0:
        content += f"- **量化操作平均时间**: {quant['total_time'] / quant['total_calls']:.2f} μs\n"
    
    # 注意力分析
    attn = report['attention_analysis']['attention_summary']
    content += f"""

## 🎯 注意力机制分析

- **注意力相关内核数**: {attn['total_kernels']}
- **注意力操作调用次数**: {attn['total_calls']}
- **注意力操作总时间**: {attn['total_time']:.1f} μs
"""
    
    if attn['total_calls'] > 0:
        content += f"- **注意力操作平均时间**: {attn['total_time'] / attn['total_calls']:.2f} μs\n"
    
    # 按测试用例分析
    content += """

## 📈 按测试用例的内核分析

| 测试用例 | 唯一内核数 | 总调用次数 | 总时间(μs) |
|----------|------------|------------|------------|
"""
    
    for test_case, stats in report['test_case_kernel_breakdown'].items():
        content += f"| {test_case} | {stats['kernel_count']} | {stats['total_kernels']} | {stats['total_time']:.1f} |\n"
    
    content += f"""

## 📝 分析总结

本次分析通过PyTorch Profiler成功捕获了DeepSeek-V3 INT8模型在实际推理过程中的所有GPU内核调用。主要发现：

### 🔍 性能特征
1. **内核多样性**: 共发现 {summary['total_unique_kernels']} 种不同的GPU内核
2. **调用密集度**: 总计 {summary['total_kernel_calls']} 次内核调用
3. **执行效率**: 平均每次内核调用耗时 {summary['average_time_per_call_us']:.2f} μs

### ⚡ 性能热点
- **量化操作**: 占总时间的 {(quant['total_time'] / summary['total_execution_time_us'] * 100):.1f}%
- **注意力计算**: 占总时间的 {(attn['total_time'] / summary['total_execution_time_us'] * 100):.1f}%

### 🎯 优化建议
1. 重点优化最耗时的内核以提升整体性能
2. 关注量化操作的效率，这是INT8模型的关键
3. 优化注意力机制的计算，减少内存带宽需求

---
*报告生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}*
*分析工具: PyTorch Profiler + CUDA事件追踪*
"""
    
    with open(filename, 'w', encoding='utf-8') as f:
        f.write(content)

def print_gpu_analysis_summary(report: Dict):
    """打印GPU分析摘要"""
    summary = report['analysis_summary']
    
    print("\n" + "="*80)
    print("🎯 GPU内核追踪分析结果")
    print("="*80)
    
    print(f"✅ 内核追踪完成！")
    print(f"   🔧 唯一内核数: {summary['total_unique_kernels']}")
    print(f"   📞 总调用次数: {summary['total_kernel_calls']}")
    print(f"   ⏱️  总执行时间: {summary['total_execution_time_us']:.1f} μs")
    print(f"   📊 平均调用时间: {summary['average_time_per_call_us']:.2f} μs")
    
    # 测试结果总结
    test_results = report['test_execution_results']
    successful = len([r for r in test_results.values() if 'error' not in r])
    total_time = sum(r.get('execution_time', 0) for r in test_results.values())
    
    print(f"\n🧪 测试执行总结:")
    print(f"   ✅ 成功: {successful}/{len(test_results)}")
    print(f"   ⏱️  总推理时间: {total_time:.3f}s")
    
    # 最耗时内核
    print(f"\n🔥 最耗时的内核 (Top 3):")
    top_time = report['performance_rankings']['top_time_consuming_kernels'][:3]
    for idx, (kernel_name, stats) in enumerate(top_time, 1):
        short_name = kernel_name[:40] + "..." if len(kernel_name) > 40 else kernel_name
        print(f"   {idx}. {short_name}")
        print(f"      ⏱️  {stats['total_time']:.1f} μs ({stats['count']} 次调用)")
    
    # 计算类别分布
    print(f"\n🧮 主要计算类别:")
    sorted_categories = sorted(
        report['computation_category_breakdown'].items(),
        key=lambda x: x[1]['total_time'],
        reverse=True
    )[:3]
    
    for category, stats in sorted_categories:
        percentage = (stats['total_time'] / summary['total_execution_time_us']) * 100
        print(f"   - {category}: {stats['total_time']:.1f} μs ({percentage:.1f}%)")
    
    # 量化和注意力分析
    quant_summary = report['quantization_analysis']['quantization_summary']
    attn_summary = report['attention_analysis']['attention_summary']
    
    print(f"\n⚖️  量化操作: {quant_summary['total_kernels']} 内核, {quant_summary['total_calls']} 调用")
    print(f"🎯 注意力操作: {attn_summary['total_kernels']} 内核, {attn_summary['total_calls']} 调用")
    
    print(f"\n✨ 分析完成！详细报告已保存为JSON和Markdown格式。")

if __name__ == "__main__":
    run_gpu_kernel_analysis()
