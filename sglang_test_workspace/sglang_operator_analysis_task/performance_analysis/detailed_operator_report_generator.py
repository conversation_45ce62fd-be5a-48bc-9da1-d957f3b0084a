#!/usr/bin/env python3
"""
详细算子分析报告生成器
基于捕获的算子调用数据生成详细报告
"""

import json
import time
from collections import defaultdict

def generate_detailed_operator_markdown_report(analysis_data, output_file="detailed_operator_analysis_report.md"):
    """生成详细的算子分析Markdown报告"""
    
    if "detailed_operator_analysis" not in analysis_data:
        print("错误: 分析数据中没有详细算子信息")
        return
    
    detailed = analysis_data["detailed_operator_analysis"]
    
    markdown_content = f"""# SGLang INT8模型详细算子分析报告

## 分析概述

**分析时间**: {analysis_data.get('analysis_timestamp', 'Unknown')}
**模型**: {analysis_data.get('model_info', {}).get('model_path', 'Unknown')}
**量化方案**: {analysis_data.get('model_info', {}).get('quantization', 'Unknown')}

### 统计汇总

- **总模块数**: {detailed['summary']['total_unique_modules']}
- **总调用次数**: {detailed['summary']['total_calls']}
- **分析层数**: {detailed['summary']['layers_analyzed']}
- **平均每模块调用次数**: {detailed['summary']['total_calls'] / max(detailed['summary']['total_unique_modules'], 1):.1f}

## 详细模块列表

### 按模块名称排序

| 模块名称 | 模块类型 | 调用次数 | 输入Shape | 输出Shape |
|----------|----------|----------|-----------|-----------|
"""
    
    # 按模块名称排序并添加到表格
    sorted_modules = sorted(detailed['module_details'].items())
    
    for module_name, details in sorted_modules:
        input_shapes = _format_shapes(details.get('input_shapes', []))
        output_shapes = _format_shapes(details.get('output_shapes', []))
        
        markdown_content += f"| `{module_name}` | {details['module_type']} | {details['call_count']} | {input_shapes} | {output_shapes} |\n"
    
    # 按层级分解
    markdown_content += f"""

## 按层级分解分析

"""
    
    if detailed.get('layer_breakdown'):
        for layer_name in sorted(detailed['layer_breakdown'].keys(), key=lambda x: int(x.split('_')[1]) if x.split('_')[1].isdigit() else 0):
            layer_info = detailed['layer_breakdown'][layer_name]
            
            markdown_content += f"""
### {layer_name.upper()}

"""
            
            # Attention模块
            if layer_info.get('attention_modules'):
                markdown_content += f"""
#### Attention模块 ({len(layer_info['attention_modules'])}个)

| 模块名称 | 类型 | 输入Shape | 输出Shape |
|----------|------|-----------|-----------|
"""
                for module in layer_info['attention_modules']:
                    input_shapes = _format_shapes(module['shapes']['input'])
                    output_shapes = _format_shapes(module['shapes']['output'])
                    markdown_content += f"| `{module['module_name']}` | {module['module_type']} | {input_shapes} | {output_shapes} |\n"
            
            # MLP模块
            if layer_info.get('mlp_modules'):
                markdown_content += f"""

#### MLP模块 ({len(layer_info['mlp_modules'])}个)

| 模块名称 | 类型 | 输入Shape | 输出Shape |
|----------|------|-----------|-----------|
"""
                for module in layer_info['mlp_modules']:
                    input_shapes = _format_shapes(module['shapes']['input'])
                    output_shapes = _format_shapes(module['shapes']['output'])
                    markdown_content += f"| `{module['module_name']}` | {module['module_type']} | {input_shapes} | {output_shapes} |\n"
            
            # MoE模块
            if layer_info.get('moe_modules'):
                markdown_content += f"""

#### MoE模块 ({len(layer_info['moe_modules'])}个)

| 模块名称 | 类型 | 输入Shape | 输出Shape |
|----------|------|-----------|-----------|
"""
                for module in layer_info['moe_modules']:
                    input_shapes = _format_shapes(module['shapes']['input'])
                    output_shapes = _format_shapes(module['shapes']['output'])
                    markdown_content += f"| `{module['module_name']}` | {module['module_type']} | {input_shapes} | {output_shapes} |\n"
            
            # 其他模块
            if layer_info.get('other_modules'):
                markdown_content += f"""

#### 其他模块 ({len(layer_info['other_modules'])}个)

| 模块名称 | 类型 | 输入Shape | 输出Shape |
|----------|------|-----------|-----------|
"""
                for module in layer_info['other_modules']:
                    input_shapes = _format_shapes(module['shapes']['input'])
                    output_shapes = _format_shapes(module['shapes']['output'])
                    markdown_content += f"| `{module['module_name']}` | {module['module_type']} | {input_shapes} | {output_shapes} |\n"
    
    # 模块类型统计
    markdown_content += f"""

## 模块类型统计

"""
    
    # 统计模块类型
    type_stats = defaultdict(int)
    for details in detailed['module_details'].values():
        type_stats[details['module_type']] += details['call_count']
    
    markdown_content += f"""
| 模块类型 | 调用次数 | 占比 |
|----------|----------|------|
"""
    
    total_calls = sum(type_stats.values())
    for module_type, call_count in sorted(type_stats.items(), key=lambda x: x[1], reverse=True):
        percentage = (call_count / total_calls * 100) if total_calls > 0 else 0
        markdown_content += f"| {module_type} | {call_count} | {percentage:.1f}% |\n"
    
    # 关键发现
    markdown_content += f"""

## 关键发现

### 计算密集模块
基于调用次数，以下是最活跃的模块：

"""
    
    # 按调用次数排序
    sorted_by_calls = sorted(detailed['module_details'].items(), 
                           key=lambda x: x[1]['call_count'], reverse=True)
    
    for i, (module_name, details) in enumerate(sorted_by_calls[:10]):
        markdown_content += f"{i+1}. **{module_name}**: {details['module_type']} (调用{details['call_count']}次)\n"
    
    # 算子Shape分析
    markdown_content += f"""

### Shape变换模式

基于捕获的Shape数据，主要的变换模式：

"""
    
    # 分析常见的Shape模式
    shape_patterns = _analyze_shape_patterns(detailed['module_details'])
    
    for pattern, modules in shape_patterns.items():
        markdown_content += f"- **{pattern}**: {len(modules)}个模块\n"
        for module in modules[:3]:  # 只显示前3个示例
            markdown_content += f"  - `{module}`\n"
        if len(modules) > 3:
            markdown_content += f"  - ... 还有{len(modules)-3}个模块\n"
    
    markdown_content += f"""

## 优化建议

基于详细的算子分析，建议的优化方向：

### 1. 算子融合机会
- **线性层融合**: 相邻的Linear模块可以考虑融合
- **激活函数融合**: 激活函数可以与前一层融合
- **注意力融合**: QKV投影可以融合为单个操作

### 2. 内存优化
- **中间结果重用**: 相同Shape的中间结果可以复用内存
- **梯度检查点**: 对于大型中间激活使用重计算策略

### 3. 并行优化
- **模块并行**: 某些独立的模块可以并行执行
- **流水线优化**: 优化模块间的数据流

---

*报告生成时间: {time.strftime("%Y-%m-%d %H:%M:%S")}*
*基于SGLang实际运行数据*
"""
    
    # 保存报告
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(markdown_content)
    
    print(f"详细算子分析报告已保存到: {output_file}")

def _format_shapes(shapes):
    """格式化shape信息"""
    if not shapes:
        return "N/A"
    
    formatted = []
    for shape_info in shapes:
        if isinstance(shape_info, dict):
            for key, value in shape_info.items():
                if isinstance(value, list):
                    formatted.append(f"{key}:{value}")
                else:
                    formatted.append(f"{key}:{value}")
        else:
            formatted.append(str(shape_info))
    
    return " | ".join(formatted) if formatted else "N/A"

def _analyze_shape_patterns(module_details):
    """分析Shape变换模式"""
    patterns = defaultdict(list)
    
    for module_name, details in module_details.items():
        # 简化的模式分析
        module_type = details['module_type']
        
        if module_type == "Linear":
            patterns["线性变换"].append(module_name)
        elif "Conv" in module_type:
            patterns["卷积变换"].append(module_name)
        elif "Norm" in module_type:
            patterns["归一化"].append(module_name)
        elif "Attention" in module_type:
            patterns["注意力计算"].append(module_name)
        elif "Embedding" in module_type:
            patterns["嵌入层"].append(module_name)
        else:
            patterns["其他变换"].append(module_name)
    
    return patterns

if __name__ == "__main__":
    print("详细算子分析报告生成器")
    print("请先运行int8_performance_profiler.py生成分析数据")
