#!/usr/bin/env python3
"""
DeepSeek-V3 INT8 精确算子追踪脚本
统计实际推理过程中出现的所有运算相关算子，精确到每一层的每个模块的每个计算
例如: layer0.attn.qa_proj, layer1.mlp.gate_proj 等
"""

import os
import sys
import time
import json
import torch
import numpy as np
from typing import Dict, List, Any, Tuple, Set
from collections import defaultdict, OrderedDict
from contextlib import contextmanager
import traceback

# 设置环境变量
os.environ.setdefault("SGL_DISABLE_TP_MEMORY_INBALANCE_CHECK", "1")
os.environ.setdefault("UCX_VFS_ENABLE", "n")
os.environ.setdefault("NCCL_P2P_DISABLE", "1")
os.environ.setdefault("NCCL_IB_DISABLE", "1")
os.environ.setdefault("SGLANG_IS_FLASHINFER_AVAILABLE", "false")
os.environ.setdefault("CUDA_VISIBLE_DEVICES", "0")

import sglang as sgl

MODEL_PATH = os.environ.get("MODEL_PATH", "/home/<USER>/deepseek-int8")

class PreciseOperatorTracker:
    """精确的算子追踪器"""
    
    def __init__(self):
        # 核心追踪数据
        self.operator_calls = defaultdict(list)  # 每个算子的调用记录
        self.layer_module_mapping = {}  # 层-模块映射
        self.operation_sequence = []  # 操作序列
        self.tensor_flows = defaultdict(list)  # 张量流向
        self.computation_graph = defaultdict(list)  # 计算图
        
        # 统计数据
        self.call_counts = defaultdict(int)
        self.shape_statistics = defaultdict(set)
        self.timing_data = defaultdict(list)
        
        # Hook管理
        self.hooks = []
        self.current_inference_id = 0
        
        # 特殊关注的算子类型
        self.target_ops = {
            'Linear', 'Conv1d', 'Conv2d', 'Embedding', 
            'LayerNorm', 'RMSNorm', 'GroupNorm',
            'MultiheadAttention', 'Attention', 'SelfAttention',
            'GELU', 'ReLU', 'SiLU', 'Sigmoid', 'Tanh',
            'Dropout', 'BatchNorm1d', 'BatchNorm2d',
            'AdaptiveAvgPool1d', 'AdaptiveAvgPool2d',
            'Softmax', 'LogSoftmax'
        }
    
    def clear_data(self):
        """清理所有追踪数据"""
        self.operator_calls.clear()
        self.layer_module_mapping.clear()
        self.operation_sequence.clear()
        self.tensor_flows.clear()
        self.computation_graph.clear()
        self.call_counts.clear()
        self.shape_statistics.clear()
        self.timing_data.clear()
        self.current_inference_id += 1
    
    def install_precise_hooks(self, model):
        """安装精确的算子追踪hooks"""
        print(f"[TRACKER] 开始安装精确算子追踪hooks...")
        
        hook_count = 0
        for full_name, module in model.named_modules():
            if full_name == "":  # 跳过根模块
                continue
                
            # 记录层-模块映射
            self._parse_layer_module_structure(full_name, module)
            
            # 为所有模块安装hook（不仅仅是有参数的）
            hook = module.register_forward_hook(self._create_precise_hook(full_name, module))
            self.hooks.append(hook)
            hook_count += 1
            
            # 打印前20个重要模块
            if hook_count <= 20:
                print(f"[TRACKER] Hook {hook_count:2d}: {full_name} -> {type(module).__name__}")
        
        print(f"[TRACKER] 总共安装了 {hook_count} 个精确追踪hooks")
        return hook_count
    
    def _parse_layer_module_structure(self, full_name: str, module):
        """解析层-模块结构"""
        parts = full_name.split('.')
        
        # 寻找层编号
        layer_num = None
        layer_type = None
        
        for i, part in enumerate(parts):
            if part == 'layers' and i + 1 < len(parts) and parts[i + 1].isdigit():
                layer_num = int(parts[i + 1])
                layer_type = 'transformer_layer'
                break
            elif part.startswith('layer') and part[5:].isdigit():
                layer_num = int(part[5:])
                layer_type = 'transformer_layer'
                break
        
        if layer_num is not None:
            layer_key = f"layer_{layer_num}"
            if layer_key not in self.layer_module_mapping:
                self.layer_module_mapping[layer_key] = {
                    'layer_number': layer_num,
                    'layer_type': layer_type,
                    'modules': {},
                    'attention_modules': [],
                    'mlp_modules': [],
                    'moe_modules': [],
                    'norm_modules': [],
                    'other_modules': []
                }
            
            # 分类模块
            module_info = {
                'full_name': full_name,
                'module_type': type(module).__name__,
                'has_parameters': len(list(module.parameters())) > 0,
                'parameter_count': sum(p.numel() for p in module.parameters())
            }
            
            self.layer_module_mapping[layer_key]['modules'][full_name] = module_info
            
            # 按功能分类
            lower_name = full_name.lower()
            if any(kw in lower_name for kw in ['attn', 'attention', 'self_attn']):
                self.layer_module_mapping[layer_key]['attention_modules'].append(full_name)
            elif any(kw in lower_name for kw in ['mlp', 'feed_forward', 'ffn']):
                self.layer_module_mapping[layer_key]['mlp_modules'].append(full_name)
            elif any(kw in lower_name for kw in ['moe', 'expert', 'gate']):
                self.layer_module_mapping[layer_key]['moe_modules'].append(full_name)
            elif any(kw in lower_name for kw in ['norm', 'layernorm', 'rmsnorm']):
                self.layer_module_mapping[layer_key]['norm_modules'].append(full_name)
            else:
                self.layer_module_mapping[layer_key]['other_modules'].append(full_name)
    
    def _create_precise_hook(self, module_name: str, module):
        """创建精确的追踪hook"""
        def precise_hook_fn(module, input, output):
            try:
                start_time = time.perf_counter()
                
                # 构建操作记录
                operation_record = {
                    'inference_id': self.current_inference_id,
                    'module_name': module_name,
                    'module_type': type(module).__name__,
                    'timestamp': time.time(),
                    'call_order': len(self.operation_sequence),
                    'input_info': self._extract_tensor_info(input, 'input'),
                    'output_info': self._extract_tensor_info(output, 'output'),
                    'parameters': self._extract_parameter_info(module),
                    'computation_type': self._classify_computation_type(module_name, module),
                    'memory_footprint': self._estimate_memory_footprint(input, output)
                }
                
                end_time = time.perf_counter()
                operation_record['hook_overhead_ms'] = (end_time - start_time) * 1000
                
                # 记录到各种数据结构
                self.operator_calls[module_name].append(operation_record)
                self.operation_sequence.append(operation_record)
                self.call_counts[module_name] += 1
                
                # 记录tensor shapes用于统计
                for shape_info in operation_record['input_info']['shapes']:
                    if shape_info != 'unknown':
                        self.shape_statistics[f"{module_name}_input"].add(tuple(shape_info))
                
                for shape_info in operation_record['output_info']['shapes']:
                    if shape_info != 'unknown':
                        self.shape_statistics[f"{module_name}_output"].add(tuple(shape_info))
                
                # 记录张量流向
                self._record_tensor_flow(operation_record)
                
            except Exception as e:
                # 静默处理错误，避免影响推理
                pass
        
        return precise_hook_fn
    
    def _extract_tensor_info(self, tensors, tensor_type: str) -> Dict:
        """提取张量信息"""
        info = {
            'type': tensor_type,
            'count': 0,
            'shapes': [],
            'dtypes': [],
            'devices': [],
            'total_elements': 0,
            'memory_mb': 0.0
        }
        
        try:
            if tensors is None:
                return info
            
            tensor_list = []
            if isinstance(tensors, torch.Tensor):
                tensor_list = [tensors]
            elif isinstance(tensors, (list, tuple)):
                tensor_list = [t for t in tensors if isinstance(t, torch.Tensor)]
            
            info['count'] = len(tensor_list)
            
            for tensor in tensor_list:
                if hasattr(tensor, 'shape'):
                    info['shapes'].append(list(tensor.shape))
                    info['total_elements'] += tensor.numel()
                    
                    # 估算内存使用
                    dtype_size = tensor.element_size() if hasattr(tensor, 'element_size') else 4
                    info['memory_mb'] += (tensor.numel() * dtype_size) / (1024 * 1024)
                    
                if hasattr(tensor, 'dtype'):
                    info['dtypes'].append(str(tensor.dtype))
                    
                if hasattr(tensor, 'device'):
                    info['devices'].append(str(tensor.device))
                else:
                    info['shapes'].append('unknown')
                    
        except Exception:
            info['shapes'] = ['error']
            
        return info
    
    def _extract_parameter_info(self, module) -> Dict:
        """提取模块参数信息"""
        param_info = {
            'parameter_count': 0,
            'parameter_shapes': [],
            'parameter_dtypes': [],
            'trainable_params': 0,
            'frozen_params': 0
        }
        
        try:
            for param_name, param in module.named_parameters(recurse=False):
                param_info['parameter_count'] += param.numel()
                param_info['parameter_shapes'].append({
                    'name': param_name,
                    'shape': list(param.shape),
                    'dtype': str(param.dtype),
                    'requires_grad': param.requires_grad
                })
                param_info['parameter_dtypes'].append(str(param.dtype))
                
                if param.requires_grad:
                    param_info['trainable_params'] += param.numel()
                else:
                    param_info['frozen_params'] += param.numel()
                    
        except Exception:
            pass
            
        return param_info
    
    def _classify_computation_type(self, module_name: str, module) -> str:
        """分类计算类型"""
        module_type = type(module).__name__
        lower_name = module_name.lower()
        
        if 'linear' in module_type.lower():
            if any(kw in lower_name for kw in ['q_proj', 'k_proj', 'v_proj', 'qkv']):
                return 'attention_projection'
            elif any(kw in lower_name for kw in ['o_proj', 'out_proj']):
                return 'attention_output'
            elif any(kw in lower_name for kw in ['gate_proj', 'gate']):
                return 'mlp_gate'
            elif any(kw in lower_name for kw in ['up_proj', 'up']):
                return 'mlp_up'
            elif any(kw in lower_name for kw in ['down_proj', 'down']):
                return 'mlp_down'
            else:
                return 'linear_transformation'
        elif 'norm' in module_type.lower():
            return 'normalization'
        elif 'embedding' in module_type.lower():
            return 'embedding_lookup'
        elif any(act in module_type.lower() for act in ['gelu', 'relu', 'silu', 'sigmoid']):
            return 'activation'
        elif 'attention' in module_type.lower():
            return 'attention_computation'
        elif 'dropout' in module_type.lower():
            return 'regularization'
        else:
            return 'other_computation'
    
    def _estimate_memory_footprint(self, input_tensors, output_tensors) -> Dict:
        """估算内存占用"""
        footprint = {
            'input_memory_mb': 0.0,
            'output_memory_mb': 0.0,
            'total_memory_mb': 0.0
        }
        
        try:
            # 计算输入内存
            if input_tensors is not None:
                if isinstance(input_tensors, torch.Tensor):
                    footprint['input_memory_mb'] = (input_tensors.numel() * input_tensors.element_size()) / (1024 * 1024)
                elif isinstance(input_tensors, (list, tuple)):
                    for tensor in input_tensors:
                        if isinstance(tensor, torch.Tensor):
                            footprint['input_memory_mb'] += (tensor.numel() * tensor.element_size()) / (1024 * 1024)
            
            # 计算输出内存
            if output_tensors is not None:
                if isinstance(output_tensors, torch.Tensor):
                    footprint['output_memory_mb'] = (output_tensors.numel() * output_tensors.element_size()) / (1024 * 1024)
                elif isinstance(output_tensors, (list, tuple)):
                    for tensor in output_tensors:
                        if isinstance(tensor, torch.Tensor):
                            footprint['output_memory_mb'] += (tensor.numel() * tensor.element_size()) / (1024 * 1024)
            
            footprint['total_memory_mb'] = footprint['input_memory_mb'] + footprint['output_memory_mb']
            
        except Exception:
            pass
            
        return footprint
    
    def _record_tensor_flow(self, operation_record):
        """记录张量流向"""
        module_name = operation_record['module_name']
        flow_record = {
            'from_module': module_name,
            'input_shapes': operation_record['input_info']['shapes'],
            'output_shapes': operation_record['output_info']['shapes'],
            'computation_type': operation_record['computation_type'],
            'call_order': operation_record['call_order']
        }
        self.tensor_flows[module_name].append(flow_record)
    
    def setup_inference_hooks(self, engine) -> bool:
        """通过推理过程设置hooks"""
        try:
            print("[TRACKER] 设置推理过程hooks...")
            
            # 保存tracker引用
            tracker_ref = self
            
            # 尝试patch PyTorch的forward方法
            original_forward = torch.nn.Module.forward
            
            def patched_forward(module_self, *args, **kwargs):
                # 获取模块的完整名称
                module_name = getattr(module_self, '_sglang_module_name', f"unknown_{type(module_self).__name__}")
                
                # 记录调用
                tracker_ref._record_patched_call(module_name, module_self, args, kwargs)
                
                # 调用原始forward
                return original_forward(module_self, *args, **kwargs)
            
            # 应用patch
            torch.nn.Module.forward = patched_forward
            self._original_forward = original_forward
            
            print("[TRACKER] PyTorch forward patch应用成功")
            return True
            
        except Exception as e:
            print(f"[TRACKER] 设置推理hooks失败: {e}")
            return False
    
    def _record_patched_call(self, module_name: str, module, args, kwargs):
        """记录通过patch捕获的调用"""
        try:
            # 只记录我们关心的模块
            module_type = type(module).__name__
            if module_type in self.target_ops or any(kw in module_name.lower() for kw in ['linear', 'norm', 'embed']):
                
                operation_record = {
                    'inference_id': self.current_inference_id,
                    'module_name': module_name,
                    'module_type': module_type,
                    'timestamp': time.time(),
                    'call_order': len(self.operation_sequence),
                    'input_info': self._extract_tensor_info(args, 'input'),
                    'output_info': {'type': 'output', 'count': 0, 'shapes': [], 'dtypes': [], 'devices': []},  # 将在后续填充
                    'parameters': self._extract_parameter_info(module),
                    'computation_type': self._classify_computation_type(module_name, module),
                    'memory_footprint': {'input_memory_mb': 0, 'output_memory_mb': 0, 'total_memory_mb': 0}
                }
                
                # 记录调用
                self.operator_calls[module_name].append(operation_record)
                self.operation_sequence.append(operation_record)
                self.call_counts[module_name] += 1
                
    def remove_hooks(self):
        """移除所有hooks"""
        for hook in self.hooks:
            hook.remove()
        self.hooks.clear()
        
        # 恢复原始的forward方法
        if hasattr(self, '_original_forward'):
            torch.nn.Module.forward = self._original_forward
            print("[TRACKER] PyTorch forward patch已恢复")
            
        print(f"[TRACKER] 已移除所有hooks")
    
    def generate_detailed_analysis(self) -> Dict:
        """生成详细分析报告"""
        print(f"[TRACKER] 开始生成详细分析报告...")
        
        analysis = {
            'inference_summary': {
                'inference_id': self.current_inference_id,
                'total_operations': len(self.operation_sequence),
                'unique_modules': len(self.operator_calls),
                'layers_analyzed': len(self.layer_module_mapping),
                'total_calls': sum(self.call_counts.values())
            },
            'layer_by_layer_analysis': self._analyze_by_layers(),
            'module_call_statistics': self._generate_module_statistics(),
            'computation_type_analysis': self._analyze_computation_types(),
            'tensor_shape_analysis': self._analyze_tensor_shapes(),
            'memory_analysis': self._analyze_memory_usage(),
            'operation_sequence': self._generate_operation_sequence(),
            'detailed_module_breakdown': self._generate_detailed_breakdown()
        }
        
        return analysis
    
    def _analyze_by_layers(self) -> Dict:
        """按层分析"""
        layer_analysis = {}
        
        for layer_key, layer_info in self.layer_module_mapping.items():
            layer_stats = {
                'layer_number': layer_info['layer_number'],
                'total_modules': len(layer_info['modules']),
                'attention_modules': len(layer_info['attention_modules']),
                'mlp_modules': len(layer_info['mlp_modules']),
                'moe_modules': len(layer_info['moe_modules']),
                'norm_modules': len(layer_info['norm_modules']),
                'other_modules': len(layer_info['other_modules']),
                'module_details': {}
            }
            
            # 详细分析每个模块
            for module_name in layer_info['modules']:
                if module_name in self.operator_calls:
                    calls = self.operator_calls[module_name]
                    layer_stats['module_details'][module_name] = {
                        'call_count': len(calls),
                        'module_type': calls[0]['module_type'] if calls else 'unknown',
                        'computation_type': calls[0]['computation_type'] if calls else 'unknown',
                        'input_shapes': [call['input_info']['shapes'] for call in calls[:3]],  # 前3次调用
                        'output_shapes': [call['output_info']['shapes'] for call in calls[:3]],
                        'parameter_count': calls[0]['parameters']['parameter_count'] if calls else 0,
                        'avg_memory_mb': np.mean([call['memory_footprint']['total_memory_mb'] for call in calls]) if calls else 0
                    }
            
            layer_analysis[layer_key] = layer_stats
        
        return layer_analysis
    
    def _generate_module_statistics(self) -> Dict:
        """生成模块统计"""
        stats = {}
        
        for module_name, calls in self.operator_calls.items():
            if calls:
                first_call = calls[0]
                stats[module_name] = {
                    'total_calls': len(calls),
                    'module_type': first_call['module_type'],
                    'computation_type': first_call['computation_type'],
                    'parameter_count': first_call['parameters']['parameter_count'],
                    'input_shapes': [call['input_info']['shapes'] for call in calls],
                    'output_shapes': [call['output_info']['shapes'] for call in calls],
                    'memory_usage': {
                        'total_mb': sum(call['memory_footprint']['total_memory_mb'] for call in calls),
                        'avg_mb': np.mean([call['memory_footprint']['total_memory_mb'] for call in calls]),
                        'max_mb': max(call['memory_footprint']['total_memory_mb'] for call in calls)
                    }
                }
        
        return stats
    
    def _analyze_computation_types(self) -> Dict:
        """分析计算类型"""
        type_stats = defaultdict(lambda: {
            'count': 0,
            'modules': [],
            'total_memory_mb': 0.0,
            'parameter_count': 0
        })
        
        for module_name, calls in self.operator_calls.items():
            if calls:
                comp_type = calls[0]['computation_type']
                type_stats[comp_type]['count'] += len(calls)
                type_stats[comp_type]['modules'].append(module_name)
                type_stats[comp_type]['total_memory_mb'] += sum(call['memory_footprint']['total_memory_mb'] for call in calls)
                type_stats[comp_type]['parameter_count'] += calls[0]['parameters']['parameter_count']
        
        return dict(type_stats)
    
    def _analyze_tensor_shapes(self) -> Dict:
        """分析张量形状"""
        shape_analysis = {}
        
        for shape_key, shapes in self.shape_statistics.items():
            shape_analysis[shape_key] = {
                'unique_shapes': len(shapes),
                'shape_list': [list(shape) for shape in shapes],
                'most_common_shape': max(shapes, key=lambda x: list(self.shape_statistics[shape_key]).count(x)) if shapes else None
            }
        
        return shape_analysis
    
    def _analyze_memory_usage(self) -> Dict:
        """分析内存使用"""
        memory_stats = {
            'total_memory_mb': 0.0,
            'by_module_type': defaultdict(float),
            'by_computation_type': defaultdict(float),
            'largest_operations': []
        }
        
        operation_memory = []
        
        for module_name, calls in self.operator_calls.items():
            for call in calls:
                memory_mb = call['memory_footprint']['total_memory_mb']
                memory_stats['total_memory_mb'] += memory_mb
                memory_stats['by_module_type'][call['module_type']] += memory_mb
                memory_stats['by_computation_type'][call['computation_type']] += memory_mb
                
                operation_memory.append({
                    'module_name': module_name,
                    'memory_mb': memory_mb,
                    'input_shapes': call['input_info']['shapes'],
                    'output_shapes': call['output_info']['shapes']
                })
        
        # 找出最大的操作
        memory_stats['largest_operations'] = sorted(operation_memory, key=lambda x: x['memory_mb'], reverse=True)[:10]
        
        return memory_stats
    
    def _generate_operation_sequence(self) -> List[Dict]:
        """生成操作序列"""
        return [{
            'order': op['call_order'],
            'module_name': op['module_name'],
            'module_type': op['module_type'],
            'computation_type': op['computation_type'],
            'input_shapes': op['input_info']['shapes'],
            'output_shapes': op['output_info']['shapes'],
            'memory_mb': op['memory_footprint']['total_memory_mb']
        } for op in self.operation_sequence]
    
    def _generate_detailed_breakdown(self) -> Dict:
        """生成详细breakdown"""
        breakdown = {
            'attention_operations': {},
            'mlp_operations': {},
            'moe_operations': {},
            'normalization_operations': {},
            'embedding_operations': {},
            'other_operations': {}
        }
        
        for module_name, calls in self.operator_calls.items():
            if not calls:
                continue
                
            call_info = {
                'module_name': module_name,
                'call_count': len(calls),
                'computation_type': calls[0]['computation_type'],
                'input_shapes': calls[0]['input_info']['shapes'],
                'output_shapes': calls[0]['output_info']['shapes'],
                'parameter_count': calls[0]['parameters']['parameter_count']
            }
            
            # 分类到不同操作
            comp_type = calls[0]['computation_type']
            if 'attention' in comp_type:
                breakdown['attention_operations'][module_name] = call_info
            elif 'mlp' in comp_type:
                breakdown['mlp_operations'][module_name] = call_info
            elif 'moe' in comp_type:
                breakdown['moe_operations'][module_name] = call_info
            elif 'norm' in comp_type:
                breakdown['normalization_operations'][module_name] = call_info
            elif 'embedding' in comp_type:
                breakdown['embedding_operations'][module_name] = call_info
            else:
                breakdown['other_operations'][module_name] = call_info
        
        return breakdown

def run_precise_operator_analysis():
    """运行精确算子分析"""
    print("="*80)
    print("开始DeepSeek-V3 INT8精确算子追踪分析")
    print("="*80)
    
    tracker = PreciseOperatorTracker()
    
    try:
        # 1. 初始化模型
        print("\n[MAIN] 步骤1: 初始化SGLang引擎...")
        
        llm = sgl.Engine(
            model_path=MODEL_PATH,
            tp_size=1,
            quantization="w8a8_int8",
            trust_remote_code=True,
            mem_fraction_static=0.6,
            disable_cuda_graph=True,
            log_level="warning",
        )
        
        print(f"[MAIN] 模型加载成功: {type(llm)}")
        
        # 2. 安装精确追踪hooks
        print("\n[MAIN] 步骤2: 安装精确算子追踪hooks...")
        
        # 尝试多种方式获取模型
        model = None
        
        # 尝试通过私有属性访问
        try:
            # SGLang可能使用分布式架构，尝试访问worker
            if hasattr(llm, '_engine'):
                engine = llm._engine
                if hasattr(engine, 'model_runner'):
                    model = engine.model_runner.model
                    print("[MAIN] 找到模型: _engine.model_runner.model")
        except Exception as e:
            print(f"[MAIN] 尝试_engine路径失败: {e}")
        
        # 尝试其他可能的路径
        if model is None:
            try:
                # 检查所有可能的属性
                for attr_name in ['model_runner', 'tp_worker', 'worker', 'engine']:
                    if hasattr(llm, attr_name):
                        attr = getattr(llm, attr_name)
                        print(f"[MAIN] 检查属性: {attr_name} -> {type(attr)}")
                        
                        if hasattr(attr, 'model'):
                            model = attr.model
                            print(f"[MAIN] 在{attr_name}中找到模型")
                            break
                        elif hasattr(attr, 'model_runner') and hasattr(attr.model_runner, 'model'):
                            model = attr.model_runner.model
                            print(f"[MAIN] 在{attr_name}.model_runner中找到模型")
                            break
            except Exception as e:
                print(f"[MAIN] 遍历属性失败: {e}")
        
        # 如果仍未找到，尝试通过推理过程hook
        if model is None:
            print("[MAIN] 直接模型访问失败，将通过推理过程进行hook注入...")
            
            # 创建一个特殊的追踪器，通过monkey patch方式工作
            success = tracker.setup_inference_hooks(llm)
            if not success:
                print("[MAIN] 错误: 无法设置推理hooks")
                return
                
        else:
            hook_count = tracker.install_precise_hooks(model)
        
        # 3. 执行测试用例
        print(f"\n[MAIN] 步骤3: 执行推理测试用例...")
        
        test_cases = [
            {
                "name": "simple_test",
                "prompt": "你好",
                "max_tokens": 5,
                "description": "简单测试用例"
            },
            {
                "name": "medium_test", 
                "prompt": "请简单介绍一下人工智能。",
                "max_tokens": 15,
                "description": "中等复杂度测试"
            },
            {
                "name": "complex_test",
                "prompt": "请详细解释深度学习在自然语言处理中的应用原理。",
                "max_tokens": 25,
                "description": "复杂测试用例"
            }
        ]
        
        for i, test_case in enumerate(test_cases):
            print(f"\n[MAIN] 执行测试用例 {i+1}: {test_case['name']}")
            print(f"[MAIN] 描述: {test_case['description']}")
            print(f"[MAIN] 提示词: {test_case['prompt']}")
            
            # 清理之前的追踪数据
            if i > 0:
                tracker.clear_data()
            
            try:
                # 执行推理
                result = llm.generate(
                    prompt=test_case["prompt"],
                    sampling_params={"max_new_tokens": test_case["max_tokens"], "temperature": 0.1}
                )
                
                print(f"[MAIN] 推理成功，生成文本: {result['text'][:50]}...")
                print(f"[MAIN] 捕获算子调用: {len(tracker.operation_sequence)} 次")
                print(f"[MAIN] 涉及模块数: {len(tracker.operator_calls)}")
                
                # 生成分析报告
                analysis = tracker.generate_detailed_analysis()
                
                # 保存报告
                timestamp = int(time.time())
                output_file = f"precise_operator_analysis_{test_case['name']}_{timestamp}.json"
                
                with open(output_file, 'w', encoding='utf-8') as f:
                    json.dump(analysis, f, indent=2, ensure_ascii=False, default=str)
                
                print(f"[MAIN] 详细分析报告已保存: {output_file}")
                
                # 生成Markdown报告
                md_file = f"precise_operator_analysis_{test_case['name']}_{timestamp}.md"
                generate_markdown_report(analysis, test_case, md_file)
                print(f"[MAIN] Markdown报告已保存: {md_file}")
                
            except Exception as e:
                print(f"[MAIN] 测试用例执行失败: {e}")
                traceback.print_exc()
        
        # 4. 生成综合分析
        print(f"\n[MAIN] 步骤4: 生成综合分析报告...")
        
        final_analysis = tracker.generate_detailed_analysis()
        
        # 保存最终报告
        final_output = f"comprehensive_operator_analysis_{int(time.time())}.json"
        with open(final_output, 'w', encoding='utf-8') as f:
            json.dump(final_analysis, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"[MAIN] 综合分析报告已保存: {final_output}")
        
        # 打印关键统计
        print_summary_statistics(final_analysis)
        
    except Exception as e:
        print(f"[MAIN] 主程序执行失败: {e}")
        traceback.print_exc()
    
    finally:
        # 清理
        tracker.remove_hooks()
        try:
            llm.shutdown()
            print("[MAIN] 模型已关闭")
        except:
            pass

def generate_markdown_report(analysis: Dict, test_case: Dict, filename: str):
    """生成Markdown格式的分析报告"""
    
    content = f"""# DeepSeek-V3 INT8 精确算子分析报告

## 测试用例信息
- **测试名称**: {test_case['name']}
- **描述**: {test_case['description']}
- **输入提示**: {test_case['prompt']}
- **最大生成tokens**: {test_case['max_tokens']}

## 总体统计

| 指标 | 数值 |
|------|------|
| 总操作数 | {analysis['inference_summary']['total_operations']} |
| 唯一模块数 | {analysis['inference_summary']['unique_modules']} |
| 分析层数 | {analysis['inference_summary']['layers_analyzed']} |
| 总调用次数 | {analysis['inference_summary']['total_calls']} |

## 按层分析

"""

    # 层级分析
    for layer_key, layer_info in analysis['layer_by_layer_analysis'].items():
        content += f"### {layer_key} (第{layer_info['layer_number']}层)\n\n"
        content += f"- **总模块数**: {layer_info['total_modules']}\n"
        content += f"- **注意力模块**: {layer_info['attention_modules']}\n"
        content += f"- **MLP模块**: {layer_info['mlp_modules']}\n"
        content += f"- **MoE模块**: {layer_info['moe_modules']}\n"
        content += f"- **归一化模块**: {layer_info['norm_modules']}\n\n"
        
        # 详细模块信息
        if layer_info['module_details']:
            content += "#### 模块详情\n\n"
            content += "| 模块名 | 类型 | 调用次数 | 参数数量 | 输入Shape | 输出Shape |\n"
            content += "|--------|------|----------|----------|-----------|----------|\n"
            
            for module_name, details in layer_info['module_details'].items():
                short_name = module_name.split('.')[-1] if '.' in module_name else module_name
                input_shapes = str(details['input_shapes'][0] if details['input_shapes'] else 'N/A')[:50]
                output_shapes = str(details['output_shapes'][0] if details['output_shapes'] else 'N/A')[:50]
                
                content += f"| {short_name} | {details['module_type']} | {details['call_count']} | {details['parameter_count']} | {input_shapes} | {output_shapes} |\n"
        
        content += "\n"

    # 计算类型分析
    content += "## 计算类型分析\n\n"
    content += "| 计算类型 | 操作次数 | 涉及模块数 | 总内存(MB) | 参数数量 |\n"
    content += "|----------|----------|------------|------------|----------|\n"
    
    for comp_type, stats in analysis['computation_type_analysis'].items():
        content += f"| {comp_type} | {stats['count']} | {len(stats['modules'])} | {stats['total_memory_mb']:.2f} | {stats['parameter_count']} |\n"

    # 内存分析
    content += "\n## 内存使用分析\n\n"
    memory_stats = analysis['memory_analysis']
    content += f"- **总内存使用**: {memory_stats['total_memory_mb']:.2f} MB\n\n"
    
    content += "### 按模块类型内存使用\n\n"
    for module_type, memory_mb in memory_stats['by_module_type'].items():
        content += f"- **{module_type}**: {memory_mb:.2f} MB\n"
    
    content += "\n### 最大内存操作 (Top 5)\n\n"
    content += "| 模块名 | 内存使用(MB) | 输入Shape | 输出Shape |\n"
    content += "|--------|--------------|-----------|----------|\n"
    
    for op in memory_stats['largest_operations'][:5]:
        short_name = op['module_name'].split('.')[-1] if '.' in op['module_name'] else op['module_name']
        input_str = str(op['input_shapes'])[:30] + "..." if len(str(op['input_shapes'])) > 30 else str(op['input_shapes'])
        output_str = str(op['output_shapes'])[:30] + "..." if len(str(op['output_shapes'])) > 30 else str(op['output_shapes'])
        content += f"| {short_name} | {op['memory_mb']:.2f} | {input_str} | {output_str} |\n"

    # 详细操作分解
    content += "\n## 详细操作分解\n\n"
    
    breakdown = analysis['detailed_module_breakdown']
    for category, operations in breakdown.items():
        if operations:
            content += f"### {category.replace('_', ' ').title()}\n\n"
            content += "| 模块名 | 调用次数 | 计算类型 | 参数数量 |\n"
            content += "|--------|----------|----------|----------|\n"
            
            for module_name, info in operations.items():
                short_name = module_name.split('.')[-2:] if '.' in module_name else [module_name]
                short_name = '.'.join(short_name)
                content += f"| {short_name} | {info['call_count']} | {info['computation_type']} | {info['parameter_count']} |\n"
            
            content += "\n"

    # 操作序列 (前20个)
    content += "## 操作执行序列 (前20个)\n\n"
    content += "| 顺序 | 模块名 | 模块类型 | 计算类型 | 内存(MB) |\n"
    content += "|------|--------|----------|----------|----------|\n"
    
    for op in analysis['operation_sequence'][:20]:
        short_name = op['module_name'].split('.')[-1] if '.' in op['module_name'] else op['module_name']
        content += f"| {op['order']+1} | {short_name} | {op['module_type']} | {op['computation_type']} | {op['memory_mb']:.2f} |\n"

    with open(filename, 'w', encoding='utf-8') as f:
        f.write(content)

def print_summary_statistics(analysis: Dict):
    """打印关键统计信息"""
    print("\n" + "="*80)
    print("关键统计信息汇总")
    print("="*80)
    
    summary = analysis['inference_summary']
    print(f"✅ 总操作数: {summary['total_operations']}")
    print(f"✅ 唯一模块数: {summary['unique_modules']}")
    print(f"✅ 分析层数: {summary['layers_analyzed']}")
    print(f"✅ 总调用次数: {summary['total_calls']}")
    
    print(f"\n📊 计算类型分布:")
    for comp_type, stats in analysis['computation_type_analysis'].items():
        print(f"   - {comp_type}: {stats['count']} 次操作")
    
    print(f"\n💾 内存使用:")
    memory_stats = analysis['memory_analysis']
    print(f"   - 总内存: {memory_stats['total_memory_mb']:.2f} MB")
    
    print(f"\n🔍 按层统计:")
    for layer_key, layer_info in analysis['layer_by_layer_analysis'].items():
        print(f"   - {layer_key}: {layer_info['total_modules']} 个模块")
    
    print("\n✨ 分析完成！所有详细数据已保存到文件。")

if __name__ == "__main__":
    run_precise_operator_analysis()
