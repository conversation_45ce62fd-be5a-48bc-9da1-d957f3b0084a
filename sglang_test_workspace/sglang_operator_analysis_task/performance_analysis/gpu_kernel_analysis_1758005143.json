{"analysis_summary": {"timestamp": 1758005143.3219802, "total_unique_kernels": 0, "total_kernel_calls": 0, "total_execution_time_us": 0, "analysis_method": "pytorch_profiler_gpu_kernel_tracing", "average_time_per_call_us": 0}, "test_execution_results": {"simple_query": {"prompt": "你好吗？", "generated_text": " serv凡是运动会那里 marriages", "execution_time": 2.2118921279907227, "max_tokens": 5, "description": "简单问候查询"}, "factual_question": {"prompt": "什么是人工智能？", "generated_text": "ранения magnification堪称opathic万的讲解 TOň:sverage最起码 Confidence fn().__ preven", "execution_time": 0.23826360702514648, "max_tokens": 15, "description": "事实性问题"}, "reasoning_task": {"prompt": "请解释机器学习的工作原理", "generated_text": "克的ash选中 zdrow也是这样 website626 Led regi decir收到的Expected mysteries痛苦的 Tür657把小 homeownersآ Maryland نقش surrenderedojasPreditrile", "execution_time": 0.9299030303955078, "max_tokens": 25, "description": "推理任务"}, "math_problem": {"prompt": "计算 15 + 27 = ?", "generated_text": ",Bjm traders případěیا退役航空航天１", "execution_time": 0.7913672924041748, "max_tokens": 8, "description": "数学计算"}}, "test_case_kernel_breakdown": {"simple_query": {"kernel_count": 0, "total_kernels": 0, "total_time": 0, "kernels": {}}, "factual_question": {"kernel_count": 0, "total_kernels": 0, "total_time": 0, "kernels": {}}, "reasoning_task": {"kernel_count": 0, "total_kernels": 0, "total_time": 0, "kernels": {}}, "math_problem": {"kernel_count": 0, "total_kernels": 0, "total_time": 0, "kernels": {}}}, "computation_category_breakdown": {}, "detailed_kernel_analysis": {}, "performance_rankings": {"top_time_consuming_kernels": [], "most_frequently_called_kernels": []}, "quantization_analysis": {"quantization_kernels": {}, "quantization_summary": {"total_kernels": 0, "total_calls": 0, "total_time": 0.0}}, "attention_analysis": {"attention_kernels": {}, "attention_summary": {"total_kernels": 0, "total_calls": 0, "total_time": 0.0}}, "memory_analysis": {"memory_kernels": {}, "memory_summary": {"total_kernels": 0, "total_calls": 0, "total_time": 0.0}}}