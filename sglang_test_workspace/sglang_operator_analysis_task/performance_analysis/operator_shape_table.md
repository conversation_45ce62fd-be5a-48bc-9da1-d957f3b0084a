# SGLang算子Shape分析表格

## 配置信息
- 批次大小: 1
- 序列长度: 512
- 张量并行: 4

## 算子Shape表格 (前20个算子示例)

```
shape			
layer	B	M	N	K
embedding	1	512	7168	1
layer_0_attn_qkv	1	512	2304	7168
layer_0_attn_qk	14	512	512	128
layer_0_attn_pv	14	512	128	512
layer_0_attn_o	1	512	7168	1792
layer_0_mlp_gate	1	512	4736	7168
layer_0_mlp_up	1	512	4736	7168
layer_0_mlp_down	1	512	7168	4736
layer_0_moe_gate	1	512	256	7168
layer_0_moe_expert_up	1	512	2048	7168
layer_0_moe_expert_gate	1	512	2048	7168
layer_0_moe_expert_down	1	512	7168	2048
layer_1_attn_qkv	1	512	2304	7168
layer_1_attn_qk	14	512	512	128
layer_1_attn_pv	14	512	128	512
layer_1_attn_o	1	512	7168	1792
layer_1_mlp_gate	1	512	4736	7168
layer_1_mlp_up	1	512	4736	7168
layer_1_mlp_down	1	512	7168	4736
layer_1_moe_gate	1	512	256	7168
```

## 算子类型说明
- **attn_qkv**: QKV合并投影
- **attn_qk**: Q与K的注意力计算
- **attn_pv**: 注意力权重与V的乘法
- **attn_o**: 注意力输出投影
- **mlp_gate**: MLP门控投影
- **mlp_up**: MLP上投影
- **mlp_down**: MLP下投影
- **moe_gate**: MoE路由门控
- **moe_expert_*****: MoE专家网络算子
- **embedding**: 词嵌入查找
- **lm_head**: 语言建模头投影

## 维度说明
- **B**: 批次维度
- **M**: 矩阵乘法的M维度 (通常是序列长度)
- **N**: 矩阵乘法的N维度 (输出特征维度)
- **K**: 矩阵乘法的K维度 (输入特征维度)
