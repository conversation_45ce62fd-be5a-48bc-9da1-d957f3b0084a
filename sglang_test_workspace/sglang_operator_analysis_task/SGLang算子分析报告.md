# SGLang DeepSeek模型算子分析报告

## 任务概述

本报告基于SGLang源码深度分析，详细梳理了DeepSeek模型在推理过程中经过的所有计算相关算子和shape变换，从prefill和decode两个关键阶段进行了全面分析。

## 模型基本信息

### 模型架构
- **模型类型**: DeepSeekV3ForCausalLMNextN
- **量化方案**: W8A8-INT8量化
- **张量并行**: 4-way tensor parallelism
- **推测参数**:
  - hidden_size: 7168
  - intermediate_size: 18944
  - num_layers: 61
  - num_attention_heads: 56
  - num_key_value_heads: 8
  - head_dim: 128
  - vocab_size: 129280

## 算子分析结果

### 1. 核心算子类别统计

#### Prefill阶段算子分布:
- **Linear变换**: 305个 (最多，包括QKV投影、MLP投影、MoE专家等)
- **Normalization**: 123个 (RMSNorm，每层前后都有)
- **Activation**: 81个 (SiLU激活函数)
- **Attention**: 61个 (每层一个自注意力)
- **Position Encoding**: 61个 (RoPE位置编码)
- **Communication**: 20个 (张量并行通信)
- **Selection**: 20个 (MoE专家选择)
- **Embedding**: 1个 (词嵌入)
- **Postprocessing**: 1个 (logits处理)

#### Decode阶段算子分布:
与Prefill阶段基本相同，但增加了:
- **Sampling**: 1个 (token采样)

### 2. 关键模块详细分析

#### 2.1 Attention模块 (DeepseekAttention)

**算子流程**:
1. **QKV投影** (QKVParallelLinear)
   - Input: `[batch_size, seq_len, hidden_size]`
   - Output: `[batch_size, seq_len, (num_heads + 2*num_kv_heads) * head_dim]`
   - 量化: W8A8-INT8
   - 权重shape: `[hidden_size, (num_heads + 2*num_kv_heads) * head_dim]`

2. **QKV分离**
   - Q: `[batch_size, seq_len, num_heads * head_dim]`
   - K,V: `[batch_size, seq_len, num_kv_heads * head_dim]`

3. **RoPE位置编码**
   - 对Q和K应用旋转位置编码

4. **注意力计算** (RadixAttention + Triton后端)
   - Prefill: 序列级causal attention
   - Decode: 单token增量attention
   - KV Cache更新

5. **输出投影** (RowParallelLinear)
   - Input: `[batch_size, seq_len, num_heads * head_dim]`
   - Output: `[batch_size, seq_len, hidden_size]`
   - 量化: W8A8-INT8

#### 2.2 MLP模块 (DeepseekMLP)

**算子流程**:
1. **Gate+Up投影** (MergedColumnParallelLinear)
   - Input: `[batch_size, seq_len, hidden_size]`
   - Output: `[batch_size, seq_len, 2 * intermediate_size]`
   - 量化: W8A8-INT8
   - 计算: 合并gate和up投影

2. **SiLU+Mul激活** (SiluAndMul)
   - Input: `[batch_size, seq_len, 2 * intermediate_size]`
   - Output: `[batch_size, seq_len, intermediate_size]`
   - 计算: SiLU(gate) * up

3. **Down投影** (RowParallelLinear)
   - Input: `[batch_size, seq_len, intermediate_size]`
   - Output: `[batch_size, seq_len, hidden_size]`
   - 量化: W8A8-INT8

#### 2.3 MoE模块 (DeepseekMoE)

**算子流程**:
1. **路由Gate** (ReplicatedLinear)
   - Input: `[batch_size, seq_len, hidden_size]`
   - Output: `[batch_size, seq_len, n_routed_experts]`
   - 计算: 专家路由logits

2. **TopK选择**
   - 为每个token选择top-k个专家

3. **专家计算** (fused_moe)
   - Gate+Up投影 → SiLU激活 → Down投影
   - 使用INT8量化

4. **共享专家** (如果存在)
   - 类似普通MLP的三阶段计算

5. **All-Reduce通信**
   - 跨张量并行ranks聚合专家输出

### 3. 量化算子分析

#### W8A8-INT8量化方案:

1. **权重量化**
   - 公式: `w_int8 = round(w_bf16 / scale_w)`
   - 存储: per-channel或per-tensor scale

2. **激活量化**
   - 公式: `a_int8 = round(a_bf16 / scale_a)`
   - 计算: dynamic per-token scale

3. **INT8矩阵乘法**
   - 计算: `C_int32 = A_int8 @ B_int8`
   - 内核: cutlass/triton/custom

4. **反量化**
   - 公式: `output_bf16 = C_int32 * scale_a * scale_w`
   - 优化: 与bias和activation融合

### 4. Prefill vs Decode差异分析

| 维度 | Prefill阶段 | Decode阶段 |
|------|-------------|------------|
| **序列长度** | 处理完整输入序列 | 处理单个token |
| **Attention模式** | Causal attention矩阵 | 单token对历史的attention |
| **KV Cache** | 初始填充整个cache | 增量更新单个位置 |
| **内存访问** | 内存带宽密集 | 内存延迟敏感 |
| **并行性** | 序列级并行 | Batch级并行 |
| **计算特征** | 大矩阵乘法 | 小向量-矩阵乘法 |
| **瓶颈** | 内存带宽+attention计算 | 内存访问延迟 |

### 5. Shape变换流程图

```
输入: [batch_size, seq_len] (token_ids)
  ↓
Embedding: [batch_size, seq_len, hidden_size]
  ↓
Layer 0-60循环:
  ├─ RMSNorm: [batch_size, seq_len, hidden_size]
  ├─ Attention:
  │   ├─ QKV: [batch_size, seq_len, (56+2*8)*128] = [B, S, 9216]
  │   ├─ Split: Q[B,S,7168], K[B,S,1024], V[B,S,1024]
  │   ├─ RoPE: Q'[B,S,7168], K'[B,S,1024]
  │   ├─ Attn: [batch_size, seq_len, 7168]
  │   └─ O_proj: [batch_size, seq_len, 7168]
  ├─ RMSNorm: [batch_size, seq_len, hidden_size]
  └─ MLP/MoE:
      ├─ Gate+Up: [batch_size, seq_len, 2*18944] = [B, S, 37888]
      ├─ SiLU: [batch_size, seq_len, 18944]
      └─ Down: [batch_size, seq_len, 7168]
  ↓
Final RMSNorm: [batch_size, seq_len, hidden_size]
  ↓
LM Head: [batch_size, seq_len, vocab_size] = [B, S, 129280]
  ↓
Logits Processing & Sampling
```

## 性能优化要点

### 1. 量化优化
- INT8计算减少内存带宽占用
- 动态量化scale计算开销
- 融合量化-反量化-激活算子

### 2. 注意力优化  
- Triton backend优化attention kernel
- KV Cache高效管理
- Chunked prefill减少内存峰值

### 3. MoE优化
- Expert路由优化
- 专家计算融合
- 通信-计算overlap

### 4. 张量并行优化
- All-reduce通信优化
- 权重分片策略
- 激活重计算vs通信权衡

## 结论

本分析深入梳理了SGLang中DeepSeek模型的完整算子流程，识别出了61层transformer中每层包含的详细算子操作。关键发现包括:

1. **算子密度高**: 每个推理step涉及600+个算子操作
2. **量化普及**: 线性层全面使用W8A8-INT8量化
3. **阶段差异明显**: Prefill和Decode在计算模式上有显著差异
4. **优化空间大**: 在量化、attention、MoE、通信等维度都有优化机会

这一分析为进一步的性能优化和算子融合提供了重要参考。

---

*分析时间: 2025-09-16*  
*基于: SGLang源码 + 实际运行环境*
