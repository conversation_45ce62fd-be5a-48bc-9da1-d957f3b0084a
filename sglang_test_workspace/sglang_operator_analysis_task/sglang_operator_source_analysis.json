{"analysis_timestamp": "2025-09-16", "source_code_analysis": {"linear_operators": [{"file": "srt/layers/linear.py", "classes": ["LinearBase", "ReplicatedLinear", "ColumnParallelLinear", "MergedColumnParallelLinear", "QKVParallelLinear", "RowParallelLinear"], "torch_operations": ["empty", "get_default_dtype", "allclose"], "matrix_operations": 2, "shape_operations": 37, "forward_methods_count": 4}], "attention_operators": [{"file": "srt/layers/attention/aiter_backend.py", "classes": ["WrapperDispatch", "AiterAttnBackend"], "torch_operations": ["tensor", "finfo", "empty_like", "arange", "index_select", "cumsum", "ones", "split", "broadcast_to", "cat", "zeros", "max", "empty"], "matrix_operations": 1, "shape_operations": 40, "forward_methods_count": 0}, {"file": "srt/layers/attention/ascend_backend.py", "classes": ["AscendAttnBackend"], "torch_operations": ["finfo", "cat", "masked_fill", "ones", "tril", "zeros", "empty"], "matrix_operations": 1, "shape_operations": 22, "forward_methods_count": 0}, {"file": "srt/layers/attention/base_attn_backend.py", "classes": ["AttentionBackend"], "torch_operations": [], "matrix_operations": 1, "shape_operations": 1, "forward_methods_count": 1}, {"file": "srt/layers/attention/cutlass_mla_backend.py", "classes": ["CutlassMLABackend"], "torch_operations": ["empty", "full"], "matrix_operations": 1, "shape_operations": 7, "forward_methods_count": 0}, {"file": "srt/layers/attention/double_sparsity_backend.py", "classes": ["DoubleSparseAttnBackend"], "torch_operations": ["zeros_like", "empty_like", "cumsum", "gather", "max", "min", "sum", "empty"], "matrix_operations": 0, "shape_operations": 14, "forward_methods_count": 0}, {"file": "srt/layers/attention/dual_chunk_flashattention_backend.py", "classes": ["DualChunkFlashAttentionBackend"], "torch_operations": ["tensor", "log1p", "arange", "full", "topk", "split", "max", "stack", "zeros", "iinfo", "exp", "abs", "cat", "empty_like", "cumsum", "where", "log", "sum", "empty"], "matrix_operations": 5, "shape_operations": 79, "forward_methods_count": 0}, {"file": "srt/layers/attention/flashattention_backend.py", "classes": ["FlashAttentionBackend"], "torch_operations": ["compile", "full", "arange", "sort", "transpose", "cumsum", "from_numpy", "where", "zeros"], "matrix_operations": 5, "shape_operations": 51, "forward_methods_count": 0}, {"file": "srt/layers/attention/flashinfer_backend.py", "classes": ["WrapperDispatch", "FlashInferAttnBackend"], "torch_operations": ["tensor", "zeros_like", "empty_like", "minimum", "cumsum", "ones", "zeros", "clamp", "empty"], "matrix_operations": 2, "shape_operations": 17, "forward_methods_count": 0}, {"file": "srt/layers/attention/flashinfer_mla_backend.py", "classes": ["FlashInferMLAAttnBackend"], "torch_operations": ["arange", "cumsum", "ones", "cat", "zeros", "empty"], "matrix_operations": 2, "shape_operations": 24, "forward_methods_count": 1}, {"file": "srt/layers/attention/flashmla_backend.py", "classes": ["FlashMLABackend"], "torch_operations": ["zeros", "ones", "full"], "matrix_operations": 1, "shape_operations": 9, "forward_methods_count": 0}, {"file": "srt/layers/attention/hybrid_attn_backend.py", "classes": ["HybridAttnBackend"], "torch_operations": [], "matrix_operations": 0, "shape_operations": 0, "forward_methods_count": 0}, {"file": "srt/layers/attention/intel_amx_backend.py", "classes": ["IntelAMXAttnBackend"], "torch_operations": ["zeros", "empty_like", "max"], "matrix_operations": 0, "shape_operations": 8, "forward_methods_count": 0}, {"file": "srt/layers/attention/merge_state.py", "classes": [], "torch_operations": [], "matrix_operations": 0, "shape_operations": 1, "forward_methods_count": 0}, {"file": "srt/layers/attention/tbo_backend.py", "classes": ["TboAttnBackend"], "torch_operations": [], "matrix_operations": 1, "shape_operations": 0, "forward_methods_count": 0}, {"file": "srt/layers/attention/torch_native_backend.py", "classes": ["TorchNativeAttnBackend"], "torch_operations": ["empty_like", "empty"], "matrix_operations": 0, "shape_operations": 15, "forward_methods_count": 0}, {"file": "srt/layers/attention/triton_backend.py", "classes": ["TritonAttnBackend"], "torch_operations": ["tensor", "zeros_like", "empty_like", "arange", "full", "minimum", "cumsum", "max", "zeros", "empty"], "matrix_operations": 2, "shape_operations": 20, "forward_methods_count": 0}, {"file": "srt/layers/attention/trtllm_mha_backend.py", "classes": ["TRTLLMHAAttnBackend", "TRTLLMHAAttnMultiStepDraftBackend"], "torch_operations": ["zeros", "arange", "cumsum"], "matrix_operations": 1, "shape_operations": 9, "forward_methods_count": 0}, {"file": "srt/layers/attention/trtllm_mla_backend.py", "classes": ["TRTLLMMLABackend", "TRTLLMMLAMultiStepDraftBackend"], "torch_operations": ["zeros", "empty", "full", "cat"], "matrix_operations": 1, "shape_operations": 11, "forward_methods_count": 0}, {"file": "srt/layers/attention/utils.py", "classes": [], "torch_operations": [], "matrix_operations": 2, "shape_operations": 0, "forward_methods_count": 0}, {"file": "srt/layers/attention/vision.py", "classes": ["VisionSdpaAttention", "VisionTritonAttention", "VisionFlash3Attention", "VisionAttention"], "torch_operations": ["tensor", "<PERSON><PERSON>l", "finfo", "empty_like", "arange", "zeros"], "matrix_operations": 6, "shape_operations": 21, "forward_methods_count": 4}, {"file": "srt/layers/attention/vision_utils.py", "classes": [], "torch_operations": ["cat"], "matrix_operations": 0, "shape_operations": 3, "forward_methods_count": 0}, {"file": "srt/layers/attention/wave_backend.py", "classes": ["WaveAttnBackend"], "torch_operations": ["empty_like", "arange", "full", "cumsum", "max", "zeros", "empty"], "matrix_operations": 2, "shape_operations": 15, "forward_methods_count": 0}, {"file": "srt/layers/attention/triton_ops/decode_attention.py", "classes": [], "torch_operations": [], "matrix_operations": 4, "shape_operations": 22, "forward_methods_count": 0}, {"file": "srt/layers/attention/triton_ops/double_sparsity_attention.py", "classes": [], "torch_operations": ["topk", "no_grad", "empty", "arange"], "matrix_operations": 11, "shape_operations": 52, "forward_methods_count": 0}, {"file": "srt/layers/attention/triton_ops/extend_attention.py", "classes": [], "torch_operations": ["empty_like", "empty"], "matrix_operations": 2, "shape_operations": 11, "forward_methods_count": 0}, {"file": "srt/layers/attention/triton_ops/merge_state.py", "classes": [], "torch_operations": ["empty_like"], "matrix_operations": 1, "shape_operations": 3, "forward_methods_count": 0}, {"file": "srt/layers/attention/triton_ops/prefill_attention.py", "classes": [], "torch_operations": [], "matrix_operations": 1, "shape_operations": 7, "forward_methods_count": 0}, {"file": "srt/layers/attention/triton_ops/rocm_mla_decode_rope.py", "classes": [], "torch_operations": [], "matrix_operations": 2, "shape_operations": 5, "forward_methods_count": 0}, {"file": "srt/layers/attention/wave_ops/decode_attention.py", "classes": [], "torch_operations": [], "matrix_operations": 1, "shape_operations": 3, "forward_methods_count": 0}, {"file": "srt/layers/attention/wave_ops/extend_attention.py", "classes": [], "torch_operations": [], "matrix_operations": 1, "shape_operations": 16, "forward_methods_count": 0}, {"file": "srt/layers/attention/wave_ops/prefill_attention.py", "classes": [], "torch_operations": [], "matrix_operations": 0, "shape_operations": 14, "forward_methods_count": 0}, {"file": "srt/layers/radix_attention.py", "classes": ["AttentionType", "RadixAttention"], "torch_operations": [], "matrix_operations": 0, "shape_operations": 3, "forward_methods_count": 1}], "mlp_operators": [], "moe_operators": [{"file": "srt/layers/moe/__init__.py", "classes": [], "torch_operations": [], "matrix_operations": 0, "shape_operations": 0, "forward_methods_count": 0}, {"file": "srt/layers/moe/cutlass_moe.py", "classes": [], "torch_operations": ["empty"], "matrix_operations": 0, "shape_operations": 23, "forward_methods_count": 0}, {"file": "srt/layers/moe/cutlass_moe_params.py", "classes": ["CutlassMoEType"], "torch_operations": ["empty", "full"], "matrix_operations": 1, "shape_operations": 0, "forward_methods_count": 0}, {"file": "srt/layers/moe/cutlass_w4a8_moe.py", "classes": [], "torch_operations": ["zeros", "empty_like", "empty"], "matrix_operations": 0, "shape_operations": 29, "forward_methods_count": 0}, {"file": "srt/layers/moe/fused_moe_native.py", "classes": [], "torch_operations": ["chunk", "einsum", "empty_like", "cat"], "matrix_operations": 0, "shape_operations": 5, "forward_methods_count": 0}, {"file": "srt/layers/moe/rocm_moe_utils.py", "classes": ["ActivationMethod"], "torch_operations": ["empty_like"], "matrix_operations": 0, "shape_operations": 1, "forward_methods_count": 0}, {"file": "srt/layers/moe/router.py", "classes": [], "torch_operations": ["empty", "tanh"], "matrix_operations": 3, "shape_operations": 16, "forward_methods_count": 1}, {"file": "srt/layers/moe/topk.py", "classes": ["TopKOutputFormat", "TopKOutput", "StandardTopKOutput", "TritonKernelTopKOutput", "BypassedTopKOutput", "TopK"], "torch_operations": ["zeros_like", "compile", "ran<PERSON>t", "full", "arange", "topk", "softmax", "ones_like", "empty"], "matrix_operations": 12, "shape_operations": 34, "forward_methods_count": 0}, {"file": "srt/layers/moe/utils.py", "classes": ["MoeA2ABackend", "MoeRunnerBackend", "DeepEPMode"], "torch_operations": [], "matrix_operations": 3, "shape_operations": 0, "forward_methods_count": 0}, {"file": "srt/layers/moe/ep_moe/__init__.py", "classes": [], "torch_operations": [], "matrix_operations": 0, "shape_operations": 0, "forward_methods_count": 0}, {"file": "srt/layers/moe/ep_moe/kernels.py", "classes": [], "torch_operations": ["searchsorted", "finfo", "arange", "sort", "no_grad", "zeros", "empty"], "matrix_operations": 25, "shape_operations": 36, "forward_methods_count": 0}, {"file": "srt/layers/moe/ep_moe/layer.py", "classes": ["EPMoE", "DeepEPMoE"], "torch_operations": ["tensor", "empty_like", "logical_and", "bitwise_and", "bitwise_right_shift", "where", "zeros", "empty"], "matrix_operations": 2, "shape_operations": 22, "forward_methods_count": 2}, {"file": "srt/layers/moe/fused_moe_triton/__init__.py", "classes": [], "torch_operations": [], "matrix_operations": 1, "shape_operations": 0, "forward_methods_count": 0}, {"file": "srt/layers/moe/fused_moe_triton/fused_moe.py", "classes": [], "torch_operations": ["empty_like", "add", "sum", "empty", "sigmoid"], "matrix_operations": 7, "shape_operations": 74, "forward_methods_count": 0}, {"file": "srt/layers/moe/fused_moe_triton/layer.py", "classes": ["FusedMoeWeightScaleSupported", "FusedMoE", "FlashInferFusedMoE", "FlashInferFP4MoE"], "torch_operations": ["get_default_dtype", "full", "arange"], "matrix_operations": 5, "shape_operations": 22, "forward_methods_count": 3}, {"file": "srt/layers/moe/fused_moe_triton/triton_kernels_moe.py", "classes": [], "torch_operations": ["empty"], "matrix_operations": 0, "shape_operations": 21, "forward_methods_count": 0}, {"file": "srt/layers/moe/moe_runner/__init__.py", "classes": [], "torch_operations": [], "matrix_operations": 0, "shape_operations": 0, "forward_methods_count": 0}, {"file": "srt/layers/moe/moe_runner/base.py", "classes": [], "torch_operations": [], "matrix_operations": 1, "shape_operations": 0, "forward_methods_count": 0}, {"file": "srt/layers/moe/token_dispatcher/__init__.py", "classes": [], "torch_operations": [], "matrix_operations": 0, "shape_operations": 0, "forward_methods_count": 0}, {"file": "srt/layers/moe/token_dispatcher/base_dispatcher.py", "classes": ["DispatchOutputFormat", "DispatchOutput", "BaseDispatcherConfig", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "torch_operations": [], "matrix_operations": 9, "shape_operations": 0, "forward_methods_count": 0}, {"file": "srt/layers/moe/token_dispatcher/deepep.py", "classes": ["DeepEPNormalOutput", "DeepEPLLOutput", "AscendDeepEPLLOutput", "DeepEPDispatchMode", "DeepEPConfig", "_DeepEPDispatcherImplNormal", "_DeepEPDispatcherImplLowLatency", "_Stage", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "torch_operations": ["zeros", "empty"], "matrix_operations": 9, "shape_operations": 14, "forward_methods_count": 0}, {"file": "srt/layers/moe/token_dispatcher/standard.py", "classes": ["StandardDispatchOutput"], "torch_operations": [], "matrix_operations": 1, "shape_operations": 0, "forward_methods_count": 0}], "normalization_operators": [{"file": "srt/layers/layernorm.py", "classes": ["RMSNorm", "GemmaRMSNorm", "Gemma3RMSNorm"], "torch_operations": ["empty_like", "rsqrt", "ones", "zeros"], "matrix_operations": 0, "shape_operations": 2, "forward_methods_count": 1}], "activation_operators": [{"file": "srt/layers/activation.py", "classes": ["SiluAndMul", "GeluAndMul", "NewGELU", "ReLU2", "QuickGELU", "ScaledActivation"], "torch_operations": ["tanh", "pow", "get_default_dtype", "empty", "sigmoid"], "matrix_operations": 0, "shape_operations": 12, "forward_methods_count": 2}], "embedding_operators": [{"file": "srt/layers/rotary_embedding.py", "classes": ["RotaryEmbedding", "LinearScalingRotaryEmbedding", "DynamicNTKScalingRotaryEmbedding", "YaRNScalingRotaryEmbedding", "Phi3LongRoPEScaledRotaryEmbedding", "DeepseekScalingRotaryEmbedding", "Llama3RotaryEmbedding", "Llama4VisionRotaryEmbedding", "DynamicNTKAlphaRotaryEmbedding", "MRotaryEmbedding", "DualChunkRotaryEmbedding"], "torch_operations": ["tensor", "arange", "index_select", "add", "outer", "ones", "cos", "get_default_dtype", "stack", "device", "view_as_real", "zeros", "any", "compile", "view_as_complex", "einsum", "ones_like", "cat", "chunk", "clamp", "sin", "argwhere", "where", "full_like"], "matrix_operations": 5, "shape_operations": 68, "forward_methods_count": 4}, {"file": "srt/layers/vocab_parallel_embedding.py", "classes": ["VocabParallel<PERSON>", "ParallelLMHead"], "torch_operations": ["get_default_dtype", "empty", "compile"], "matrix_operations": 10, "shape_operations": 8, "forward_methods_count": 2}], "quantization_operators": [{"file": "srt/layers/quantization/__init__.py", "classes": [], "torch_operations": [], "matrix_operations": 0, "shape_operations": 0, "forward_methods_count": 0}, {"file": "srt/layers/quantization/awq.py", "classes": ["AWQConfig", "AWQMarlinConfig", "AWQLinearMethod", "AWQMarlinLinearMethod", "AWQMoEMethod"], "torch_operations": ["<PERSON><PERSON>l", "empty"], "matrix_operations": 11, "shape_operations": 16, "forward_methods_count": 0}, {"file": "srt/layers/quantization/awq_triton.py", "classes": [], "torch_operations": ["zeros", "empty"], "matrix_operations": 2, "shape_operations": 26, "forward_methods_count": 0}, {"file": "srt/layers/quantization/base_config.py", "classes": ["QuantizeMethodBase", "LinearMethodBase", "FusedMoEMethodBase", "QuantizationConfig"], "torch_operations": [], "matrix_operations": 19, "shape_operations": 0, "forward_methods_count": 0}, {"file": "srt/layers/quantization/blockwise_int8.py", "classes": ["BlockInt8Config", "BlockInt8LinearMethod", "BlockInt8MoEMethod"], "torch_operations": ["finfo", "ones", "empty"], "matrix_operations": 5, "shape_operations": 0, "forward_methods_count": 0}, {"file": "srt/layers/quantization/fp8.py", "classes": ["Fp8Config", "Fp8LinearMethod", "Fp8MoEMethod", "Fp8KVCacheMethod"], "torch_operations": ["finfo", "empty_like", "full", "ones", "empty"], "matrix_operations": 5, "shape_operations": 3, "forward_methods_count": 0}, {"file": "srt/layers/quantization/fp8_kernel.py", "classes": [], "torch_operations": ["tensor", "empty_like", "finfo", "zeros", "iinfo", "empty"], "matrix_operations": 13, "shape_operations": 68, "forward_methods_count": 0}, {"file": "srt/layers/quantization/fp8_utils.py", "classes": [], "torch_operations": ["narrow", "finfo", "arange", "maximum", "pow", "ceil", "log2", "ones", "zeros", "all", "_scaled_mm"], "matrix_operations": 0, "shape_operations": 72, "forward_methods_count": 0}, {"file": "srt/layers/quantization/fpgemm_fp8.py", "classes": ["FBGEMMFp8Config", "FBGEMMFp8LinearMethod"], "torch_operations": ["finfo", "tensor", "empty", "get_default_dtype"], "matrix_operations": 5, "shape_operations": 0, "forward_methods_count": 0}, {"file": "srt/layers/quantization/gptq.py", "classes": ["GPTQConfig", "GPTQMarlinConfig", "GPTQLinearMethod", "GPTQMarlinLinearMethod", "GPTQMarlinMoEMethod"], "torch_operations": ["tensor", "argsor<PERSON>", "empty_like", "empty"], "matrix_operations": 13, "shape_operations": 15, "forward_methods_count": 0}, {"file": "srt/layers/quantization/int8_kernel.py", "classes": [], "torch_operations": ["iinfo", "empty", "empty_like"], "matrix_operations": 4, "shape_operations": 21, "forward_methods_count": 0}, {"file": "srt/layers/quantization/int8_utils.py", "classes": [], "torch_operations": ["iinfo", "maximum"], "matrix_operations": 0, "shape_operations": 8, "forward_methods_count": 0}, {"file": "srt/layers/quantization/kv_cache.py", "classes": ["BaseKVCacheMethod"], "torch_operations": ["tensor"], "matrix_operations": 0, "shape_operations": 0, "forward_methods_count": 0}, {"file": "srt/layers/quantization/marlin_utils.py", "classes": ["MarlinConfig", "MarlinLinearMethod"], "torch_operations": ["zeros", "argsor<PERSON>", "empty"], "matrix_operations": 6, "shape_operations": 35, "forward_methods_count": 0}, {"file": "srt/layers/quantization/marlin_utils_fp8.py", "classes": [], "torch_operations": ["empty", "ones_like", "cat"], "matrix_operations": 0, "shape_operations": 20, "forward_methods_count": 0}, {"file": "srt/layers/quantization/modelopt_quant.py", "classes": ["ModelOptFp8Config", "ModelOptFp8LinearMethod", "ModelOptFp8KVCacheMethod", "ModelOptFp8MoEMethod", "ModelOptFp4Config", "ModelOptFp4LinearMethod", "ModelOptNvFp4FusedMoEMethod"], "torch_operations": ["allclose", "finfo", "full", "zeros", "stack", "empty"], "matrix_operations": 12, "shape_operations": 45, "forward_methods_count": 0}, {"file": "srt/layers/quantization/moe_wna16.py", "classes": ["MoeWNA16Config", "MoeWNA16Method"], "torch_operations": ["from_numpy", "tensor", "empty", "zeros"], "matrix_operations": 8, "shape_operations": 9, "forward_methods_count": 0}, {"file": "srt/layers/quantization/mxfp4.py", "classes": ["Mxfp4Config", "Mxfp4MoEMethod", "Mxfp4DynamicQuantMoEMethod"], "torch_operations": ["tensor", "empty_like", "ones", "zeros", "stack", "empty"], "matrix_operations": 5, "shape_operations": 41, "forward_methods_count": 0}, {"file": "srt/layers/quantization/mxfp4_tensor.py", "classes": [], "torch_operations": ["tensor", "sign", "exp2", "maximum", "ceil", "log2", "zeros", "sum"], "matrix_operations": 2, "shape_operations": 11, "forward_methods_count": 0}, {"file": "srt/layers/quantization/petit.py", "classes": ["PetitNvFp4Config", "PetitNvFp4LinearMethod"], "torch_operations": ["empty"], "matrix_operations": 7, "shape_operations": 0, "forward_methods_count": 0}, {"file": "srt/layers/quantization/petit_utils.py", "classes": [], "torch_operations": [], "matrix_operations": 0, "shape_operations": 5, "forward_methods_count": 0}, {"file": "srt/layers/quantization/qoq.py", "classes": ["QoQConfig", "QoQLinearMethod"], "torch_operations": ["empty"], "matrix_operations": 5, "shape_operations": 0, "forward_methods_count": 0}, {"file": "srt/layers/quantization/unquant.py", "classes": ["UnquantizedEmbeddingMethod", "UnquantizedLinearMethod", "UnquantizedFusedMoEMethod"], "torch_operations": ["empty", "ones_like"], "matrix_operations": 0, "shape_operations": 5, "forward_methods_count": 0}, {"file": "srt/layers/quantization/utils.py", "classes": [], "torch_operations": ["randperm", "allclose", "finfo", "abs", "from_numpy", "argsor<PERSON>", "clamp", "max", "min", "Tensor", "zeros", "round", "empty"], "matrix_operations": 0, "shape_operations": 22, "forward_methods_count": 0}, {"file": "srt/layers/quantization/w4afp8.py", "classes": ["W4AFp8Config", "W4AFp8MoEMethod"], "torch_operations": ["tensor", "full", "where", "ones", "zeros", "empty"], "matrix_operations": 5, "shape_operations": 3, "forward_methods_count": 0}, {"file": "srt/layers/quantization/w8a8_fp8.py", "classes": ["W8A8Fp8Config", "W8A8Fp8LinearMethod", "W8A8FP8MoEMethod"], "torch_operations": ["ones", "empty"], "matrix_operations": 5, "shape_operations": 1, "forward_methods_count": 0}, {"file": "srt/layers/quantization/w8a8_int8.py", "classes": ["W8A8Int8Config", "W8A8Int8LinearMethod", "W8A8Int8MoEMethod", "NPU_W8A8LinearMethod", "NPU_W8A8DynamicLinearMethod", "NPU_W8A8MoEMethod"], "torch_operations": ["arange", "flatten", "ones", "zeros", "empty"], "matrix_operations": 17, "shape_operations": 9, "forward_methods_count": 0}, {"file": "srt/layers/quantization/compressed_tensors/__init__.py", "classes": [], "torch_operations": [], "matrix_operations": 0, "shape_operations": 0, "forward_methods_count": 0}, {"file": "srt/layers/quantization/compressed_tensors/compressed_tensors.py", "classes": ["DeviceCapability", "CompressedTensorsConfig", "CompressedTensorsLinearMethod"], "torch_operations": [], "matrix_operations": 8, "shape_operations": 0, "forward_methods_count": 0}, {"file": "srt/layers/quantization/compressed_tensors/compressed_tensors_moe.py", "classes": ["GPTQMarlinState", "CompressedTensorsMoEMethod", "CompressedTensorsW8A8Fp8MoEMethod", "CompressedTensorsWNA16MoEMethod"], "torch_operations": ["empty_like", "no_grad", "argsor<PERSON>", "ones", "empty"], "matrix_operations": 3, "shape_operations": 16, "forward_methods_count": 0}, {"file": "srt/layers/quantization/compressed_tensors/utils.py", "classes": [], "torch_operations": [], "matrix_operations": 0, "shape_operations": 0, "forward_methods_count": 0}, {"file": "srt/layers/quantization/compressed_tensors/schemes/__init__.py", "classes": [], "torch_operations": [], "matrix_operations": 0, "shape_operations": 0, "forward_methods_count": 0}, {"file": "srt/layers/quantization/compressed_tensors/schemes/compressed_tensors_scheme.py", "classes": ["CompressedTensorsScheme"], "torch_operations": [], "matrix_operations": 5, "shape_operations": 0, "forward_methods_count": 0}, {"file": "srt/layers/quantization/compressed_tensors/schemes/compressed_tensors_w8a16_fp8.py", "classes": ["CompressedTensorsW8A16Fp8"], "torch_operations": ["finfo", "empty"], "matrix_operations": 1, "shape_operations": 0, "forward_methods_count": 0}, {"file": "srt/layers/quantization/compressed_tensors/schemes/compressed_tensors_w8a8_fp8.py", "classes": ["CompressedTensorsW8A8Fp8"], "torch_operations": ["finfo", "empty"], "matrix_operations": 1, "shape_operations": 0, "forward_methods_count": 0}, {"file": "srt/layers/quantization/deep_gemm_wrapper/__init__.py", "classes": [], "torch_operations": [], "matrix_operations": 0, "shape_operations": 0, "forward_methods_count": 0}, {"file": "srt/layers/quantization/deep_gemm_wrapper/compile_utils.py", "classes": ["DeepGemmKernelType", "_NormalWarmupExecutor", "_GroupedContWarmupExecutor", "_GroupedMaskedWarmupExecutor"], "torch_operations": ["zeros", "empty"], "matrix_operations": 2, "shape_operations": 0, "forward_methods_count": 0}, {"file": "srt/layers/quantization/deep_gemm_wrapper/configurer.py", "classes": [], "torch_operations": [], "matrix_operations": 0, "shape_operations": 0, "forward_methods_count": 0}, {"file": "srt/layers/quantization/deep_gemm_wrapper/entrypoint.py", "classes": [], "torch_operations": [], "matrix_operations": 1, "shape_operations": 6, "forward_methods_count": 0}, {"file": "srt/layers/quantization/quark/__init__.py", "classes": [], "torch_operations": [], "matrix_operations": 0, "shape_operations": 0, "forward_methods_count": 0}, {"file": "srt/layers/quantization/quark/quark.py", "classes": ["QuarkConfig", "QuarkLinearMethod", "QuarkKVCacheMethod"], "torch_operations": [], "matrix_operations": 5, "shape_operations": 0, "forward_methods_count": 0}, {"file": "srt/layers/quantization/quark/quark_moe.py", "classes": ["QuarkW4A4MXFp4MoEMethod"], "torch_operations": ["get_default_dtype", "ones", "empty"], "matrix_operations": 1, "shape_operations": 6, "forward_methods_count": 0}, {"file": "srt/layers/quantization/quark/utils.py", "classes": [], "torch_operations": [], "matrix_operations": 0, "shape_operations": 0, "forward_methods_count": 0}, {"file": "srt/layers/quantization/quark/schemes/__init__.py", "classes": [], "torch_operations": [], "matrix_operations": 0, "shape_operations": 0, "forward_methods_count": 0}, {"file": "srt/layers/quantization/quark/schemes/quark_scheme.py", "classes": ["QuarkScheme"], "torch_operations": [], "matrix_operations": 5, "shape_operations": 0, "forward_methods_count": 0}, {"file": "srt/layers/quantization/quark/schemes/quark_w4a4_mxfp4.py", "classes": ["QuarkW4A4MXFP4"], "torch_operations": ["zeros", "empty", "get_default_dtype"], "matrix_operations": 1, "shape_operations": 5, "forward_methods_count": 0}], "shape_computations": [{"file": "srt/models/deepseek.py", "classes": ["DeepseekMLP", "DeepseekMoE", "DeepseekAttention", "DeepseekDecoderLayer", "DeepseekModel", "DeepseekForCausalLM"], "torch_operations": ["no_grad"], "matrix_operations": 1, "shape_operations": 7, "forward_methods_count": 6}, {"file": "srt/model_executor/model_runner.py", "classes": ["RankZeroFilter"], "torch_operations": ["tensor", "empty", "set_num_threads", "get_device_module", "ones", "stack", "device"], "matrix_operations": 3, "shape_operations": 1, "forward_methods_count": 1}, {"file": "srt/layers/sampler.py", "classes": ["<PERSON><PERSON>"], "torch_operations": ["zeros_like", "finfo", "arange", "full_like", "isnan", "cumsum", "multinomial", "where", "softmax", "gather", "log", "repeat_interleave", "any", "argmax"], "matrix_operations": 0, "shape_operations": 12, "forward_methods_count": 1}]}, "deepseek_model_analysis": {"model_components": {"DeepseekMLP": {"components": ["gate_up_proj", "down_proj", "act_fn"], "operations": ["MergedColumnParallelLinear (gate + up projection)", "SiluAndMul activation", "RowParallelLinear (down projection)"], "shape_flow": ["input: [batch_size, seq_len, hidden_size]", "gate_up_proj: [batch_size, seq_len, 2 * intermediate_size]", "activation: [batch_size, seq_len, intermediate_size]", "down_proj: [batch_size, seq_len, hidden_size]"]}, "DeepseekMoE": {"components": ["gate", "experts", "shared_experts", "topk"], "operations": ["Router gate computation", "Expert selection (TopK)", "Fused MoE computation", "Shared experts (if present)", "All-reduce for tensor parallelism"], "expert_ops": ["Gate projection", "Up projection", "Down projection", "SiLU activation"]}, "DeepseekAttention": {"components": ["qkv_proj", "rotary_emb", "attn", "o_proj"], "operations": ["QKV parallel linear projection", "Rotary position embedding", "Radix attention computation", "Output projection"], "shape_flow": ["input: [batch_size, seq_len, hidden_size]", "qkv: [batch_size, seq_len, (num_heads + 2*num_kv_heads) * head_dim]", "q: [batch_size, seq_len, num_heads * head_dim]", "k,v: [batch_size, seq_len, num_kv_heads * head_dim]", "attention_output: [batch_size, seq_len, num_heads * head_dim]", "output: [batch_size, seq_len, hidden_size]"]}}, "layer_structure": {}, "operator_flow": {}, "shape_transformations": []}, "quantization_analysis": {"w8a8_operators": [{"file": "__init__.py", "quantization_functions": ["override_quantization_method", "get_quantization_config", "monkey_patch_quant_configs"], "dequantization_functions": [], "int8_operations": 8, "kernel_calls": []}, {"file": "awq.py", "quantization_functions": ["get_quant_method", "override_quantization_method", "get_quant_method"], "dequantization_functions": [], "int8_operations": 1, "kernel_calls": []}, {"file": "awq_triton.py", "quantization_functions": ["awq_dequantize_kernel", "awq_dequantize_triton"], "dequantization_functions": ["awq_dequantize_kernel", "awq_dequantize_triton"], "int8_operations": 0, "kernel_calls": []}, {"file": "base_config.py", "quantization_functions": ["override_quantization_method", "get_quant_method"], "dequantization_functions": [], "int8_operations": 0, "kernel_calls": []}, {"file": "blockwise_int8.py", "quantization_functions": ["get_quant_method"], "dequantization_functions": [], "int8_operations": 32, "kernel_calls": []}, {"file": "fp8.py", "quantization_functions": ["get_quant_method"], "dequantization_functions": [], "int8_operations": 2, "kernel_calls": ["torch.ops.sgl_kernel", "cuda.empty_cache", "cuda.empty_cache", "cuda.empty_cache", "cuda.empty_cache", "cuda.empty_cache", "cuda.empty_cache", "torch.ops.sgl_kernel"]}, {"file": "fp8_kernel.py", "quantization_functions": ["_per_token_group_quant_8bit", "_per_token_group_quant_8bit_colmajor", "_per_token_group_quant_8bit_raw", "_per_token_group_quant_8bit_fuse_silu_and_mul", "per_token_group_quant_8bit", "create_per_token_group_quant_fp8_output_scale", "sglang_per_token_group_quant_fp8", "sglang_per_token_group_quant_8bit", "sglang_per_token_quant_fp8", "_static_quant_fp8", "static_quant_fp8", "_per_tensor_quant_mla_fp8_stage1", "_per_tensor_quant_mla_fp8_stage2", "per_tensor_quant_mla_fp8", "_per_token_group_quant_mla_deep_gemm_masked_fp8", "per_token_group_quant_mla_deep_gemm_masked_fp8", "scaled_fp8_quant", "scaled_fp8_quant", "_per_token_group_quant_fp8_hopper_moe_mn_major", "per_token_group_quant_fp8_hopper_moe_mn_major"], "dequantization_functions": [], "int8_operations": 8, "kernel_calls": ["cuda.get_device_properties", "torch.ops.sglang", "torch.ops._C", "torch.ops._C", "torch.ops._C"]}, {"file": "fp8_utils.py", "quantization_functions": ["dequant_mxfp4", "block_quant_to_tensor_quant", "block_quant_dequant", "requant_weight_ue8m0_inplace", "_requant_weight_ue8m0", "channel_quant_to_tensor_quant"], "dequantization_functions": ["dequant_mxfp4", "block_quant_dequant"], "int8_operations": 9, "kernel_calls": ["cuda.get_device_capability", "cuda.split"]}, {"file": "fpgemm_fp8.py", "quantization_functions": ["get_quant_method"], "dequantization_functions": [], "int8_operations": 0, "kernel_calls": []}, {"file": "gptq.py", "quantization_functions": ["get_quant_method", "override_quantization_method", "get_quant_method"], "dequantization_functions": [], "int8_operations": 1, "kernel_calls": []}, {"file": "int8_kernel.py", "quantization_functions": ["_per_token_quant_int8", "per_token_quant_int8", "_per_token_group_quant_int8", "per_token_group_quant_int8", "sglang_per_token_group_quant_int8"], "dequantization_functions": [], "int8_operations": 40, "kernel_calls": ["cuda.libdevice"]}, {"file": "int8_utils.py", "quantization_functions": ["block_dequant"], "dequantization_functions": ["block_dequant"], "int8_operations": 14, "kernel_calls": []}, {"file": "kv_cache.py", "quantization_functions": [], "dequantization_functions": [], "int8_operations": 0, "kernel_calls": []}, {"file": "marlin_utils.py", "quantization_functions": ["query_marlin_supported_quant_types", "override_quantization_method", "get_quant_method"], "dequantization_functions": [], "int8_operations": 1, "kernel_calls": ["cuda.get_device_properties", "cuda.get_device_capability", "cuda.get_device_capability"]}, {"file": "marlin_utils_fp8.py", "quantization_functions": ["marlin_quant_fp8_torch"], "dequantization_functions": [], "int8_operations": 0, "kernel_calls": []}, {"file": "modelopt_quant.py", "quantization_functions": ["get_quant_method", "get_quant_method"], "dequantization_functions": [], "int8_operations": 15, "kernel_calls": []}, {"file": "moe_wna16.py", "quantization_functions": ["override_quantization_method", "get_quant_method", "is_layer_skipped_quant"], "dequantization_functions": [], "int8_operations": 12, "kernel_calls": []}, {"file": "mxfp4.py", "quantization_functions": ["_dequant_mxfp4", "_dequant_mxfp4_fake", "_quant_dequant_mxfp4", "_quant_dequant_mxfp4_fake", "get_quant_method", "mxfp4_quantize"], "dequantization_functions": ["_dequant_mxfp4", "_dequant_mxfp4_fake", "_quant_dequant_mxfp4", "_quant_dequant_mxfp4_fake"], "int8_operations": 9, "kernel_calls": ["torch.ops.sglang", "torch.ops.sglang", "cuda.get_device_properties", "cuda.is_available", "cuda.empty_cache"]}, {"file": "mxfp4_tensor.py", "quantization_functions": ["quantize", "dequantize"], "dequantization_functions": ["dequantize"], "int8_operations": 10, "kernel_calls": []}, {"file": "petit.py", "quantization_functions": ["override_quantization_method", "get_quant_method"], "dequantization_functions": [], "int8_operations": 2, "kernel_calls": []}, {"file": "petit_utils.py", "quantization_functions": [], "dequantization_functions": [], "int8_operations": 0, "kernel_calls": []}, {"file": "qoq.py", "quantization_functions": ["get_quant_method"], "dequantization_functions": [], "int8_operations": 7, "kernel_calls": []}, {"file": "unquant.py", "quantization_functions": [], "dequantization_functions": [], "int8_operations": 1, "kernel_calls": ["torch.ops.sgl_kernel", "cuda.is_available", "cuda.empty_cache", "cuda.empty_cache", "torch.ops.sgl_kernel"]}, {"file": "utils.py", "quantization_functions": ["per_tensor_dequantize", "requantize_with_max_scale", "get_linear_quant_method", "quantize_weights", "gptq_quantize_weights"], "dequantization_functions": ["per_tensor_dequantize"], "int8_operations": 5, "kernel_calls": []}, {"file": "w4afp8.py", "quantization_functions": ["get_quant_method"], "dequantization_functions": [], "int8_operations": 2, "kernel_calls": []}, {"file": "w8a8_fp8.py", "quantization_functions": ["get_quant_method"], "dequantization_functions": [], "int8_operations": 0, "kernel_calls": []}, {"file": "w8a8_int8.py", "quantization_functions": ["get_quant_method"], "dequantization_functions": [], "int8_operations": 27, "kernel_calls": ["torch.ops.sgl_kernel", "torch.ops.sgl_kernel"]}, {"file": "compressed_tensors/__init__.py", "quantization_functions": [], "dequantization_functions": [], "int8_operations": 0, "kernel_calls": []}, {"file": "compressed_tensors/compressed_tensors.py", "quantization_functions": ["get_quant_method", "_quantization_scheme_map_from_config"], "dequantization_functions": [], "int8_operations": 0, "kernel_calls": ["cuda.get_device_capability"]}, {"file": "compressed_tensors/compressed_tensors_moe.py", "quantization_functions": [], "dequantization_functions": [], "int8_operations": 0, "kernel_calls": ["cuda.empty_cache", "cuda.empty_cache", "torch.ops.vllm"]}, {"file": "compressed_tensors/utils.py", "quantization_functions": ["is_activation_quantization_format"], "dequantization_functions": [], "int8_operations": 0, "kernel_calls": []}, {"file": "compressed_tensors/schemes/__init__.py", "quantization_functions": [], "dequantization_functions": [], "int8_operations": 0, "kernel_calls": []}, {"file": "compressed_tensors/schemes/compressed_tensors_scheme.py", "quantization_functions": [], "dequantization_functions": [], "int8_operations": 0, "kernel_calls": []}, {"file": "compressed_tensors/schemes/compressed_tensors_w8a16_fp8.py", "quantization_functions": [], "dequantization_functions": [], "int8_operations": 0, "kernel_calls": []}, {"file": "compressed_tensors/schemes/compressed_tensors_w8a8_fp8.py", "quantization_functions": [], "dequantization_functions": [], "int8_operations": 0, "kernel_calls": []}, {"file": "deep_gemm_wrapper/__init__.py", "quantization_functions": [], "dequantization_functions": [], "int8_operations": 0, "kernel_calls": []}, {"file": "deep_gemm_wrapper/compile_utils.py", "quantization_functions": [], "dequantization_functions": [], "int8_operations": 0, "kernel_calls": []}, {"file": "deep_gemm_wrapper/configurer.py", "quantization_functions": [], "dequantization_functions": [], "int8_operations": 0, "kernel_calls": ["cuda.get_device_capability", "cuda.current_device"]}, {"file": "deep_gemm_wrapper/entrypoint.py", "quantization_functions": [], "dequantization_functions": [], "int8_operations": 0, "kernel_calls": []}, {"file": "quark/__init__.py", "quantization_functions": [], "dequantization_functions": [], "int8_operations": 0, "kernel_calls": []}, {"file": "quark/quark.py", "quantization_functions": ["get_quant_method"], "dequantization_functions": [], "int8_operations": 0, "kernel_calls": []}, {"file": "quark/quark_moe.py", "quantization_functions": [], "dequantization_functions": [], "int8_operations": 1, "kernel_calls": []}, {"file": "quark/utils.py", "quantization_functions": [], "dequantization_functions": [], "int8_operations": 0, "kernel_calls": []}, {"file": "quark/schemes/__init__.py", "quantization_functions": [], "dequantization_functions": [], "int8_operations": 0, "kernel_calls": []}, {"file": "quark/schemes/quark_scheme.py", "quantization_functions": [], "dequantization_functions": [], "int8_operations": 0, "kernel_calls": []}, {"file": "quark/schemes/quark_w4a4_mxfp4.py", "quantization_functions": [], "dequantization_functions": [], "int8_operations": 2, "kernel_calls": []}], "int8_kernels": [], "quantization_flow": [], "dequantization_flow": []}, "operator_categories": {"core_operators": ["Linear transformations (MLP gate, up, down projections)", "Attention mechanisms (QKV, O projections)", "MoE routing and expert computation", "Quantization/Dequantization (W8A8)", "Normalization (RMSNorm)", "Activations (SiLU, SiluAndMul)", "Embeddings (<PERSON>ken, Position)"], "prefill_specific": ["Sequence-level attention computation", "Batch matrix multiplications", "Large activation computation", "KV cache filling"], "decode_specific": ["Single-token attention", "Incremental KV cache update", "Sampler operations", "Autoregressive generation"]}, "shape_analysis": {"typical_shapes": {"embedding": "[batch_size, seq_len, hidden_size]", "attention_weights": "[batch_size, num_heads, seq_len, seq_len]", "mlp_intermediate": "[batch_size, seq_len, intermediate_size]", "logits": "[batch_size, seq_len, vocab_size]"}, "tp_sharding": {"attention_heads": "num_heads // tp_size", "mlp_intermediate": "intermediate_size // tp_size", "vocab_parallel": "vocab_size // tp_size"}}}