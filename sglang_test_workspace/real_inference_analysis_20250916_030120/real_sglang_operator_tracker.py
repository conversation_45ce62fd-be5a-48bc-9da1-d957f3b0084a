#!/usr/bin/env python3
"""
SGLang 真实推理算子追踪器
基于实际的离线推理过程获取模型运算信息
严格按照用户要求，不虚构任何推理过程
"""

import os
import sys
import json
import time
import torch
import threading
from datetime import datetime
from collections import defaultdict, Counter
from typing import Dict, List, Any, Tuple, Optional
import logging

# 设置环境变量
os.environ["CUDA_VISIBLE_DEVICES"] = "0,1"

# 添加SGLang路径
sys.path.insert(0, '/workspace/sglang')

try:
    import sglang as sgl
    from sglang import Engine
    from sglang.srt.server_args import ServerArgs
    from sglang.srt.sampling.sampling_params import SamplingParams
    print("✅ 成功导入 SGLang")
except ImportError as e:
    print(f"❌ 导入 SGLang 失败: {e}")
    sys.exit(1)

class RealInferenceOperatorTracker:
    """真实推理算子追踪器"""
    
    def __init__(self):
        self.operator_logs = []
        self.phase_detection = {
            'prefill_detected': False,
            'decode_detected': False,
            'current_phase': 'unknown'
        }
        self.module_operations = defaultdict(list)
        self.tensor_shapes = defaultdict(list)
        self.performance_stats = defaultdict(list)
        self.hooks = []
        self.lock = threading.Lock()
        
        # 算子分类映射
        self.operator_categories = {
            # 注意力模块
            'attention': [
                'scaled_dot_product_attention', 'attention',
                'multi_head_attention', 'self_attention',
                'flash_attn', 'cutlass_scaled_mm'
            ],
            # 前馈网络
            'ffn': [
                'linear', 'addmm', 'mm', 'bmm', 'matmul',
                'gelu', 'relu', 'silu', 'swish'
            ],
            # 专家混合
            'expert': [
                'expert', 'moe', 'gate', 'router',
                'top_k', 'sparse'
            ],
            # MLP操作
            'mlp': [
                'gate_proj', 'up_proj', 'down_proj',
                'mlp', 'feed_forward'
            ],
            # 量化算子
            'quantization': [
                'int8_scaled_mm', 'quantize', 'dequantize',
                'w8a8', 'fp8', 'int4'
            ],
            # 其他计算
            'compute': [
                'conv', 'norm', 'layer_norm', 'rms_norm',
                'embedding', 'softmax', 'transpose'
            ]
        }
    
    def categorize_operator(self, op_name: str) -> str:
        """对算子进行分类"""
        op_lower = op_name.lower()
        for category, keywords in self.operator_categories.items():
            if any(keyword in op_lower for keyword in keywords):
                return category
        return 'other'
    
    def detect_phase(self, tensor_shapes: List[Tuple]) -> str:
        """基于张量形状检测推理阶段"""
        if not tensor_shapes:
            return 'unknown'
        
        # 分析序列长度模式
        seq_lens = []
        for shape in tensor_shapes:
            if len(shape) >= 2:
                seq_lens.append(shape[-2] if shape[-2] > 1 else shape[-1])
        
        if seq_lens:
            max_seq_len = max(seq_lens)
            if max_seq_len > 50:  # 长序列通常是prefill
                return 'prefill'
            elif max_seq_len <= 10:  # 短序列通常是decode
                return 'decode'
        
        return 'unknown'
    
    def hook_function(self, module, input_tensors, output_tensors):
        """Hook函数，捕获模块调用"""
        try:
            with self.lock:
                module_name = module.__class__.__name__
                timestamp = time.time()
                
                # 提取输入张量形状
                input_shapes = []
                if isinstance(input_tensors, (tuple, list)):
                    for inp in input_tensors:
                        if isinstance(inp, torch.Tensor):
                            input_shapes.append(tuple(inp.shape))
                
                # 提取输出张量形状
                output_shapes = []
                if isinstance(output_tensors, torch.Tensor):
                    output_shapes.append(tuple(output_tensors.shape))
                elif isinstance(output_tensors, (tuple, list)):
                    for out in output_tensors:
                        if isinstance(out, torch.Tensor):
                            output_shapes.append(tuple(out.shape))
                
                # 检测推理阶段
                current_phase = self.detect_phase(input_shapes + output_shapes)
                if current_phase != 'unknown':
                    self.phase_detection['current_phase'] = current_phase
                    if current_phase == 'prefill':
                        self.phase_detection['prefill_detected'] = True
                    elif current_phase == 'decode':
                        self.phase_detection['decode_detected'] = True
                
                # 记录算子信息
                operator_info = {
                    'timestamp': timestamp,
                    'module_name': module_name,
                    'phase': current_phase,
                    'input_shapes': input_shapes,
                    'output_shapes': output_shapes,
                    'category': self.categorize_operator(module_name)
                }
                
                self.operator_logs.append(operator_info)
                self.module_operations[module_name].append(operator_info)
                
        except Exception as e:
            print(f"Hook函数执行错误: {e}")
    
    def register_hooks(self, model):
        """为模型注册hooks"""
        def register_recursive(module, prefix=""):
            # 为当前模块注册hook
            hook = module.register_forward_hook(self.hook_function)
            self.hooks.append(hook)
            
            # 递归为子模块注册hooks
            for name, child in module.named_children():
                child_prefix = f"{prefix}.{name}" if prefix else name
                register_recursive(child, child_prefix)
        
        if hasattr(model, 'model'):
            register_recursive(model.model)
        else:
            register_recursive(model)
        
        print(f"✅ 已为模型注册 {len(self.hooks)} 个hooks")
    
    def cleanup_hooks(self):
        """清理hooks"""
        for hook in self.hooks:
            hook.remove()
        self.hooks.clear()
        print("✅ 已清理所有hooks")
    
    def run_real_inference(self, model_path: str, prompts: List[str]) -> Dict[str, Any]:
        """运行真实推理并收集算子信息"""
        print(f"🚀 开始真实推理追踪...")
        print(f"📍 模型路径: {model_path}")
        print(f"📝 测试提示数量: {len(prompts)}")
        
        try:
            # 初始化SGLang Engine
            print("🔧 初始化 SGLang Engine...")
            server_args = ServerArgs(
                model_path=model_path,
                tp_size=2,
                quantization="w8a8",
                trust_remote_code=True,
                disable_custom_all_reduce=True
            )
            engine = Engine(server_args)
            print("✅ SGLang Engine 初始化成功")
            
            # 注册hooks
            print("🎯 注册模型hooks...")
            self.register_hooks(engine.model_runner.model)
            
            # 执行真实推理
            print("🔄 开始执行真实推理...")
            results = []
            
            for i, prompt in enumerate(prompts):
                print(f"📊 处理提示 {i+1}/{len(prompts)}: {prompt[:50]}...")
                
                try:
                    # 使用SGLang Engine的generate方法进行真实推理
                    sampling_params = SamplingParams(
                        max_new_tokens=128,
                        temperature=0.8,
                        top_p=0.9
                    )
                    
                    outputs = engine.generate([prompt], sampling_params)
                    
                    if outputs and len(outputs) > 0:
                        result_text = outputs[0].text
                        results.append({
                            'prompt': prompt,
                            'response': result_text,
                            'operators_captured': len(self.operator_logs)
                        })
                        print(f"✅ 推理完成，捕获算子数: {len(self.operator_logs)}")
                    
                except Exception as e:
                    print(f"❌ 推理出错: {e}")
                    results.append({
                        'prompt': prompt,
                        'error': str(e),
                        'operators_captured': len(self.operator_logs)
                    })
            
            return {
                'inference_results': results,
                'total_operators': len(self.operator_logs),
                'phases_detected': self.phase_detection
            }
            
        except Exception as e:
            print(f"❌ 真实推理失败: {e}")
            return {'error': str(e)}
        
        finally:
            self.cleanup_hooks()
    
    def analyze_operators(self) -> Dict[str, Any]:
        """分析算子数据"""
        print("📊 开始分析算子数据...")
        
        if not self.operator_logs:
            return {'error': '没有捕获到算子数据'}
        
        # 按阶段分组
        phase_analysis = defaultdict(lambda: defaultdict(list))
        category_analysis = defaultdict(lambda: defaultdict(int))
        shape_analysis = defaultdict(list)
        
        for op_info in self.operator_logs:
            phase = op_info['phase']
            module = op_info['module_name']
            category = op_info['category']
            
            phase_analysis[phase][module].append(op_info)
            category_analysis[phase][category] += 1
            
            # 收集形状信息
            for shape in op_info['input_shapes'] + op_info['output_shapes']:
                shape_analysis[f"{phase}_{module}"].append(shape)
        
        # 生成统计信息
        statistics = {
            'total_operators': len(self.operator_logs),
            'phases_detected': dict(self.phase_detection),
            'phase_distribution': {
                phase: len(ops) for phase, ops in phase_analysis.items()
            },
            'category_distribution': dict(category_analysis),
            'module_distribution': {
                phase: {module: len(ops) for module, ops in modules.items()}
                for phase, modules in phase_analysis.items()
            },
            'unique_modules': len(set(op['module_name'] for op in self.operator_logs)),
            'unique_categories': len(set(op['category'] for op in self.operator_logs))
        }
        
        return {
            'statistics': statistics,
            'phase_analysis': dict(phase_analysis),
            'category_analysis': dict(category_analysis),
            'shape_analysis': dict(shape_analysis),
            'raw_logs': self.operator_logs[:100]  # 限制输出量
        }
    
    def save_results(self, analysis_results: Dict[str, Any], 
                    inference_results: Dict[str, Any], 
                    output_dir: str):
        """保存分析结果"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 保存JSON报告
        json_file = os.path.join(output_dir, f"real_inference_analysis_{timestamp}.json")
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump({
                'analysis_results': analysis_results,
                'inference_results': inference_results,
                'timestamp': timestamp
            }, f, indent=2, ensure_ascii=False)
        
        # 保存Markdown报告
        md_file = os.path.join(output_dir, f"SGLang真实推理算子分析报告_{timestamp}.md")
        with open(md_file, 'w', encoding='utf-8') as f:
            f.write(f"# SGLang DeepSeek V3 INT8 真实推理算子分析报告\n\n")
            f.write(f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            # 基本信息
            f.write("## 1. 基本信息\n\n")
            f.write(f"- **模型**: DeepSeek V3 INT8 (W8A8)\n")
            f.write(f"- **张量并行**: TP=2 (GPU 0,1)\n")
            f.write(f"- **推理框架**: SGLang\n")
            f.write(f"- **分析方式**: 真实离线推理\n\n")
            
            # 统计概览
            if 'statistics' in analysis_results:
                stats = analysis_results['statistics']
                f.write("## 2. 算子统计概览\n\n")
                f.write(f"- **总算子数量**: {stats['total_operators']}\n")
                f.write(f"- **唯一模块数**: {stats['unique_modules']}\n")
                f.write(f"- **算子类别数**: {stats['unique_categories']}\n")
                f.write(f"- **检测到的阶段**: {list(stats['phases_detected'].keys())}\n\n")
                
                # 阶段分布
                f.write("### 2.1 阶段分布\n\n")
                for phase, count in stats['phase_distribution'].items():
                    f.write(f"- **{phase}**: {count} 个算子\n")
                f.write("\n")
                
                # 类别分布
                f.write("### 2.2 算子类别分布\n\n")
                for phase, categories in stats['category_distribution'].items():
                    f.write(f"**{phase} 阶段**:\n")
                    for category, count in categories.items():
                        f.write(f"- {category}: {count} 次\n")
                    f.write("\n")
            
            # 推理结果
            f.write("## 3. 推理执行结果\n\n")
            if 'inference_results' in inference_results:
                results = inference_results['inference_results']
                f.write(f"- **执行的推理数量**: {len(results)}\n")
                f.write(f"- **成功推理数量**: {len([r for r in results if 'error' not in r])}\n")
                f.write(f"- **失败推理数量**: {len([r for r in results if 'error' in r])}\n\n")
        
        print(f"✅ 结果已保存:")
        print(f"   📄 JSON: {json_file}")
        print(f"   📄 Markdown: {md_file}")
        
        return json_file, md_file

def main():
    """主函数"""
    print("🎯 SGLang 真实推理算子追踪器启动")
    print("=" * 60)
    
    # 创建追踪器
    tracker = RealInferenceOperatorTracker()
    
    # 模型路径
    model_path = "/workspace/deepseek-ai/DeepSeek-V3"
    
    # 测试提示
    test_prompts = [
        "Hello, how are you?",
        "What is artificial intelligence?",
        "Explain quantum computing in simple terms.",
    ]
    
    # 运行真实推理
    print("🚀 开始真实推理追踪...")
    inference_results = tracker.run_real_inference(model_path, test_prompts)
    
    # 分析算子数据
    print("📊 分析算子数据...")
    analysis_results = tracker.analyze_operators()
    
    # 保存结果
    output_dir = os.getcwd()
    json_file, md_file = tracker.save_results(analysis_results, inference_results, output_dir)
    
    print("\n" + "=" * 60)
    print("✅ 真实推理算子追踪完成!")
    print(f"📊 捕获算子总数: {len(tracker.operator_logs)}")
    print(f"🔍 检测阶段: {tracker.phase_detection}")
    print("=" * 60)

if __name__ == "__main__":
    main()
