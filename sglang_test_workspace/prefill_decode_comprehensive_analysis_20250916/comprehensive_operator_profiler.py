#!/usr/bin/env python3
"""
全面的SGLang算子性能和shape分析工具
专注于prefill和decode两个阶段的所有计算相关算子
支持双卡测试(GPU 0,1)
"""

import os
import sys
import json
import time
import logging
import traceback
from datetime import datetime
from collections import defaultdict, Counter
from typing import Dict, List, Any, Tuple, Optional
from dataclasses import dataclass, asdict
import threading
import psutil

import torch
import torch.nn.functional as F
from torch.profiler import profile, record_function, ProfilerActivity

# 设置环境变量
os.environ["CUDA_VISIBLE_DEVICES"] = "0,1"  # 使用GPU 0和1
os.environ["TOKENIZERS_PARALLELISM"] = "false"

# 添加sglang路径
sys.path.insert(0, "/workspace/sglang_test/lib/python3.10/site-packages")

try:
    import sglang as sgl
    from sglang import Engine
    from sglang.srt.constrained import disable_cache
    from sglang.srt.utils import kill_child_process
except ImportError as e:
    print(f"SGLang导入失败: {e}")
    sys.exit(1)

@dataclass
class OperatorMetrics:
    """算子性能指标"""
    name: str
    phase: str  # prefill, decode, model_loading
    call_count: int
    total_time: float
    avg_time: float
    input_shapes: List[Tuple]
    output_shapes: List[Tuple]
    input_dtypes: List[str]
    output_dtypes: List[str]
    memory_before: int
    memory_after: int
    gpu_utilization: float
    tensor_parallel_rank: int = -1

@dataclass
class PhaseMetrics:
    """阶段性能指标"""
    phase_name: str
    start_time: float
    end_time: float
    duration: float
    operators: List[OperatorMetrics]
    peak_memory: int
    total_flops: int
    gpu_ids: List[int]

class ComprehensiveOperatorProfiler:
    """全面的算子性能分析器"""
    
    def __init__(self, output_dir: str):
        self.output_dir = output_dir
        self.current_phase = "unknown"
        self.operators = []
        self.phase_metrics = []
        self.operator_stats = defaultdict(lambda: {
            'count': 0, 'total_time': 0.0, 'shapes': [], 'phases': set()
        })
        
        # 性能监控
        self.start_time = time.time()
        self.memory_tracker = {}
        
        # 日志设置
        self.setup_logging()
        
        # 原始函数保存
        self.original_functions = {}
        
        # 线程锁
        self.lock = threading.Lock()
        
        self.logger.info("=== 初始化全面算子性能分析器 ===")
        self.logger.info(f"使用GPU: {os.environ.get('CUDA_VISIBLE_DEVICES', 'ALL')}")
        self.logger.info(f"输出目录: {output_dir}")

    def setup_logging(self):
        """设置日志"""
        log_file = os.path.join(self.output_dir, f"profiler_{int(time.time())}.log")
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)

    def get_memory_usage(self) -> Dict[int, int]:
        """获取所有GPU的内存使用情况"""
        memory_usage = {}
        if torch.cuda.is_available():
            for i in range(torch.cuda.device_count()):
                memory_usage[i] = torch.cuda.memory_allocated(i)
        return memory_usage

    def get_gpu_utilization(self) -> float:
        """获取GPU利用率"""
        try:
            # 使用nvidia-smi命令获取GPU利用率
            import subprocess
            result = subprocess.run(['nvidia-smi', '--query-gpu=utilization.gpu', '--format=csv,noheader,nounits'], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                if lines:
                    return float(lines[0].strip())
        except:
            pass
        return 0.0

    def get_tensor_shapes_and_dtypes(self, *args, **kwargs) -> Tuple[List, List]:
        """获取张量的形状和数据类型"""
        shapes = []
        dtypes = []
        
        for arg in args:
            if isinstance(arg, torch.Tensor):
                shapes.append(tuple(arg.shape))
                dtypes.append(str(arg.dtype))
            elif isinstance(arg, (list, tuple)):
                for item in arg:
                    if isinstance(item, torch.Tensor):
                        shapes.append(tuple(item.shape))
                        dtypes.append(str(item.dtype))
        
        for value in kwargs.values():
            if isinstance(value, torch.Tensor):
                shapes.append(tuple(value.shape))
                dtypes.append(str(value.dtype))
        
        return shapes, dtypes

    def create_operator_wrapper(self, func_name: str, original_func):
        """创建算子包装器"""
        def wrapper(*args, **kwargs):
            start_time = time.perf_counter()
            memory_before = self.get_memory_usage()
            gpu_util = self.get_gpu_utilization()
            
            # 获取输入张量信息
            input_shapes, input_dtypes = self.get_tensor_shapes_and_dtypes(*args, **kwargs)
            
            # 执行原函数
            result = original_func(*args, **kwargs)
            
            end_time = time.perf_counter()
            memory_after = self.get_memory_usage()
            execution_time = end_time - start_time
            
            # 获取输出张量信息
            if isinstance(result, torch.Tensor):
                output_shapes = [tuple(result.shape)]
                output_dtypes = [str(result.dtype)]
            elif isinstance(result, (list, tuple)):
                output_shapes = []
                output_dtypes = []
                for item in result:
                    if isinstance(item, torch.Tensor):
                        output_shapes.append(tuple(item.shape))
                        output_dtypes.append(str(item.dtype))
            else:
                output_shapes = []
                output_dtypes = []
            
            # 记录算子信息
            with self.lock:
                operator_metric = OperatorMetrics(
                    name=func_name,
                    phase=self.current_phase,
                    call_count=1,
                    total_time=execution_time,
                    avg_time=execution_time,
                    input_shapes=input_shapes,
                    output_shapes=output_shapes,
                    input_dtypes=input_dtypes,
                    output_dtypes=output_dtypes,
                    memory_before=sum(memory_before.values()) if memory_before else 0,
                    memory_after=sum(memory_after.values()) if memory_after else 0,
                    gpu_utilization=gpu_util
                )
                
                self.operators.append(operator_metric)
                
                # 更新统计
                stats = self.operator_stats[func_name]
                stats['count'] += 1
                stats['total_time'] += execution_time
                stats['shapes'].extend(input_shapes)
                stats['phases'].add(self.current_phase)
                
                # 日志记录
                if execution_time > 0.001:  # 只记录耗时超过1ms的操作
                    self.logger.info(f"[{self.current_phase}] {func_name}: {execution_time:.4f}s, "
                                   f"输入: {input_shapes}, 输出: {output_shapes}")
            
            return result
        
        return wrapper

    def hook_pytorch_operations(self):
        """Hook PyTorch核心操作"""
        operations_to_hook = [
            # 线性代数操作
            (torch, 'mm'),
            (torch, 'bmm'),
            (torch, 'addmm'),
            (torch, 'baddbmm'),
            (torch, 'matmul'),
            (torch.nn.functional, 'linear'),
            
            # 激活函数
            (torch.nn.functional, 'relu'),
            (torch.nn.functional, 'gelu'),
            (torch.nn.functional, 'silu'),
            (torch.nn.functional, 'swish'),
            (torch.nn.functional, 'tanh'),
            (torch.nn.functional, 'sigmoid'),
            (torch.nn.functional, 'softmax'),
            (torch.nn.functional, 'log_softmax'),
            
            # 注意力相关
            (torch.nn.functional, 'scaled_dot_product_attention'),
            
            # 归一化
            (torch.nn.functional, 'layer_norm'),
            (torch.nn.functional, 'group_norm'),
            (torch.nn.functional, 'batch_norm'),
            (torch.nn.functional, 'instance_norm'),
            
            # 卷积操作
            (torch.nn.functional, 'conv1d'),
            (torch.nn.functional, 'conv2d'),
            (torch.nn.functional, 'conv3d'),
            
            # 池化操作
            (torch.nn.functional, 'max_pool1d'),
            (torch.nn.functional, 'max_pool2d'),
            (torch.nn.functional, 'avg_pool1d'),
            (torch.nn.functional, 'avg_pool2d'),
            
            # 张量操作
            (torch, 'cat'),
            (torch, 'stack'),
            (torch, 'split'),
            (torch, 'chunk'),
            (torch, 'transpose'),
            (torch, 'permute'),
            (torch, 'reshape'),
            (torch, 'view'),
            (torch, 'squeeze'),
            (torch, 'unsqueeze'),
            
            # 数学运算
            (torch, 'add'),
            (torch, 'sub'),
            (torch, 'mul'),
            (torch, 'div'),
            (torch, 'pow'),
            (torch, 'sqrt'),
            (torch, 'exp'),
            (torch, 'log'),
            (torch, 'sin'),
            (torch, 'cos'),
            
            # 缩减操作
            (torch, 'sum'),
            (torch, 'mean'),
            (torch, 'max'),
            (torch, 'min'),
            (torch, 'std'),
            (torch, 'var'),
        ]
        
        for module, func_name in operations_to_hook:
            if hasattr(module, func_name):
                original_func = getattr(module, func_name)
                self.original_functions[f"{module.__name__}.{func_name}"] = original_func
                wrapped_func = self.create_operator_wrapper(f"{module.__name__}.{func_name}", original_func)
                setattr(module, func_name, wrapped_func)
                self.logger.info(f"已Hook: {module.__name__}.{func_name}")

    def hook_sglang_operations(self):
        """Hook SGLang特定操作"""
        try:
            # Hook SGLang模型相关函数
            sglang_operations = []
            
            # 查找SGLang中的关键模块
            try:
                from sglang.srt.models.deepseek import DeepseekForCausalLM
                sglang_operations.extend([
                    (DeepseekForCausalLM, 'forward'),
                ])
            except ImportError:
                pass
            
            try:
                from sglang.srt.layers.linear import LinearBase
                sglang_operations.extend([
                    (LinearBase, 'forward'),
                ])
            except ImportError:
                pass
            
            try:
                from sglang.srt.layers.attention import Attention
                sglang_operations.extend([
                    (Attention, 'forward'),
                ])
            except ImportError:
                pass
            
            # Hook找到的操作
            for module_class, method_name in sglang_operations:
                if hasattr(module_class, method_name):
                    original_method = getattr(module_class, method_name)
                    self.original_functions[f"{module_class.__name__}.{method_name}"] = original_method
                    wrapped_method = self.create_operator_wrapper(
                        f"sglang.{module_class.__name__}.{method_name}", 
                        original_method
                    )
                    setattr(module_class, method_name, wrapped_method)
                    self.logger.info(f"已Hook SGLang: {module_class.__name__}.{method_name}")
                    
        except Exception as e:
            self.logger.warning(f"Hook SGLang操作时出错: {e}")

    def hook_custom_kernels(self):
        """Hook自定义内核操作"""
        try:
            # Hook sgl_kernel中的操作
            import sgl_kernel
            if hasattr(sgl_kernel, 'int8_scaled_mm'):
                original_func = sgl_kernel.int8_scaled_mm
                self.original_functions['sgl_kernel.int8_scaled_mm'] = original_func
                wrapped_func = self.create_operator_wrapper('sgl_kernel.int8_scaled_mm', original_func)
                sgl_kernel.int8_scaled_mm = wrapped_func
                self.logger.info("已Hook: sgl_kernel.int8_scaled_mm")
                
        except ImportError:
            self.logger.warning("未找到sgl_kernel模块")
        except Exception as e:
            self.logger.warning(f"Hook自定义内核时出错: {e}")

    def set_phase(self, phase: str):
        """设置当前阶段"""
        with self.lock:
            old_phase = self.current_phase
            self.current_phase = phase
            current_time = time.time()
            
            if old_phase != "unknown":
                # 结束上一个阶段
                phase_end_time = current_time
                self.logger.info(f"=== 阶段结束: {old_phase} ===")
            
            self.logger.info(f"=== 阶段开始: {phase} ===")

    def start_profiling(self):
        """开始性能分析"""
        self.logger.info("=== 开始性能分析 ===")
        
        # Hook各种操作
        self.hook_pytorch_operations()
        self.hook_sglang_operations()
        self.hook_custom_kernels()
        
        self.logger.info("所有Hook已设置完成")

    def stop_profiling(self):
        """停止性能分析"""
        self.logger.info("=== 停止性能分析 ===")
        
        # 恢复原始函数
        for func_path, original_func in self.original_functions.items():
            try:
                module_path, func_name = func_path.rsplit('.', 1)
                if module_path == 'torch':
                    setattr(torch, func_name, original_func)
                elif module_path == 'torch.nn.functional':
                    setattr(torch.nn.functional, func_name, original_func)
                elif 'sglang' in module_path:
                    # SGLang函数恢复需要特殊处理
                    pass
                elif module_path == 'sgl_kernel':
                    import sgl_kernel
                    setattr(sgl_kernel, func_name, original_func)
            except Exception as e:
                self.logger.warning(f"恢复函数 {func_path} 时出错: {e}")

    def analyze_prefill_decode_patterns(self) -> Dict[str, Any]:
        """分析prefill和decode阶段的模式"""
        analysis = {
            'prefill': {'operators': [], 'total_time': 0, 'memory_peak': 0},
            'decode': {'operators': [], 'total_time': 0, 'memory_peak': 0},
            'comparison': {}
        }
        
        # 按阶段分组算子
        prefill_ops = [op for op in self.operators if op.phase == 'prefill']
        decode_ops = [op for op in self.operators if op.phase == 'decode']
        
        # 分析prefill阶段
        if prefill_ops:
            prefill_stats = defaultdict(lambda: {'count': 0, 'total_time': 0, 'shapes': []})
            for op in prefill_ops:
                prefill_stats[op.name]['count'] += op.call_count
                prefill_stats[op.name]['total_time'] += op.total_time
                prefill_stats[op.name]['shapes'].extend(op.input_shapes)
            
            analysis['prefill']['operators'] = dict(prefill_stats)
            analysis['prefill']['total_time'] = sum(op.total_time for op in prefill_ops)
            analysis['prefill']['memory_peak'] = max(op.memory_after for op in prefill_ops)
        
        # 分析decode阶段
        if decode_ops:
            decode_stats = defaultdict(lambda: {'count': 0, 'total_time': 0, 'shapes': []})
            for op in decode_ops:
                decode_stats[op.name]['count'] += op.call_count
                decode_stats[op.name]['total_time'] += op.total_time
                decode_stats[op.name]['shapes'].extend(op.input_shapes)
            
            analysis['decode']['operators'] = dict(decode_stats)
            analysis['decode']['total_time'] = sum(op.total_time for op in decode_ops)
            analysis['decode']['memory_peak'] = max(op.memory_after for op in decode_ops)
        
        # 对比分析
        common_ops = set(analysis['prefill']['operators'].keys()) & set(analysis['decode']['operators'].keys())
        for op_name in common_ops:
            prefill_data = analysis['prefill']['operators'][op_name]
            decode_data = analysis['decode']['operators'][op_name]
            
            analysis['comparison'][op_name] = {
                'prefill_time': prefill_data['total_time'],
                'decode_time': decode_data['total_time'],
                'time_ratio': prefill_data['total_time'] / max(decode_data['total_time'], 1e-9),
                'prefill_shapes': list(set(prefill_data['shapes'])),
                'decode_shapes': list(set(decode_data['shapes']))
            }
        
        return analysis

    def generate_report(self) -> str:
        """生成分析报告"""
        timestamp = int(time.time())
        report_file = os.path.join(self.output_dir, f"comprehensive_analysis_report_{timestamp}.json")
        
        # 基础统计
        total_operators = len(self.operators)
        total_time = sum(op.total_time for op in self.operators)
        
        # 阶段分析
        phase_analysis = self.analyze_prefill_decode_patterns()
        
        # 算子排行
        op_time_ranking = sorted(
            self.operator_stats.items(),
            key=lambda x: x[1]['total_time'],
            reverse=True
        )[:20]
        
        # 内存分析
        memory_stats = {
            'peak_memory': max(op.memory_after for op in self.operators) if self.operators else 0,
            'avg_memory': sum(op.memory_after for op in self.operators) / len(self.operators) if self.operators else 0
        }
        
        # 生成完整报告
        report = {
            'metadata': {
                'timestamp': timestamp,
                'total_operators': total_operators,
                'total_execution_time': total_time,
                'profiling_duration': time.time() - self.start_time,
                'gpu_devices': os.environ.get('CUDA_VISIBLE_DEVICES', 'ALL')
            },
            'phase_analysis': phase_analysis,
            'operator_ranking': [
                {
                    'name': name,
                    'total_time': stats['total_time'],
                    'call_count': stats['count'],
                    'avg_time': stats['total_time'] / stats['count'],
                    'phases': list(stats['phases']),
                    'unique_shapes': len(set(stats['shapes']))
                }
                for name, stats in op_time_ranking
            ],
            'memory_analysis': memory_stats,
            'detailed_operators': [asdict(op) for op in self.operators],
            'shape_patterns': self.analyze_shape_patterns()
        }
        
        # 保存报告
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"分析报告已保存: {report_file}")
        return report_file

    def analyze_shape_patterns(self) -> Dict[str, Any]:
        """分析张量形状模式"""
        shape_analysis = {
            'common_shapes': Counter(),
            'operator_shapes': defaultdict(set),
            'shape_progression': []
        }
        
        for op in self.operators:
            # 统计常见形状
            for shape in op.input_shapes:
                shape_analysis['common_shapes'][str(shape)] += 1
            
            # 按算子统计形状
            shape_analysis['operator_shapes'][op.name].update(str(s) for s in op.input_shapes)
        
        # 转换为可序列化格式
        shape_analysis['common_shapes'] = dict(shape_analysis['common_shapes'].most_common(20))
        shape_analysis['operator_shapes'] = {k: list(v) for k, v in shape_analysis['operator_shapes'].items()}
        
        return shape_analysis


def run_comprehensive_test():
    """运行全面测试"""
    output_dir = "/workspace/sglang_test_workspace/prefill_decode_comprehensive_analysis_20250916"
    
    # 初始化分析器
    profiler = ComprehensiveOperatorProfiler(output_dir)
    
    try:
        # 开始性能分析
        profiler.start_profiling()
        
        # 设置模型参数
        model_path = "deepseek-ai/DeepSeek-V3"
        
        # 阶段1: 模型加载
        profiler.set_phase("model_loading")
        profiler.logger.info("开始模型加载...")
        
        engine = sgl.Engine(
            model_path=model_path,
            tokenizer_path=model_path,
            tp_size=2,  # 使用2张卡
            context_length=4096,
            mem_fraction_static=0.8,
            dtype="auto",
            quantization="int8",
            trust_remote_code=True,
            disable_flashinfer_sampling=True
        )
        
        profiler.logger.info("模型加载完成")
        
        # 测试用例
        test_cases = [
            {
                'prompt': "请介绍一下人工智能的发展历史，包括主要的里程碑事件。",
                'max_tokens': 100,
                'description': "中等长度prompt，中等生成长度"
            },
            {
                'prompt': "什么是机器学习？",
                'max_tokens': 50,
                'description': "短prompt，短生成"
            },
            {
                'prompt': "请详细解释深度学习中的注意力机制，包括其工作原理、数学公式、不同类型的注意力机制（如自注意力、交叉注意力等），以及在Transformer架构中的具体应用。同时，请分析注意力机制为什么能够有效解决序列建模中的长距离依赖问题，并比较它与传统RNN和CNN方法的优势。",
                'max_tokens': 200,
                'description': "长prompt，长生成"
            }
        ]
        
        for i, test_case in enumerate(test_cases):
            profiler.logger.info(f"\n=== 测试用例 {i+1}: {test_case['description']} ===")
            
            # 阶段2: Prefill
            profiler.set_phase("prefill")
            profiler.logger.info("开始Prefill阶段...")
            
            # 执行生成
            response = engine.generate(
                prompt=test_case['prompt'],
                sampling_params={
                    "max_new_tokens": test_case['max_tokens'],
                    "temperature": 0.7,
                    "top_p": 0.9,
                    "do_sample": True
                }
            )
            
            # 阶段3: Decode
            profiler.set_phase("decode")
            profiler.logger.info("Decode阶段已在生成过程中完成")
            
            profiler.logger.info(f"生成完成，长度: {len(response.text)}")
            
            # 添加短暂延迟以分隔测试
            time.sleep(1)
        
        # 停止性能分析
        profiler.stop_profiling()
        
        # 生成最终报告
        report_file = profiler.generate_report()
        profiler.logger.info(f"=== 分析完成，报告文件: {report_file} ===")
        
    except Exception as e:
        profiler.logger.error(f"测试过程中出错: {e}")
        profiler.logger.error(traceback.format_exc())
    
    finally:
        try:
            if 'engine' in locals():
                engine.shutdown()
                profiler.logger.info("引擎已关闭")
        except:
            pass


if __name__ == "__main__":
    print("=== SGLang Prefill/Decode 全面算子性能分析 ===")
    print("使用GPU: 0,1")
    print("开始分析...")
    
    run_comprehensive_test()
    
    print("分析完成！")
