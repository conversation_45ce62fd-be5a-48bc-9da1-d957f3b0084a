2025-09-16 02:20:46,972 - INFO - 启动双GPU (0,1) 综合算子性能和shape分析
2025-09-16 02:20:48,730 - INFO - 发现2个GPU，将使用GPU 0和1进行测试
2025-09-16 02:20:48,730 - INFO - 开始SGLang双GPU测试...
2025-09-16 02:20:48,737 - ERROR - SGLang测试失败: No module named 'pybase64'
2025-09-16 02:20:48,738 - ERROR - Traceback (most recent call last):
  File "/workspace/sglang_test_workspace/prefill_decode_comprehensive_analysis_20250916/run_dual_gpu_analysis.py", line 234, in run_sglang_test
    import sglang as sgl
  File "/workspace/sglang/python/sglang/__init__.py", line 5, in <module>
    from sglang.lang.api import (
  File "/workspace/sglang/python/sglang/lang/api.py", line 7, in <module>
    from sglang.lang.backend.base_backend import BaseBackend
  File "/workspace/sglang/python/sglang/lang/backend/base_backend.py", line 5, in <module>
    from sglang.lang.interpreter import StreamExecutor
  File "/workspace/sglang/python/sglang/lang/interpreter.py", line 35, in <module>
    from sglang.utils import (
  File "/workspace/sglang/python/sglang/utils.py", line 22, in <module>
    import pybase64
ModuleNotFoundError: No module named 'pybase64'

2025-09-16 02:20:48,739 - ERROR - 分析过程中发生错误: float division by zero
2025-09-16 02:20:48,739 - ERROR - Traceback (most recent call last):
  File "/workspace/sglang_test_workspace/prefill_decode_comprehensive_analysis_20250916/run_dual_gpu_analysis.py", line 418, in main
    json_file, report_file = profiler.save_results()
  File "/workspace/sglang_test_workspace/prefill_decode_comprehensive_analysis_20250916/run_dual_gpu_analysis.py", line 389, in save_results
    f.write(f"| 平均每次调用时间(ms) | {prefill_time/max(prefill_ops,1):.2f} | {decode_time/max(decode_ops,1):.2f} | {(prefill_time/max(prefill_ops,1))/(decode_time/max(decode_ops,1)):.2f} |\n")
ZeroDivisionError: float division by zero

