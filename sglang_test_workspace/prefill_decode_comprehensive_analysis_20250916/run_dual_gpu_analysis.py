#!/usr/bin/env python3
"""
双GPU (0,1) 综合算子性能和shape分析测试脚本
从prefill和decode两个阶段进行全面的算子分析
"""

import os
import sys
import json
import time
import logging
import subprocess
import threading
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime

# 添加sglang到Python路径
sys.path.insert(0, '/workspace/sglang/python')

# 设置环境变量
os.environ['CUDA_VISIBLE_DEVICES'] = '0,1'  # 使用GPU 0和1
os.environ['TOKENIZERS_PARALLELISM'] = 'false'
os.environ['NCCL_DEBUG'] = 'INFO'

import torch
import torch.nn as nn
from torch.profiler import profile, record_function, ProfilerActivity

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('dual_gpu_analysis.log'),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)

logger = setup_logging()

class DualGPUOperatorProfiler:
    """双GPU算子性能分析器"""
    
    def __init__(self):
        self.phase_data = {
            'prefill': {
                'operators': [],
                'shapes': {},
                'memory_usage': {},
                'timings': {},
                'gpu_utilization': {}
            },
            'decode': {
                'operators': [],
                'shapes': {},
                'memory_usage': {},
                'timings': {},
                'gpu_utilization': {}
            }
        }
        self.current_phase = None
        self.hooks = []
        self.start_time = None
        
    def register_hooks(self):
        """注册所有模块的前向钩子"""
        try:
            import sglang as sgl
            from sglang.srt.model_executor.model_runner import ModelRunner
            from sglang.srt.layers.linear import (
                LinearBase, MergedColumnParallelLinear, QKVParallelLinear,
                RowParallelLinear, ColumnParallelLinear
            )
            from sglang.srt.layers.quantization.w8a8_int8 import W8A8Int8LinearMethod
            from sglang.srt.layers.radix_attention import RadixAttention
            
            # 关键算子类列表
            target_classes = [
                nn.Linear,
                LinearBase,
                MergedColumnParallelLinear,
                QKVParallelLinear,
                RowParallelLinear,
                ColumnParallelLinear,
                W8A8Int8LinearMethod,
                RadixAttention,
                nn.Embedding,
                nn.LayerNorm,
                nn.RMSNorm if hasattr(nn, 'RMSNorm') else type(None)
            ]
            
            # 为每个类注册钩子
            for cls in target_classes:
                if cls is None or cls is type(None):
                    continue
                    
                original_forward = cls.forward
                
                def create_hook(class_name):
                    def hooked_forward(self, *args, **kwargs):
                        if self.current_phase is None:
                            return original_forward(self, *args, **kwargs)
                            
                        start_time = time.perf_counter()
                        
                        # 记录输入形状
                        input_shapes = []
                        for arg in args:
                            if hasattr(arg, 'shape'):
                                input_shapes.append(list(arg.shape))
                        
                        # 记录GPU内存使用
                        memory_before = {}
                        for i in [0, 1]:  # GPU 0和1
                            if torch.cuda.is_available() and i < torch.cuda.device_count():
                                torch.cuda.synchronize(i)
                                memory_before[f'gpu_{i}'] = torch.cuda.memory_allocated(i)
                        
                        # 执行前向传播
                        with record_function(f"{class_name}_{self.current_phase}"):
                            result = original_forward(self, *args, **kwargs)
                        
                        # 记录执行时间
                        end_time = time.perf_counter()
                        execution_time = (end_time - start_time) * 1000  # 转换为毫秒
                        
                        # 记录输出形状
                        output_shapes = []
                        if hasattr(result, 'shape'):
                            output_shapes.append(list(result.shape))
                        elif isinstance(result, (tuple, list)):
                            for item in result:
                                if hasattr(item, 'shape'):
                                    output_shapes.append(list(item.shape))
                        
                        # 记录GPU内存使用
                        memory_after = {}
                        for i in [0, 1]:  # GPU 0和1
                            if torch.cuda.is_available() and i < torch.cuda.device_count():
                                torch.cuda.synchronize(i)
                                memory_after[f'gpu_{i}'] = torch.cuda.memory_allocated(i)
                        
                        # 记录算子信息
                        op_info = {
                            'name': class_name,
                            'phase': self.current_phase,
                            'input_shapes': input_shapes,
                            'output_shapes': output_shapes,
                            'execution_time_ms': execution_time,
                            'memory_before': memory_before,
                            'memory_after': memory_after,
                            'memory_delta': {k: memory_after.get(k, 0) - memory_before.get(k, 0) 
                                           for k in memory_before.keys()},
                            'timestamp': time.time()
                        }
                        
                        self.phase_data[self.current_phase]['operators'].append(op_info)
                        
                        return result
                    
                    return hooked_forward
                
                # 应用钩子
                cls.forward = create_hook(cls.__name__)
                self.hooks.append((cls, original_forward))
                
        except Exception as e:
            logger.error(f"注册钩子时出错: {e}")
    
    def restore_hooks(self):
        """恢复原始的前向传播函数"""
        for cls, original_forward in self.hooks:
            cls.forward = original_forward
        self.hooks.clear()
    
    def get_gpu_info(self):
        """获取GPU信息"""
        gpu_info = {}
        try:
            result = subprocess.run(['nvidia-smi', '--query-gpu=index,name,memory.total,memory.used,utilization.gpu', 
                                   '--format=csv,noheader,nounits'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                for line in lines:
                    parts = line.split(', ')
                    if len(parts) >= 5:
                        gpu_idx = int(parts[0])
                        if gpu_idx in [0, 1]:  # 只关注GPU 0和1
                            gpu_info[f'gpu_{gpu_idx}'] = {
                                'name': parts[1],
                                'memory_total_mb': int(parts[2]),
                                'memory_used_mb': int(parts[3]),
                                'utilization_percent': int(parts[4])
                            }
        except Exception as e:
            logger.warning(f"获取GPU信息失败: {e}")
        
        return gpu_info
    
    def start_phase(self, phase_name: str):
        """开始新的分析阶段"""
        self.current_phase = phase_name
        self.start_time = time.perf_counter()
        logger.info(f"开始 {phase_name} 阶段分析")
        
        # 记录开始时的GPU状态
        self.phase_data[phase_name]['gpu_utilization']['start'] = self.get_gpu_info()
    
    def end_phase(self):
        """结束当前分析阶段"""
        if self.current_phase is None:
            return
            
        end_time = time.perf_counter()
        phase_duration = (end_time - self.start_time) * 1000  # 转换为毫秒
        
        # 记录结束时的GPU状态
        self.phase_data[self.current_phase]['gpu_utilization']['end'] = self.get_gpu_info()
        self.phase_data[self.current_phase]['timings']['total_phase_time_ms'] = phase_duration
        
        logger.info(f"完成 {self.current_phase} 阶段分析，用时: {phase_duration:.2f}ms")
        self.current_phase = None
    
    def run_sglang_test(self):
        """运行SGLang测试"""
        try:
            logger.info("开始SGLang双GPU测试...")
            
            # 导入SGLang
            import sglang as sgl
            
            # 设置运行时配置
            runtime_args = {
                "model_path": "/workspace/models/deepseek-ai/DeepSeek-V3",
                "tp_size": 2,  # 使用2个GPU
                "device": "cuda",
                "trust_remote_code": True,
                "quantization": "w8a8_int8",
                "dtype": "bfloat16",
                "mem_fraction_static": 0.8,
                "disable_custom_all_reduce": True,
                "enable_torch_compile": False,
            }
            
            # 启动运行时
            logger.info("启动SGLang运行时...")
            sgl.set_default_backend(sgl.RuntimeEndpoint("http://127.0.0.1:30000"))
            
            # 注册钩子
            self.register_hooks()
            
            # 测试prompts
            test_prompts = [
                "请解释什么是深度学习？",  # 短prompt，测试prefill
                "请详细介绍人工智能的发展历史，包括关键里程碑事件、重要人物、技术突破以及对社会的影响。同时分析当前AI技术的局限性和未来发展趋势。",  # 长prompt，测试prefill
            ]
            
            for i, prompt in enumerate(test_prompts):
                logger.info(f"测试prompt {i+1}: {prompt[:50]}...")
                
                # Prefill阶段分析
                self.start_phase('prefill')
                
                with profile(
                    activities=[ProfilerActivity.CPU, ProfilerActivity.CUDA],
                    record_shapes=True,
                    with_stack=True
                ) as prof:
                    # 模拟prefill阶段
                    response = sgl.gen(prompt, max_new_tokens=1, stop=None)
                
                self.end_phase()
                
                # Decode阶段分析
                self.start_phase('decode')
                
                with profile(
                    activities=[ProfilerActivity.CPU, ProfilerActivity.CUDA],
                    record_shapes=True,
                    with_stack=True
                ) as prof:
                    # 模拟decode阶段
                    response = sgl.gen(prompt, max_new_tokens=50, stop=None)
                
                self.end_phase()
                
                logger.info(f"Prompt {i+1} 完成")
            
        except Exception as e:
            logger.error(f"SGLang测试失败: {e}")
            import traceback
            logger.error(traceback.format_exc())
        finally:
            # 恢复钩子
            self.restore_hooks()
    
    def save_results(self):
        """保存分析结果"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 保存详细的JSON数据
        json_file = f"dual_gpu_analysis_{timestamp}.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(self.phase_data, f, indent=2, ensure_ascii=False)
        
        # 生成分析报告
        report_file = f"双GPU_Prefill_Decode_算子分析报告_{timestamp}.md"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("# 双GPU (0,1) Prefill/Decode 阶段算子性能分析报告\n\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            for phase in ['prefill', 'decode']:
                f.write(f"## {phase.upper()} 阶段分析\n\n")
                
                phase_data = self.phase_data[phase]
                operators = phase_data['operators']
                
                if not operators:
                    f.write("未捕获到算子数据\n\n")
                    continue
                
                # 统计信息
                f.write(f"### 基本统计\n")
                f.write(f"- 总算子调用次数: {len(operators)}\n")
                f.write(f"- 总执行时间: {phase_data['timings'].get('total_phase_time_ms', 0):.2f}ms\n")
                
                # 算子类型统计
                op_types = {}
                for op in operators:
                    op_name = op['name']
                    if op_name not in op_types:
                        op_types[op_name] = {'count': 0, 'total_time': 0, 'shapes': set()}
                    op_types[op_name]['count'] += 1
                    op_types[op_name]['total_time'] += op['execution_time_ms']
                    for shape in op['input_shapes'] + op['output_shapes']:
                        op_types[op_name]['shapes'].add(str(shape))
                
                f.write(f"\n### 算子类型分布\n")
                f.write("| 算子类型 | 调用次数 | 总时间(ms) | 平均时间(ms) | 主要形状 |\n")
                f.write("|----------|----------|------------|-------------|----------|\n")
                
                for op_name, stats in sorted(op_types.items(), key=lambda x: x[1]['total_time'], reverse=True):
                    avg_time = stats['total_time'] / stats['count']
                    shapes_str = ', '.join(list(stats['shapes'])[:3])  # 显示前3个形状
                    f.write(f"| {op_name} | {stats['count']} | {stats['total_time']:.2f} | {avg_time:.2f} | {shapes_str} |\n")
                
                # GPU内存使用分析
                f.write(f"\n### GPU内存使用分析\n")
                if 'start' in phase_data['gpu_utilization'] and 'end' in phase_data['gpu_utilization']:
                    start_info = phase_data['gpu_utilization']['start']
                    end_info = phase_data['gpu_utilization']['end']
                    
                    for gpu_key in ['gpu_0', 'gpu_1']:
                        if gpu_key in start_info and gpu_key in end_info:
                            start_mem = start_info[gpu_key]['memory_used_mb']
                            end_mem = end_info[gpu_key]['memory_used_mb']
                            mem_delta = end_mem - start_mem
                            f.write(f"- {gpu_key.upper()}: {start_mem}MB -> {end_mem}MB (Δ{mem_delta:+}MB)\n")
                
                # 性能热点
                f.write(f"\n### 性能热点 (Top 10)\n")
                sorted_ops = sorted(operators, key=lambda x: x['execution_time_ms'], reverse=True)[:10]
                f.write("| 排名 | 算子类型 | 执行时间(ms) | 输入形状 | 输出形状 |\n")
                f.write("|------|----------|-------------|----------|----------|\n")
                
                for i, op in enumerate(sorted_ops, 1):
                    input_shapes = ', '.join([str(s) for s in op['input_shapes'][:2]])
                    output_shapes = ', '.join([str(s) for s in op['output_shapes'][:2]])
                    f.write(f"| {i} | {op['name']} | {op['execution_time_ms']:.2f} | {input_shapes} | {output_shapes} |\n")
                
                f.write("\n")
            
            # 阶段对比
            f.write("## Prefill vs Decode 阶段对比\n\n")
            
            prefill_ops = len(self.phase_data['prefill']['operators'])
            decode_ops = len(self.phase_data['decode']['operators'])
            prefill_time = self.phase_data['prefill']['timings'].get('total_phase_time_ms', 0)
            decode_time = self.phase_data['decode']['timings'].get('total_phase_time_ms', 0)
            
            f.write("| 指标 | Prefill | Decode | 比值 |\n")
            f.write("|------|---------|--------|------|\n")
            f.write(f"| 算子调用次数 | {prefill_ops} | {decode_ops} | {prefill_ops/max(decode_ops,1):.2f} |\n")
            f.write(f"| 总执行时间(ms) | {prefill_time:.2f} | {decode_time:.2f} | {prefill_time/max(decode_time,1):.2f} |\n")
            f.write(f"| 平均每次调用时间(ms) | {prefill_time/max(prefill_ops,1):.2f} | {decode_time/max(decode_ops,1):.2f} | {(prefill_time/max(prefill_ops,1))/(decode_time/max(decode_ops,1)):.2f} |\n")
        
        logger.info(f"分析结果已保存到: {json_file} 和 {report_file}")
        return json_file, report_file

def main():
    """主函数"""
    logger.info("启动双GPU (0,1) 综合算子性能和shape分析")
    
    # 检查GPU可用性
    if not torch.cuda.is_available():
        logger.error("CUDA不可用")
        return
    
    gpu_count = torch.cuda.device_count()
    if gpu_count < 2:
        logger.error(f"需要至少2个GPU，但只发现{gpu_count}个")
        return
    
    logger.info(f"发现{gpu_count}个GPU，将使用GPU 0和1进行测试")
    
    # 创建分析器
    profiler = DualGPUOperatorProfiler()
    
    try:
        # 运行测试
        profiler.run_sglang_test()
        
        # 保存结果
        json_file, report_file = profiler.save_results()
        
        logger.info("双GPU算子性能分析完成！")
        logger.info(f"详细数据: {json_file}")
        logger.info(f"分析报告: {report_file}")
        
    except Exception as e:
        logger.error(f"分析过程中发生错误: {e}")
        import traceback
        logger.error(traceback.format_exc())

if __name__ == "__main__":
    main()
