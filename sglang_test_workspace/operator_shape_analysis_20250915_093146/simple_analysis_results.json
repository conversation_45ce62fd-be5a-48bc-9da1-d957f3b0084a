{"analysis_time": "2025-09-15 09:38:19", "model_config": {"_attn_implementation_autoset": false, "_name_or_path": "/data1/DeepSeek-V3-0324", "add_cross_attention": false, "architectures": ["DeepseekV3ForCausalLMNextN"], "attention_bias": false, "attention_dropout": 0.0, "auto_map": {"AutoConfig": "configuration_deepseek.DeepseekV3Config", "AutoModel": "modeling_deepseek.DeepseekV3Model", "AutoModelForCausalLM": "modeling_deepseek.DeepseekV3ForCausalLM"}, "aux_loss_alpha": 0.001, "bad_words_ids": null, "begin_suppress_tokens": null, "bos_token_id": 0, "chunk_size_feed_forward": 0, "cross_attention_hidden_size": null, "decoder_start_token_id": null, "diversity_penalty": 0.0, "do_sample": false, "early_stopping": false, "encoder_no_repeat_ngram_size": 0, "eos_token_id": 1, "ep_size": 1, "exponential_decay_length_penalty": null, "finetuning_task": null, "first_k_dense_replace": 3, "forced_bos_token_id": null, "forced_eos_token_id": null, "hidden_act": "silu", "hidden_size": 7168, "id2label": {"0": "LABEL_0", "1": "LABEL_1"}, "initializer_range": 0.02, "intermediate_size": 18432, "is_decoder": false, "is_encoder_decoder": false, "kv_lora_rank": 512, "label2id": {"LABEL_0": 0, "LABEL_1": 1}, "length_penalty": 1.0, "max_length": 20, "max_position_embeddings": 163840, "min_length": 0, "model_type": "deepseek_v3", "moe_intermediate_size": 2048, "moe_layer_freq": 1, "n_group": 8, "n_routed_experts": 256, "n_shared_experts": 1, "no_repeat_ngram_size": 0, "norm_topk_prob": true, "num_attention_heads": 128, "num_beam_groups": 1, "num_beams": 1, "num_experts_per_tok": 8, "num_hidden_layers": 1, "num_key_value_heads": 128, "num_nextn_predict_layers": 1, "num_return_sequences": 1, "output_attentions": false, "output_hidden_states": false, "output_scores": false, "pad_token_id": null, "prefix": null, "pretraining_tp": 1, "problem_type": null, "pruned_heads": {}, "q_lora_rank": 1536, "qk_nope_head_dim": 128, "qk_rope_head_dim": 64, "remove_invalid_values": false, "repetition_penalty": 1.0, "return_dict": true, "return_dict_in_generate": false, "rms_norm_eps": 1e-06, "rope_scaling": {"beta_fast": 32, "beta_slow": 1, "factor": 40, "mscale": 1.0, "mscale_all_dim": 1.0, "original_max_position_embeddings": 4096, "type": "yarn"}, "rope_theta": 10000, "routed_scaling_factor": 2.5, "scoring_func": "sigmoid", "sep_token_id": null, "seq_aux": true, "suppress_tokens": null, "task_specific_params": null, "temperature": 1.0, "tf_legacy_loss": false, "tie_encoder_decoder": false, "tie_word_embeddings": false, "tokenizer_class": null, "top_k": 50, "top_p": 1.0, "topk_group": 4, "topk_method": "noaux_tc", "torch_dtype": "bfloat16", "torchscript": false, "transformers_version": "4.48.3", "typical_p": 1.0, "use_bfloat16": false, "use_cache": true, "v_head_dim": 128, "vocab_size": 129280}, "reference_shapes": {"attn_wqa": {"B": 1, "M": 128, "N": 1536, "K": 7168}, "attn_wqb": {"B": 1, "M": 128, "N": 24576, "K": 1536}, "attn_wkv_a": {"B": 1, "M": 128, "N": 576, "K": 7168}, "attn_wkv_b": {"B": 1, "M": 128, "N": 32768, "K": 512}, "attn_qk": {"B": 16384, "M": 1, "N": 4097, "K": 192}, "attn_pv": {"B": 16384, "M": 1, "N": 128, "K": 4097}, "attn_o": {"B": 1, "M": 128, "N": 7168, "K": 16384}, "dense_up": {"B": 1, "M": 128, "N": 18432, "K": 7168}, "dense_gate": {"B": 1, "M": 128, "N": 18432, "K": 7168}, "dense_down": {"B": 1, "M": 128, "N": 7168, "K": 18432}, "moe_export_up": {"B": 1, "M": 128, "N": 2048, "K": 7168}, "moe_export_gate": {"B": 1, "M": 128, "N": 2048, "K": 7168}, "moe_export_down": {"B": 1, "M": 128, "N": 7168, "K": 2048}, "moe_gate": {"B": 1, "M": 128, "N": 256, "K": 7168}}, "theoretical_analysis": {"attn_wqa": {"reference": {"B": 1, "M": 128, "N": 1536, "K": 7168}, "match_analysis": ["K或N维度匹配hidden_size(7168)"]}, "attn_wqb": {"reference": {"B": 1, "M": 128, "N": 24576, "K": 1536}, "match_analysis": []}, "attn_wkv_a": {"reference": {"B": 1, "M": 128, "N": 576, "K": 7168}, "match_analysis": ["K或N维度匹配hidden_size(7168)"]}, "attn_wkv_b": {"reference": {"B": 1, "M": 128, "N": 32768, "K": 512}, "match_analysis": []}, "attn_qk": {"reference": {"B": 16384, "M": 1, "N": 4097, "K": 192}, "match_analysis": []}, "attn_pv": {"reference": {"B": 16384, "M": 1, "N": 128, "K": 4097}, "match_analysis": []}, "attn_o": {"reference": {"B": 1, "M": 128, "N": 7168, "K": 16384}, "match_analysis": ["K或N维度匹配hidden_size(7168)"]}, "dense_up": {"reference": {"B": 1, "M": 128, "N": 18432, "K": 7168}, "match_analysis": ["K或N维度匹配hidden_size(7168)", "K或N维度匹配intermediate_size(18432)"]}, "dense_gate": {"reference": {"B": 1, "M": 128, "N": 18432, "K": 7168}, "match_analysis": ["K或N维度匹配hidden_size(7168)", "K或N维度匹配intermediate_size(18432)"]}, "dense_down": {"reference": {"B": 1, "M": 128, "N": 7168, "K": 18432}, "match_analysis": ["K或N维度匹配hidden_size(7168)", "K或N维度匹配intermediate_size(18432)"]}, "moe_export_up": {"reference": {"B": 1, "M": 128, "N": 2048, "K": 7168}, "match_analysis": ["K或N维度匹配hidden_size(7168)"]}, "moe_export_gate": {"reference": {"B": 1, "M": 128, "N": 2048, "K": 7168}, "match_analysis": ["K或N维度匹配hidden_size(7168)"]}, "moe_export_down": {"reference": {"B": 1, "M": 128, "N": 7168, "K": 2048}, "match_analysis": ["K或N维度匹配hidden_size(7168)"]}, "moe_gate": {"reference": {"B": 1, "M": 128, "N": 256, "K": 7168}, "match_analysis": ["K或N维度匹配hidden_size(7168)"]}}, "summary": {"total_reference_operators": 14, "config_matches": 10, "key_findings": ["模型hidden_size为7168", "模型intermediate_size为18432", "共14个参考算子", "其中10个与配置参数匹配"]}}