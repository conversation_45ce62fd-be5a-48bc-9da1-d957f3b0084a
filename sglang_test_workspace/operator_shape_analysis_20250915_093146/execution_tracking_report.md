# SGLang执行追踪分析报告

## 分析时间: 2025-09-15 09:40:20

## 执行摘要
- 总操作数: 9
- 总耗时: 39.092秒
- import阶段操作: 1
- engine_creation阶段操作: 3
- inference阶段操作: 2
- cleanup阶段操作: 1

## 显存使用分析
- gpu_0峰值显存: 0.0MB
- gpu_1峰值显存: 0.0MB
- gpu_2峰值显存: 0.0MB
- gpu_3峰值显存: 0.0MB
- gpu_4峰值显存: 0.0MB
- gpu_5峰值显存: 0.0MB
- gpu_6峰值显存: 0.0MB
- gpu_7峰值显存: 0.0MB
- 追踪阶段: initial, after_import, after_engine_creation, after_inference, after_cleanup

## 性能分析
- total_time: 39.031秒
- inference_time: 6.997秒
- operations_count: 6

## 操作时间线
| 时间(s) | 操作 | 详情 |
|---------|------|------|
| 1.442 | start_analysis |  |
| 1.442 | importing_sglang |  |
| 1.791 | creating_engine |  |
| 32.019 | engine_created |  |
| 32.023 | starting_inference |  |
| 39.020 | inference_completed | {'inference_time': 6.997097492218018, 'prompt_leng... |
| 39.031 | cleanup_start |  |
| 39.090 | engine_shutdown |  |
| 39.092 | analysis_complete |  |
