{"schemaVersion": 1, "deviceProperties": [{"id": 0, "name": "NVIDIA A100-SXM4-40GB", "totalGlobalMem": 42406903808, "computeMajor": 8, "computeMinor": 0, "maxThreadsPerBlock": 1024, "maxThreadsPerMultiprocessor": 2048, "regsPerBlock": 65536, "warpSize": 32, "sharedMemPerBlock": 49152, "numSms": 108, "regsPerMultiprocessor": 65536, "sharedMemPerBlockOptin": 166912, "sharedMemPerMultiprocessor": 167936}, {"id": 1, "name": "NVIDIA A100-SXM4-40GB", "totalGlobalMem": 42406903808, "computeMajor": 8, "computeMinor": 0, "maxThreadsPerBlock": 1024, "maxThreadsPerMultiprocessor": 2048, "regsPerBlock": 65536, "warpSize": 32, "sharedMemPerBlock": 49152, "numSms": 108, "regsPerMultiprocessor": 65536, "sharedMemPerBlockOptin": 166912, "sharedMemPerMultiprocessor": 167936}, {"id": 2, "name": "NVIDIA A100-SXM4-40GB", "totalGlobalMem": 42406903808, "computeMajor": 8, "computeMinor": 0, "maxThreadsPerBlock": 1024, "maxThreadsPerMultiprocessor": 2048, "regsPerBlock": 65536, "warpSize": 32, "sharedMemPerBlock": 49152, "numSms": 108, "regsPerMultiprocessor": 65536, "sharedMemPerBlockOptin": 166912, "sharedMemPerMultiprocessor": 167936}, {"id": 3, "name": "NVIDIA A100-SXM4-40GB", "totalGlobalMem": 42406903808, "computeMajor": 8, "computeMinor": 0, "maxThreadsPerBlock": 1024, "maxThreadsPerMultiprocessor": 2048, "regsPerBlock": 65536, "warpSize": 32, "sharedMemPerBlock": 49152, "numSms": 108, "regsPerMultiprocessor": 65536, "sharedMemPerBlockOptin": 166912, "sharedMemPerMultiprocessor": 167936}, {"id": 4, "name": "NVIDIA A100-SXM4-40GB", "totalGlobalMem": 42406903808, "computeMajor": 8, "computeMinor": 0, "maxThreadsPerBlock": 1024, "maxThreadsPerMultiprocessor": 2048, "regsPerBlock": 65536, "warpSize": 32, "sharedMemPerBlock": 49152, "numSms": 108, "regsPerMultiprocessor": 65536, "sharedMemPerBlockOptin": 166912, "sharedMemPerMultiprocessor": 167936}, {"id": 5, "name": "NVIDIA A100-SXM4-40GB", "totalGlobalMem": 42406903808, "computeMajor": 8, "computeMinor": 0, "maxThreadsPerBlock": 1024, "maxThreadsPerMultiprocessor": 2048, "regsPerBlock": 65536, "warpSize": 32, "sharedMemPerBlock": 49152, "numSms": 108, "regsPerMultiprocessor": 65536, "sharedMemPerBlockOptin": 166912, "sharedMemPerMultiprocessor": 167936}, {"id": 6, "name": "NVIDIA A100-SXM4-40GB", "totalGlobalMem": 42406903808, "computeMajor": 8, "computeMinor": 0, "maxThreadsPerBlock": 1024, "maxThreadsPerMultiprocessor": 2048, "regsPerBlock": 65536, "warpSize": 32, "sharedMemPerBlock": 49152, "numSms": 108, "regsPerMultiprocessor": 65536, "sharedMemPerBlockOptin": 166912, "sharedMemPerMultiprocessor": 167936}, {"id": 7, "name": "NVIDIA A100-SXM4-40GB", "totalGlobalMem": 42406903808, "computeMajor": 8, "computeMinor": 0, "maxThreadsPerBlock": 1024, "maxThreadsPerMultiprocessor": 2048, "regsPerBlock": 65536, "warpSize": 32, "sharedMemPerBlock": 49152, "numSms": 108, "regsPerMultiprocessor": 65536, "sharedMemPerBlockOptin": 166912, "sharedMemPerMultiprocessor": 167936}], "cupti_version": 26, "cuda_runtime_version": 12080, "cuda_driver_version": 12090, "record_shapes": 1, "trace_id": "E00F97FE0A4540589CD33C085424FA46", "displayTimeUnit": "ms", "baseTimeNanoseconds": 1751410836000000000, "traceEvents": [{"ph": "X", "cat": "cuda_runtime", "name": "cudaDeviceSynchronize", "pid": 473961, "tid": 473961, "ts": 6516882829969.737, "dur": 15.777, "args": {"cbid": 165, "correlation": 51}}, {"ph": "s", "id": 51, "pid": 473961, "tid": 473961, "ts": 6516882829969.737, "cat": "ac2g", "name": "ac2g"}, {"ph": "X", "cat": "overhead", "name": "Activity Buffer Request", "pid": -1, "tid": 0, "ts": 6516882829986.983, "dur": 386.83}, {"ph": "X", "cat": "cuda_runtime", "name": "cudaDeviceSynchronize", "pid": 473961, "tid": 473961, "ts": 6516882830488.103, "dur": 10.126, "args": {"cbid": 165, "correlation": 55}}, {"ph": "s", "id": 55, "pid": 473961, "tid": 473961, "ts": 6516882830488.103, "cat": "ac2g", "name": "ac2g"}, {"name": "process_name", "ph": "M", "ts": 6516878341158.842, "pid": 473961, "tid": 0, "args": {"name": "python"}}, {"name": "process_labels", "ph": "M", "ts": 6516878341158.842, "pid": 473961, "tid": 0, "args": {"labels": "CPU"}}, {"name": "process_sort_index", "ph": "M", "ts": 6516878341158.842, "pid": 473961, "tid": 0, "args": {"sort_index": 473961}}, {"name": "process_name", "ph": "M", "ts": 6516878341158.842, "pid": 0, "tid": 0, "args": {"name": "python"}}, {"name": "process_labels", "ph": "M", "ts": 6516878341158.842, "pid": 0, "tid": 0, "args": {"labels": "GPU 0"}}, {"name": "process_sort_index", "ph": "M", "ts": 6516878341158.842, "pid": 0, "tid": 0, "args": {"sort_index": 5000000}}, {"name": "process_name", "ph": "M", "ts": 6516878341158.842, "pid": 1, "tid": 0, "args": {"name": "python"}}, {"name": "process_labels", "ph": "M", "ts": 6516878341158.842, "pid": 1, "tid": 0, "args": {"labels": "GPU 1"}}, {"name": "process_sort_index", "ph": "M", "ts": 6516878341158.842, "pid": 1, "tid": 0, "args": {"sort_index": 5000001}}, {"name": "process_name", "ph": "M", "ts": 6516878341158.842, "pid": 2, "tid": 0, "args": {"name": "python"}}, {"name": "process_labels", "ph": "M", "ts": 6516878341158.842, "pid": 2, "tid": 0, "args": {"labels": "GPU 2"}}, {"name": "process_sort_index", "ph": "M", "ts": 6516878341158.842, "pid": 2, "tid": 0, "args": {"sort_index": 5000002}}, {"name": "process_name", "ph": "M", "ts": 6516878341158.842, "pid": 3, "tid": 0, "args": {"name": "python"}}, {"name": "process_labels", "ph": "M", "ts": 6516878341158.842, "pid": 3, "tid": 0, "args": {"labels": "GPU 3"}}, {"name": "process_sort_index", "ph": "M", "ts": 6516878341158.842, "pid": 3, "tid": 0, "args": {"sort_index": 5000003}}, {"name": "process_name", "ph": "M", "ts": 6516878341158.842, "pid": 4, "tid": 0, "args": {"name": "python"}}, {"name": "process_labels", "ph": "M", "ts": 6516878341158.842, "pid": 4, "tid": 0, "args": {"labels": "GPU 4"}}, {"name": "process_sort_index", "ph": "M", "ts": 6516878341158.842, "pid": 4, "tid": 0, "args": {"sort_index": 5000004}}, {"name": "process_name", "ph": "M", "ts": 6516878341158.842, "pid": 5, "tid": 0, "args": {"name": "python"}}, {"name": "process_labels", "ph": "M", "ts": 6516878341158.842, "pid": 5, "tid": 0, "args": {"labels": "GPU 5"}}, {"name": "process_sort_index", "ph": "M", "ts": 6516878341158.842, "pid": 5, "tid": 0, "args": {"sort_index": 5000005}}, {"name": "process_name", "ph": "M", "ts": 6516878341158.842, "pid": 6, "tid": 0, "args": {"name": "python"}}, {"name": "process_labels", "ph": "M", "ts": 6516878341158.842, "pid": 6, "tid": 0, "args": {"labels": "GPU 6"}}, {"name": "process_sort_index", "ph": "M", "ts": 6516878341158.842, "pid": 6, "tid": 0, "args": {"sort_index": 5000006}}, {"name": "process_name", "ph": "M", "ts": 6516878341158.842, "pid": 7, "tid": 0, "args": {"name": "python"}}, {"name": "process_labels", "ph": "M", "ts": 6516878341158.842, "pid": 7, "tid": 0, "args": {"labels": "GPU 7"}}, {"name": "process_sort_index", "ph": "M", "ts": 6516878341158.842, "pid": 7, "tid": 0, "args": {"sort_index": 5000007}}, {"name": "process_name", "ph": "M", "ts": 6516878341158.842, "pid": 8, "tid": 0, "args": {"name": "python"}}, {"name": "process_labels", "ph": "M", "ts": 6516878341158.842, "pid": 8, "tid": 0, "args": {"labels": "GPU 8"}}, {"name": "process_sort_index", "ph": "M", "ts": 6516878341158.842, "pid": 8, "tid": 0, "args": {"sort_index": 5000008}}, {"name": "process_name", "ph": "M", "ts": 6516878341158.842, "pid": 9, "tid": 0, "args": {"name": "python"}}, {"name": "process_labels", "ph": "M", "ts": 6516878341158.842, "pid": 9, "tid": 0, "args": {"labels": "GPU 9"}}, {"name": "process_sort_index", "ph": "M", "ts": 6516878341158.842, "pid": 9, "tid": 0, "args": {"sort_index": 5000009}}, {"name": "process_name", "ph": "M", "ts": 6516878341158.842, "pid": 10, "tid": 0, "args": {"name": "python"}}, {"name": "process_labels", "ph": "M", "ts": 6516878341158.842, "pid": 10, "tid": 0, "args": {"labels": "GPU 10"}}, {"name": "process_sort_index", "ph": "M", "ts": 6516878341158.842, "pid": 10, "tid": 0, "args": {"sort_index": 5000010}}, {"name": "process_name", "ph": "M", "ts": 6516878341158.842, "pid": 11, "tid": 0, "args": {"name": "python"}}, {"name": "process_labels", "ph": "M", "ts": 6516878341158.842, "pid": 11, "tid": 0, "args": {"labels": "GPU 11"}}, {"name": "process_sort_index", "ph": "M", "ts": 6516878341158.842, "pid": 11, "tid": 0, "args": {"sort_index": 5000011}}, {"name": "process_name", "ph": "M", "ts": 6516878341158.842, "pid": 12, "tid": 0, "args": {"name": "python"}}, {"name": "process_labels", "ph": "M", "ts": 6516878341158.842, "pid": 12, "tid": 0, "args": {"labels": "GPU 12"}}, {"name": "process_sort_index", "ph": "M", "ts": 6516878341158.842, "pid": 12, "tid": 0, "args": {"sort_index": 5000012}}, {"name": "process_name", "ph": "M", "ts": 6516878341158.842, "pid": 13, "tid": 0, "args": {"name": "python"}}, {"name": "process_labels", "ph": "M", "ts": 6516878341158.842, "pid": 13, "tid": 0, "args": {"labels": "GPU 13"}}, {"name": "process_sort_index", "ph": "M", "ts": 6516878341158.842, "pid": 13, "tid": 0, "args": {"sort_index": 5000013}}, {"name": "process_name", "ph": "M", "ts": 6516878341158.842, "pid": 14, "tid": 0, "args": {"name": "python"}}, {"name": "process_labels", "ph": "M", "ts": 6516878341158.842, "pid": 14, "tid": 0, "args": {"labels": "GPU 14"}}, {"name": "process_sort_index", "ph": "M", "ts": 6516878341158.842, "pid": 14, "tid": 0, "args": {"sort_index": 5000014}}, {"name": "process_name", "ph": "M", "ts": 6516878341158.842, "pid": 15, "tid": 0, "args": {"name": "python"}}, {"name": "process_labels", "ph": "M", "ts": 6516878341158.842, "pid": 15, "tid": 0, "args": {"labels": "GPU 15"}}, {"name": "process_sort_index", "ph": "M", "ts": 6516878341158.842, "pid": 15, "tid": 0, "args": {"sort_index": 5000015}}, {"name": "thread_name", "ph": "M", "ts": 6516878341158.842, "pid": 473961, "tid": 473961, "args": {"name": "thread 473961 (python)"}}, {"name": "thread_sort_index", "ph": "M", "ts": 6516878341158.842, "pid": 473961, "tid": 473961, "args": {"sort_index": 473961}}, {"name": "thread_name", "ph": "M", "ts": 6516878341158.842, "pid": 473961, "tid": 473961, "args": {"name": "thread 473961 (python)"}}, {"name": "thread_sort_index", "ph": "M", "ts": 6516878341158.842, "pid": 473961, "tid": 473961, "args": {"sort_index": 473961}}, {"ph": "X", "cat": "Trace", "ts": 6516878341003.076, "dur": 4489510.44, "pid": "Spans", "tid": "PyTorch Profiler", "name": "PyTorch Profiler (0)", "args": {"Op count": 0}}, {"name": "process_sort_index", "ph": "M", "ts": 6516878341003.076, "pid": "Spans", "tid": 0, "args": {"sort_index": 536870912}}, {"name": "Iteration Start: PyTorch Profiler", "ph": "i", "s": "g", "pid": "Traces", "tid": "Trace PyTorch Profiler", "ts": 6516878341003.076}, {"name": "Record Window End", "ph": "i", "s": "g", "pid": "", "tid": "", "ts": 6516882830666.877}], "traceName": "/workspace/sglang_test_workspace/sglang_debug_task_20250915_071412/trace_infer.json"}