#!/usr/bin/env python3
import os
import sys
import time
import json
import signal
import subprocess

import requests

MODEL_PATH = "/home/<USER>/deepseek-int8"
HOST = os.environ.get("SGL_HOST", "127.0.0.1")
PORT = int(os.environ.get("SGL_PORT", "30000"))
URL = f"http://{HOST}:{PORT}/v1/chat/completions"

LAUNCH_CMD = [
    sys.executable, "-m", "sglang.launch_server",
    "--model", MODEL_PATH,
    "--tp", "4",
    "--dist-init-addr", "127.0.0.1:8001",
    "--trust-remote",
    "--quantization", "w8a8_int8",
    "--disable-cuda-graph",
    "--skip-server-warmup",
    "--log-level", "info",
]

ENV = os.environ.copy()
ENV.update({
    "SGL_DISABLE_TP_MEMORY_INBALANCE_CHECK": "1",
    "UCX_VFS_ENABLE": "n",
    "NCCL_P2P_DISABLE": "1",
    "NCCL_IB_DISABLE": "1",
    "SGLANG_IS_FLASHINFER_AVAILABLE": "false",
})


def wait_ready(timeout_s=180, interval_s=2):
    deadline = time.time() + timeout_s
    payload = {
        "model": MODEL_PATH,
        "messages": [{"role": "user", "content": "ping"}],
        "max_tokens": 1,
        "temperature": 0.0,
        "stream": False,
    }
    headers = {"Content-Type": "application/json"}
    last_err = None
    while time.time() < deadline:
        try:
            r = requests.post(URL, headers=headers, data=json.dumps(payload), timeout=5)
            if r.status_code == 200:
                return True
            last_err = f"HTTP {r.status_code}: {r.text[:200]}"
        except Exception as e:
            last_err = str(e)
        time.sleep(interval_s)
    print(f"Server not ready in {timeout_s}s. Last error: {last_err}")
    return False


def main():
    print("Launching sglang server ...")
    proc = subprocess.Popen(LAUNCH_CMD, env=ENV)
    try:
        if not wait_ready():
            proc.terminate()
            try:
                proc.wait(timeout=10)
            except subprocess.TimeoutExpired:
                proc.kill()
            sys.exit(1)

        print("Server ready. Sending generate request ...")
        headers = {"Content-Type": "application/json"}
        body = {
            "model": MODEL_PATH,
            "messages": [{"role": "user", "content": "用一句话介绍你自己。"}],
            "max_tokens": 32,
            "temperature": 0.7,
            "stream": False,
        }
        r = requests.post(URL, headers=headers, data=json.dumps(body), timeout=120)
        r.raise_for_status()
        data = r.json()
        text = data["choices"][0]["message"]["content"]
        usage = data.get("usage", {})
        print("Generate OK\n---\n", text)
        print("---\nUsage:", usage)
        sys.exit(0)
    finally:
        # Gracefully stop server
        try:
            os.kill(proc.pid, signal.SIGTERM)
        except Exception:
            pass
        try:
            proc.wait(timeout=10)
        except Exception:
            pass


if __name__ == "__main__":
    main()
