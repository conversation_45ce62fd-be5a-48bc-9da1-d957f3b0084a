#!/usr/bin/env python3
import os
import sys
import traceback

# 必须在导入 sglang 前设置分布式/通信相关环境
os.environ.setdefault("SGL_DISABLE_TP_MEMORY_INBALANCE_CHECK", "1")
os.environ.setdefault("UCX_VFS_ENABLE", "n")
os.environ.setdefault("NCCL_P2P_DISABLE", "1")
os.environ.setdefault("NCCL_IB_DISABLE", "1")
os.environ.setdefault("SGLANG_IS_FLASHINFER_AVAILABLE", "false")

import sglang as sgl

MODEL_PATH = os.environ.get("MODEL_PATH", "/home/<USER>/deepseek-int8")


def main():
    # 直接离线引擎加载（不启动HTTP服务）
    llm = None
    try:
        print("[offline] init Engine ...")
        llm = sgl.Engine(
            model_path=MODEL_PATH,
            tp_size=1,
            quantization="w8a8_int8",
            trust_remote_code=True,
            dist_init_addr="127.0.0.1:8001",
            disable_cuda_graph=True,
            log_level="info",
        )

        prompt = "用一句话介绍你自己。"
        sampling_params = {"max_new_tokens": 32, "temperature": 0.7}
        print("[offline] generate ...")
        out = llm.generate(prompt=prompt, sampling_params=sampling_params)
        print("Generate OK\n---\n", out.get("text", out))
    except Exception:
        print("[offline] ERROR:\n" + traceback.format_exc())
        sys.exit(1)
    finally:
        if llm is not None:
            try:
                llm.shutdown()
            except Exception:
                pass


if __name__ == "__main__":
    main()
