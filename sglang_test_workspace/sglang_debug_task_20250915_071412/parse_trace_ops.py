#!/usr/bin/env python3
import os
import sys
import json
from collections import defaultdict

TRACE_PATH = sys.argv[1] if len(sys.argv) > 1 else "trace_infer.json"
OUT_TXT = sys.argv[2] if len(sys.argv) > 2 else "op_shapes_infer_from_trace.txt"
OUT_CSV = sys.argv[3] if len(sys.argv) > 3 else "op_shapes_infer_from_trace.csv"

KEYWORDS = (
    "sgl_kernel", "dsv3", "moe", "bmm", "gemm", "cutlass", "flashinfer", "triton",
    "aten::mm", "aten::bmm", "aten::addmm", "aten::matmul", "_scaled_dot_product_attention",
    "layernorm", "rmsnorm", "softmax", "embedding", "topk", "gather", "scatter", "index_select",
)


def load_trace(path):
    with open(path, "r") as f:
        # chrome trace may be large; use json to load
        data = json.load(f)
    # Both formats observed: {"traceEvents": [...]} or a list
    if isinstance(data, dict) and "traceEvents" in data:
        events = data["traceEvents"]
    elif isinstance(data, list):
        events = data
    else:
        raise ValueError("Unsupported trace format")
    return events


def extract_shapes(args_dict):
    # Try common keys
    for k in ("Input Dims", "Input Shapes", "input_shapes", "Shapes", "input_dims"):
        if isinstance(args_dict, dict) and k in args_dict:
            return args_dict[k]
    return None


def main():
    events = load_trace(TRACE_PATH)
    agg = defaultdict(lambda: {"count": 0, "dur": 0.0})

    for ev in events:
        if not isinstance(ev, dict):
            continue
        name = ev.get("name", "")
        ph = ev.get("ph", "")
        if not name or ph not in ("X", "f", "p", "i"):
            continue
        if not any(k in name for k in KEYWORDS):
            continue
        args = ev.get("args", {})
        shapes = extract_shapes(args)
        # canonicalize shapes to a compact string
        if isinstance(shapes, list):
            s = []
            for item in shapes:
                if isinstance(item, (list, tuple)):
                    s.append("[" + ",".join(str(x) for x in item) + "]")
                else:
                    s.append(str(item))
            shape_str = "|".join(s)
        else:
            shape_str = str(shapes) if shapes is not None else "-"
        key = (name, shape_str)
        agg[key]["count"] += 1
        # dur in microseconds in chrome trace? Unit varies; we'll keep as-is
        agg[key]["dur"] += float(ev.get("dur", 0.0))

    # sort by duration desc
    items = sorted(agg.items(), key=lambda kv: kv[1]["dur"], reverse=True)

    with open(OUT_TXT, "w", encoding="utf-8") as f:
        for (name, shape_str), stat in items:
            f.write(f"{name} | shapes={shape_str} | count={stat['count']} | dur={stat['dur']:.1f}\n")

    # write CSV
    import csv
    with open(OUT_CSV, "w", newline="", encoding="utf-8") as cf:
        w = csv.writer(cf)
        w.writerow(["name", "shapes", "count", "dur"])
        for (name, shape_str), stat in items:
            w.writerow([name, shape_str, stat["count"], f"{stat['dur']:.1f}"])

    print(f"Wrote {len(items)} rows to {OUT_TXT} and {OUT_CSV}")


if __name__ == "__main__":
    main()
