{"schemaVersion": 1, "deviceProperties": [{"id": 0, "name": "NVIDIA A100-SXM4-40GB", "totalGlobalMem": 42406903808, "computeMajor": 8, "computeMinor": 0, "maxThreadsPerBlock": 1024, "maxThreadsPerMultiprocessor": 2048, "regsPerBlock": 65536, "warpSize": 32, "sharedMemPerBlock": 49152, "numSms": 108, "regsPerMultiprocessor": 65536, "sharedMemPerBlockOptin": 166912, "sharedMemPerMultiprocessor": 167936}, {"id": 1, "name": "NVIDIA A100-SXM4-40GB", "totalGlobalMem": 42406903808, "computeMajor": 8, "computeMinor": 0, "maxThreadsPerBlock": 1024, "maxThreadsPerMultiprocessor": 2048, "regsPerBlock": 65536, "warpSize": 32, "sharedMemPerBlock": 49152, "numSms": 108, "regsPerMultiprocessor": 65536, "sharedMemPerBlockOptin": 166912, "sharedMemPerMultiprocessor": 167936}, {"id": 2, "name": "NVIDIA A100-SXM4-40GB", "totalGlobalMem": 42406903808, "computeMajor": 8, "computeMinor": 0, "maxThreadsPerBlock": 1024, "maxThreadsPerMultiprocessor": 2048, "regsPerBlock": 65536, "warpSize": 32, "sharedMemPerBlock": 49152, "numSms": 108, "regsPerMultiprocessor": 65536, "sharedMemPerBlockOptin": 166912, "sharedMemPerMultiprocessor": 167936}, {"id": 3, "name": "NVIDIA A100-SXM4-40GB", "totalGlobalMem": 42406903808, "computeMajor": 8, "computeMinor": 0, "maxThreadsPerBlock": 1024, "maxThreadsPerMultiprocessor": 2048, "regsPerBlock": 65536, "warpSize": 32, "sharedMemPerBlock": 49152, "numSms": 108, "regsPerMultiprocessor": 65536, "sharedMemPerBlockOptin": 166912, "sharedMemPerMultiprocessor": 167936}, {"id": 4, "name": "NVIDIA A100-SXM4-40GB", "totalGlobalMem": 42406903808, "computeMajor": 8, "computeMinor": 0, "maxThreadsPerBlock": 1024, "maxThreadsPerMultiprocessor": 2048, "regsPerBlock": 65536, "warpSize": 32, "sharedMemPerBlock": 49152, "numSms": 108, "regsPerMultiprocessor": 65536, "sharedMemPerBlockOptin": 166912, "sharedMemPerMultiprocessor": 167936}, {"id": 5, "name": "NVIDIA A100-SXM4-40GB", "totalGlobalMem": 42406903808, "computeMajor": 8, "computeMinor": 0, "maxThreadsPerBlock": 1024, "maxThreadsPerMultiprocessor": 2048, "regsPerBlock": 65536, "warpSize": 32, "sharedMemPerBlock": 49152, "numSms": 108, "regsPerMultiprocessor": 65536, "sharedMemPerBlockOptin": 166912, "sharedMemPerMultiprocessor": 167936}, {"id": 6, "name": "NVIDIA A100-SXM4-40GB", "totalGlobalMem": 42406903808, "computeMajor": 8, "computeMinor": 0, "maxThreadsPerBlock": 1024, "maxThreadsPerMultiprocessor": 2048, "regsPerBlock": 65536, "warpSize": 32, "sharedMemPerBlock": 49152, "numSms": 108, "regsPerMultiprocessor": 65536, "sharedMemPerBlockOptin": 166912, "sharedMemPerMultiprocessor": 167936}, {"id": 7, "name": "NVIDIA A100-SXM4-40GB", "totalGlobalMem": 42406903808, "computeMajor": 8, "computeMinor": 0, "maxThreadsPerBlock": 1024, "maxThreadsPerMultiprocessor": 2048, "regsPerBlock": 65536, "warpSize": 32, "sharedMemPerBlock": 49152, "numSms": 108, "regsPerMultiprocessor": 65536, "sharedMemPerBlockOptin": 166912, "sharedMemPerMultiprocessor": 167936}], "cupti_version": 26, "cuda_runtime_version": 12080, "cuda_driver_version": 12090, "record_shapes": 1, "trace_id": "1FDFA59772DA4AD9B27821295F1C4B6B", "displayTimeUnit": "ms", "baseTimeNanoseconds": 1751410836000000000, "traceEvents": [{"ph": "X", "cat": "cpu_op", "name": "torchvision::_cuda_version", "pid": 473961, "tid": 473961, "ts": 6516842423800.751, "dur": 1.612, "args": {"External id": 1, "Record function id": 0, "Ev Idx": 0}}, {"ph": "X", "cat": "cpu_op", "name": "torchvision::_cuda_version", "pid": 473961, "tid": 473961, "ts": 6516842514734.561, "dur": 2.79, "args": {"External id": 2, "Record function id": 0, "Ev Idx": 1}}, {"ph": "X", "cat": "cpu_op", "name": "torchvision::_cuda_version", "pid": 473961, "tid": 473961, "ts": 6516842514747.996, "dur": 0.117, "args": {"External id": 3, "Record function id": 0, "Ev Idx": 2}}, {"ph": "X", "cat": "user_annotation", "name": "inductor_codecache_torch_key (dynamo_timed)", "pid": 473961, "tid": 473961, "ts": 6516846111272.366, "dur": 111835.824, "args": {"External id": 4, "Record function id": 0, "Ev Idx": 3}}, {"ph": "X", "cat": "cpu_op", "name": "aten::empty", "pid": 473961, "tid": 473961, "ts": 6516846949249.781, "dur": 13.831, "args": {"External id": 5, "Record function id": 0, "Concrete Inputs": ["[8]", "6", "0", "", "False", ""], "Input type": ["ScalarList", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "", "<PERSON><PERSON><PERSON>", ""], "Input Strides": [[], [], [], [], [], []], "Input Dims": [[], [], [], [], [], []], "Ev Idx": 4}}, {"ph": "X", "cat": "cpu_op", "name": "aten::to", "pid": 473961, "tid": 473961, "ts": 6516846949289.996, "dur": 1.021, "args": {"External id": 6, "Record function id": 0, "Concrete Inputs": ["", "", "6", "False", "False", ""], "Input type": ["float", "", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", ""], "Input Strides": [[1], [], [], [], [], []], "Input Dims": [[8], [], [], [], [], []], "Ev Idx": 5}}, {"ph": "X", "cat": "cpu_op", "name": "aten::lift_fresh", "pid": 473961, "tid": 473961, "ts": 6516846949302.345, "dur": 0.596, "args": {"External id": 7, "Record function id": 0, "Concrete Inputs": [""], "Input type": ["float"], "Input Strides": [[1]], "Input Dims": [[8]], "Ev Idx": 6}}, {"ph": "X", "cat": "cpu_op", "name": "aten::detach_", "pid": 473961, "tid": 473961, "ts": 6516846949308.486, "dur": 6.305, "args": {"External id": 8, "Sequence number": 0, "Fwd thread id": 0, "Record function id": 0, "Concrete Inputs": [""], "Input type": ["float"], "Input Strides": [[1]], "Input Dims": [[8]], "Ev Idx": 7}}, {"ph": "X", "cat": "cpu_op", "name": "detach_", "pid": 473961, "tid": 473961, "ts": 6516846949311.677, "dur": 2.928, "args": {"External id": 9, "Record function id": 0, "Concrete Inputs": [""], "Input type": ["float"], "Input Strides": [[1]], "Input Dims": [[8]], "Ev Idx": 8}}, {"ph": "X", "cat": "cpu_op", "name": "aten::empty", "pid": 473961, "tid": 473961, "ts": 6516846950711.482, "dur": 6.71, "args": {"External id": 10, "Record function id": 0, "Concrete Inputs": ["[7]", "6", "0", "", "False", ""], "Input type": ["ScalarList", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "", "<PERSON><PERSON><PERSON>", ""], "Input Strides": [[], [], [], [], [], []], "Input Dims": [[], [], [], [], [], []], "Ev Idx": 9}}, {"ph": "X", "cat": "cpu_op", "name": "aten::to", "pid": 473961, "tid": 473961, "ts": 6516846950724.653, "dur": 0.483, "args": {"External id": 11, "Record function id": 0, "Concrete Inputs": ["", "", "6", "False", "False", ""], "Input type": ["float", "", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", ""], "Input Strides": [[1], [], [], [], [], []], "Input Dims": [[7], [], [], [], [], []], "Ev Idx": 10}}, {"ph": "X", "cat": "cpu_op", "name": "aten::lift_fresh", "pid": 473961, "tid": 473961, "ts": 6516846950727.232, "dur": 0.372, "args": {"External id": 12, "Record function id": 0, "Concrete Inputs": [""], "Input type": ["float"], "Input Strides": [[1]], "Input Dims": [[7]], "Ev Idx": 11}}, {"ph": "X", "cat": "cpu_op", "name": "aten::detach_", "pid": 473961, "tid": 473961, "ts": 6516846950729.071, "dur": 4.159, "args": {"External id": 13, "Sequence number": 0, "Fwd thread id": 0, "Record function id": 0, "Concrete Inputs": [""], "Input type": ["float"], "Input Strides": [[1]], "Input Dims": [[7]], "Ev Idx": 12}}, {"ph": "X", "cat": "cpu_op", "name": "detach_", "pid": 473961, "tid": 473961, "ts": 6516846950730.932, "dur": 2.139, "args": {"External id": 14, "Record function id": 0, "Concrete Inputs": [""], "Input type": ["float"], "Input Strides": [[1]], "Input Dims": [[7]], "Ev Idx": 13}}, {"ph": "X", "cat": "cuda_runtime", "name": "cudaGetDeviceCount", "pid": 473961, "tid": 473961, "ts": 6516844668039.199, "dur": 1.069, "args": {"cbid": 3, "correlation": 1}}, {"ph": "f", "id": 1, "pid": 473961, "tid": 473961, "ts": 6516844668039.199, "cat": "ac2g", "name": "ac2g", "bp": "e"}, {"ph": "X", "cat": "overhead", "name": "Activity Buffer Request", "pid": -1, "tid": 0, "ts": 6516844668042.965, "dur": 5743.035}, {"ph": "X", "cat": "cuda_runtime", "name": "cudaGetDeviceCount", "pid": 473961, "tid": 473961, "ts": 6516844673967.67, "dur": 0.31, "args": {"cbid": 3, "correlation": 3}}, {"ph": "f", "id": 3, "pid": 473961, "tid": 473961, "ts": 6516844673967.67, "cat": "ac2g", "name": "ac2g", "bp": "e"}, {"ph": "X", "cat": "cuda_runtime", "name": "cudaGetDeviceProperties_v2", "pid": 473961, "tid": 473961, "ts": 6516844674815.551, "dur": 3777.435, "args": {"cbid": 440, "correlation": 27}}, {"ph": "f", "id": 27, "pid": 473961, "tid": 473961, "ts": 6516844674815.551, "cat": "ac2g", "name": "ac2g", "bp": "e"}, {"ph": "X", "cat": "cuda_runtime", "name": "cudaGetDeviceProperties_v2", "pid": 473961, "tid": 473961, "ts": 6516844679057.45, "dur": 2895.672, "args": {"cbid": 440, "correlation": 28}}, {"ph": "f", "id": 28, "pid": 473961, "tid": 473961, "ts": 6516844679057.45, "cat": "ac2g", "name": "ac2g", "bp": "e"}, {"ph": "X", "cat": "cuda_runtime", "name": "cudaGetDeviceProperties_v2", "pid": 473961, "tid": 473961, "ts": 6516844682354.007, "dur": 3602.592, "args": {"cbid": 440, "correlation": 29}}, {"ph": "f", "id": 29, "pid": 473961, "tid": 473961, "ts": 6516844682354.007, "cat": "ac2g", "name": "ac2g", "bp": "e"}, {"ph": "X", "cat": "cuda_runtime", "name": "cudaGetDeviceProperties_v2", "pid": 473961, "tid": 473961, "ts": 6516844686297.918, "dur": 2557.953, "args": {"cbid": 440, "correlation": 30}}, {"ph": "f", "id": 30, "pid": 473961, "tid": 473961, "ts": 6516844686297.918, "cat": "ac2g", "name": "ac2g", "bp": "e"}, {"ph": "X", "cat": "cuda_runtime", "name": "cudaGetDeviceProperties_v2", "pid": 473961, "tid": 473961, "ts": 6516844689190.708, "dur": 2065.793, "args": {"cbid": 440, "correlation": 31}}, {"ph": "f", "id": 31, "pid": 473961, "tid": 473961, "ts": 6516844689190.708, "cat": "ac2g", "name": "ac2g", "bp": "e"}, {"ph": "X", "cat": "cuda_runtime", "name": "cudaGetDeviceProperties_v2", "pid": 473961, "tid": 473961, "ts": 6516844691566.779, "dur": 2286.103, "args": {"cbid": 440, "correlation": 32}}, {"ph": "f", "id": 32, "pid": 473961, "tid": 473961, "ts": 6516844691566.779, "cat": "ac2g", "name": "ac2g", "bp": "e"}, {"ph": "X", "cat": "cuda_runtime", "name": "cudaGetDeviceProperties_v2", "pid": 473961, "tid": 473961, "ts": 6516844694146.976, "dur": 2028.09, "args": {"cbid": 440, "correlation": 33}}, {"ph": "f", "id": 33, "pid": 473961, "tid": 473961, "ts": 6516844694146.976, "cat": "ac2g", "name": "ac2g", "bp": "e"}, {"ph": "X", "cat": "cuda_runtime", "name": "cudaGetDeviceProperties_v2", "pid": 473961, "tid": 473961, "ts": 6516844696288.982, "dur": 1402.687, "args": {"cbid": 440, "correlation": 34}}, {"ph": "f", "id": 34, "pid": 473961, "tid": 473961, "ts": 6516844696288.982, "cat": "ac2g", "name": "ac2g", "bp": "e"}, {"ph": "X", "cat": "overhead", "name": "Instrumentation", "pid": -1, "tid": 0, "ts": 6516875745465.55, "dur": 88.006}, {"ph": "X", "cat": "overhead", "name": "Instrumentation", "pid": -1, "tid": 0, "ts": 6516875745663.547, "dur": 36.93}, {"ph": "X", "cat": "overhead", "name": "Resource", "pid": -1, "tid": 0, "ts": 6516875745700.642, "dur": 11.336}, {"ph": "X", "cat": "overhead", "name": "Resource", "pid": -1, "tid": 0, "ts": 6516875745724.038, "dur": 558303.85}, {"ph": "X", "cat": "overhead", "name": "Resource", "pid": -1, "tid": 0, "ts": 6516876304030.724, "dur": 294095.675}, {"ph": "X", "cat": "overhead", "name": "Resource", "pid": -1, "tid": 0, "ts": 6516876598128.904, "dur": 816897.574}, {"ph": "X", "cat": "cuda_runtime", "name": "cudaDeviceSynchronize", "pid": 473961, "tid": 473961, "ts": 6516875307833.602, "dur": 2150507.225, "args": {"cbid": 165, "correlation": 43}}, {"ph": "s", "id": 43, "pid": 473961, "tid": 473961, "ts": 6516875307833.602, "cat": "ac2g", "name": "ac2g"}, {"ph": "X", "cat": "cuda_runtime", "name": "cudaDeviceSynchronize", "pid": 473961, "tid": 473961, "ts": 6516877458955.337, "dur": 10.308, "args": {"cbid": 165, "correlation": 47}}, {"ph": "s", "id": 47, "pid": 473961, "tid": 473961, "ts": 6516877458955.337, "cat": "ac2g", "name": "ac2g"}, {"name": "process_name", "ph": "M", "ts": 6516841210875.849, "pid": 473961, "tid": 0, "args": {"name": "python"}}, {"name": "process_labels", "ph": "M", "ts": 6516841210875.849, "pid": 473961, "tid": 0, "args": {"labels": "CPU"}}, {"name": "process_sort_index", "ph": "M", "ts": 6516841210875.849, "pid": 473961, "tid": 0, "args": {"sort_index": 473961}}, {"name": "process_name", "ph": "M", "ts": 6516841210875.849, "pid": 0, "tid": 0, "args": {"name": "python"}}, {"name": "process_labels", "ph": "M", "ts": 6516841210875.849, "pid": 0, "tid": 0, "args": {"labels": "GPU 0"}}, {"name": "process_sort_index", "ph": "M", "ts": 6516841210875.849, "pid": 0, "tid": 0, "args": {"sort_index": 5000000}}, {"name": "process_name", "ph": "M", "ts": 6516841210875.849, "pid": 1, "tid": 0, "args": {"name": "python"}}, {"name": "process_labels", "ph": "M", "ts": 6516841210875.849, "pid": 1, "tid": 0, "args": {"labels": "GPU 1"}}, {"name": "process_sort_index", "ph": "M", "ts": 6516841210875.849, "pid": 1, "tid": 0, "args": {"sort_index": 5000001}}, {"name": "process_name", "ph": "M", "ts": 6516841210875.849, "pid": 2, "tid": 0, "args": {"name": "python"}}, {"name": "process_labels", "ph": "M", "ts": 6516841210875.849, "pid": 2, "tid": 0, "args": {"labels": "GPU 2"}}, {"name": "process_sort_index", "ph": "M", "ts": 6516841210875.849, "pid": 2, "tid": 0, "args": {"sort_index": 5000002}}, {"name": "process_name", "ph": "M", "ts": 6516841210875.849, "pid": 3, "tid": 0, "args": {"name": "python"}}, {"name": "process_labels", "ph": "M", "ts": 6516841210875.849, "pid": 3, "tid": 0, "args": {"labels": "GPU 3"}}, {"name": "process_sort_index", "ph": "M", "ts": 6516841210875.849, "pid": 3, "tid": 0, "args": {"sort_index": 5000003}}, {"name": "process_name", "ph": "M", "ts": 6516841210875.849, "pid": 4, "tid": 0, "args": {"name": "python"}}, {"name": "process_labels", "ph": "M", "ts": 6516841210875.849, "pid": 4, "tid": 0, "args": {"labels": "GPU 4"}}, {"name": "process_sort_index", "ph": "M", "ts": 6516841210875.849, "pid": 4, "tid": 0, "args": {"sort_index": 5000004}}, {"name": "process_name", "ph": "M", "ts": 6516841210875.849, "pid": 5, "tid": 0, "args": {"name": "python"}}, {"name": "process_labels", "ph": "M", "ts": 6516841210875.849, "pid": 5, "tid": 0, "args": {"labels": "GPU 5"}}, {"name": "process_sort_index", "ph": "M", "ts": 6516841210875.849, "pid": 5, "tid": 0, "args": {"sort_index": 5000005}}, {"name": "process_name", "ph": "M", "ts": 6516841210875.849, "pid": 6, "tid": 0, "args": {"name": "python"}}, {"name": "process_labels", "ph": "M", "ts": 6516841210875.849, "pid": 6, "tid": 0, "args": {"labels": "GPU 6"}}, {"name": "process_sort_index", "ph": "M", "ts": 6516841210875.849, "pid": 6, "tid": 0, "args": {"sort_index": 5000006}}, {"name": "process_name", "ph": "M", "ts": 6516841210875.849, "pid": 7, "tid": 0, "args": {"name": "python"}}, {"name": "process_labels", "ph": "M", "ts": 6516841210875.849, "pid": 7, "tid": 0, "args": {"labels": "GPU 7"}}, {"name": "process_sort_index", "ph": "M", "ts": 6516841210875.849, "pid": 7, "tid": 0, "args": {"sort_index": 5000007}}, {"name": "process_name", "ph": "M", "ts": 6516841210875.849, "pid": 8, "tid": 0, "args": {"name": "python"}}, {"name": "process_labels", "ph": "M", "ts": 6516841210875.849, "pid": 8, "tid": 0, "args": {"labels": "GPU 8"}}, {"name": "process_sort_index", "ph": "M", "ts": 6516841210875.849, "pid": 8, "tid": 0, "args": {"sort_index": 5000008}}, {"name": "process_name", "ph": "M", "ts": 6516841210875.849, "pid": 9, "tid": 0, "args": {"name": "python"}}, {"name": "process_labels", "ph": "M", "ts": 6516841210875.849, "pid": 9, "tid": 0, "args": {"labels": "GPU 9"}}, {"name": "process_sort_index", "ph": "M", "ts": 6516841210875.849, "pid": 9, "tid": 0, "args": {"sort_index": 5000009}}, {"name": "process_name", "ph": "M", "ts": 6516841210875.849, "pid": 10, "tid": 0, "args": {"name": "python"}}, {"name": "process_labels", "ph": "M", "ts": 6516841210875.849, "pid": 10, "tid": 0, "args": {"labels": "GPU 10"}}, {"name": "process_sort_index", "ph": "M", "ts": 6516841210875.849, "pid": 10, "tid": 0, "args": {"sort_index": 5000010}}, {"name": "process_name", "ph": "M", "ts": 6516841210875.849, "pid": 11, "tid": 0, "args": {"name": "python"}}, {"name": "process_labels", "ph": "M", "ts": 6516841210875.849, "pid": 11, "tid": 0, "args": {"labels": "GPU 11"}}, {"name": "process_sort_index", "ph": "M", "ts": 6516841210875.849, "pid": 11, "tid": 0, "args": {"sort_index": 5000011}}, {"name": "process_name", "ph": "M", "ts": 6516841210875.849, "pid": 12, "tid": 0, "args": {"name": "python"}}, {"name": "process_labels", "ph": "M", "ts": 6516841210875.849, "pid": 12, "tid": 0, "args": {"labels": "GPU 12"}}, {"name": "process_sort_index", "ph": "M", "ts": 6516841210875.849, "pid": 12, "tid": 0, "args": {"sort_index": 5000012}}, {"name": "process_name", "ph": "M", "ts": 6516841210875.849, "pid": 13, "tid": 0, "args": {"name": "python"}}, {"name": "process_labels", "ph": "M", "ts": 6516841210875.849, "pid": 13, "tid": 0, "args": {"labels": "GPU 13"}}, {"name": "process_sort_index", "ph": "M", "ts": 6516841210875.849, "pid": 13, "tid": 0, "args": {"sort_index": 5000013}}, {"name": "process_name", "ph": "M", "ts": 6516841210875.849, "pid": 14, "tid": 0, "args": {"name": "python"}}, {"name": "process_labels", "ph": "M", "ts": 6516841210875.849, "pid": 14, "tid": 0, "args": {"labels": "GPU 14"}}, {"name": "process_sort_index", "ph": "M", "ts": 6516841210875.849, "pid": 14, "tid": 0, "args": {"sort_index": 5000014}}, {"name": "process_name", "ph": "M", "ts": 6516841210875.849, "pid": 15, "tid": 0, "args": {"name": "python"}}, {"name": "process_labels", "ph": "M", "ts": 6516841210875.849, "pid": 15, "tid": 0, "args": {"labels": "GPU 15"}}, {"name": "process_sort_index", "ph": "M", "ts": 6516841210875.849, "pid": 15, "tid": 0, "args": {"sort_index": 5000015}}, {"name": "thread_name", "ph": "M", "ts": 6516841210875.849, "pid": 473961, "tid": 473961, "args": {"name": "thread 473961 (python)"}}, {"name": "thread_sort_index", "ph": "M", "ts": 6516841210875.849, "pid": 473961, "tid": 473961, "args": {"sort_index": 473961}}, {"name": "thread_name", "ph": "M", "ts": 6516841210875.849, "pid": 473961, "tid": 473961, "args": {"name": "thread 473961 (python)"}}, {"name": "thread_sort_index", "ph": "M", "ts": 6516841210875.849, "pid": 473961, "tid": 473961, "args": {"sort_index": 473961}}, {"ph": "X", "cat": "Trace", "ts": 6516841210831.138, "dur": 36248243.154, "pid": "Spans", "tid": "PyTorch Profiler", "name": "PyTorch Profiler (0)", "args": {"Op count": 0}}, {"name": "process_sort_index", "ph": "M", "ts": 6516841210831.138, "pid": "Spans", "tid": 0, "args": {"sort_index": 536870912}}, {"name": "Iteration Start: PyTorch Profiler", "ph": "i", "s": "g", "pid": "Traces", "tid": "Trace PyTorch Profiler", "ts": 6516841210831.138}, {"name": "Record Window End", "ph": "i", "s": "g", "pid": "", "tid": "", "ts": 6516877459783.066}], "traceName": "/workspace/sglang_test_workspace/sglang_debug_task_20250915_071412/trace_engine_load.json"}