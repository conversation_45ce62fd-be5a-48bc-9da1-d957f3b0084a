#!/usr/bin/env python3
import os
import sys
import time
import json
import traceback
from collections import defaultdict

# 环境准备（与离线脚本一致，尽量减少外部通信依赖）
os.environ.setdefault("SGL_DISABLE_TP_MEMORY_INBALANCE_CHECK", "1")
os.environ.setdefault("UCX_VFS_ENABLE", "n")
os.environ.setdefault("NCCL_P2P_DISABLE", "1")
os.environ.setdefault("NCCL_IB_DISABLE", "1")
os.environ.setdefault("SGLANG_IS_FLASHINFER_AVAILABLE", "false")

MODEL_PATH = os.environ.get("MODEL_PATH", "/home/<USER>/deepseek-int8")
TP_SIZE = int(os.environ.get("TP_SIZE", "4"))
QUANT = os.environ.get("QUANT", "w8a8_int8")
DIST_ADDR = os.environ.get("DIST_ADDR", "127.0.0.1:8001")
MAX_NEW_TOKENS = int(os.environ.get("MAX_NEW_TOKENS", "16"))
PROMPT = os.environ.get("PROMPT", "用一句话介绍你自己。")
OUT_DIR = os.environ.get("OUT_DIR", os.path.dirname(__file__))

TRACE_LOAD_JSON = os.path.join(OUT_DIR, "trace_engine_load.json")
TRACE_INFER_JSON = os.path.join(OUT_DIR, "trace_infer.json")
SHAPES_LOAD_TXT = os.path.join(OUT_DIR, "op_shapes_engine_load.txt")
SHAPES_INFER_TXT = os.path.join(OUT_DIR, "op_shapes_infer.txt")
SHAPES_INFER_CSV = os.path.join(OUT_DIR, "op_shapes_infer.csv")


def _summarize_key_averages(prof, txt_path, csv_path=None):
    # 基于 key_averages(record_shapes=True) 输出唯一 (op_name, input_shapes) 组合
    try:
        ka = prof.key_averages(group_by_input_shape=True)
    except Exception:
        ka = prof.key_averages()

    # 聚合 (name, input_shapes) -> count, cpu/cuda 时间
    rows = []
    for ev in ka:
        name = getattr(ev, "key", getattr(ev, "name", ""))
        input_shapes = getattr(ev, "input_shapes", None)
        if input_shapes is None:
            # 某些版本字段名不同
            input_shapes = getattr(ev, "shapes", None)
        # 仅保留计算相关常见算子，减少噪音
        keep = False
        for kw in (
            "mm", "bmm", "addmm", "linear", "matmul", "sdpa", "attention",
            "sgl_kernel", "triton", "ops::", "rmsnorm", "layernorm", "softmax",
            "topk", "index_select", "scatter", "gather", "quant", "dequant",
            "rope", "rotary", "embedding", "cat", "concat", "permute",
        ):
            if kw in name:
                keep = True
                break
        if not keep:
            continue
        row = {
            "name": name,
            "input_shapes": str(input_shapes),
            "count": getattr(ev, "count", 1),
            "cpu_time_total_us": getattr(ev, "cpu_time_total", 0.0) / 1000.0,
            "cuda_time_total_us": getattr(ev, "cuda_time_total", 0.0) / 1000.0,
        }
        rows.append(row)

    # 写入文本
    with open(txt_path, "w", encoding="utf-8") as f:
        for r in rows:
            f.write(f"{r['name']} | shapes={r['input_shapes']} | count={r['count']} | cpu_us={r['cpu_time_total_us']:.1f} | cuda_us={r['cuda_time_total_us']:.1f}\n")

    # 可选写入 CSV
    if csv_path:
        try:
            import csv
            with open(csv_path, "w", newline="", encoding="utf-8") as cf:
                w = csv.DictWriter(cf, fieldnames=list(rows[0].keys())) if rows else None
                if w:
                    w.writeheader()
                    for r in rows:
                        w.writerow(r)
        except Exception:
            pass


def main():
    import torch
    from torch.profiler import profile, ProfilerActivity
    import sglang as sgl

    llm = None
    try:
        # 1) 仅模型加载阶段
        with profile(
            activities=[ProfilerActivity.CPU, ProfilerActivity.CUDA],
            record_shapes=True,
            with_stack=False,
            profile_memory=False,
            with_flops=False,
        ) as prof_load:
            t0 = time.time()
            llm = sgl.Engine(
                model_path=MODEL_PATH,
                tp_size=TP_SIZE,
                quantization=QUANT,
                trust_remote_code=True,
                dist_init_addr=DIST_ADDR,
                disable_cuda_graph=True,
                log_level="info",
            )
            torch.cuda.synchronize() if torch.cuda.is_available() else None
            t1 = time.time()
            print(f"[profile] Engine init OK, elapsed={t1 - t0:.2f}s")
        # 导出加载阶段 trace
        prof_load.export_chrome_trace(TRACE_LOAD_JSON)
        _summarize_key_averages(prof_load, SHAPES_LOAD_TXT)

        # 2) 推理阶段
        sampling_params = {"max_new_tokens": MAX_NEW_TOKENS, "temperature": 0.7}
        with profile(
            activities=[ProfilerActivity.CPU, ProfilerActivity.CUDA],
            record_shapes=True,
            with_stack=False,
            profile_memory=False,
            with_flops=False,
        ) as prof_infer:
            out = llm.generate(prompt=PROMPT, sampling_params=sampling_params)
            torch.cuda.synchronize() if torch.cuda.is_available() else None
            print("[profile] Generate OK; text=", out.get("text", out)[:120])
        prof_infer.export_chrome_trace(TRACE_INFER_JSON)
        _summarize_key_averages(prof_infer, SHAPES_INFER_TXT, SHAPES_INFER_CSV)

        print("[profile] Files saved:")
        print(" - ", TRACE_LOAD_JSON)
        print(" - ", SHAPES_LOAD_TXT)
        print(" - ", TRACE_INFER_JSON)
        print(" - ", SHAPES_INFER_TXT)
        print(" - ", SHAPES_INFER_CSV)

    except Exception:
        print("[profile] ERROR:\n" + traceback.format_exc())
        sys.exit(1)
    finally:
        if llm is not None:
            try:
                llm.shutdown()
            except Exception:
                pass


if __name__ == "__main__":
    main()
