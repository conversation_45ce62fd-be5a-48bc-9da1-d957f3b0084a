#!/usr/bin/env python3
import os
import sys
import time
import json
import signal
import subprocess

import requests

# Config
MODEL_PATH = os.environ.get("VLLM_MODEL", "/home/<USER>/deepseek-int8")
HOST = os.environ.get("VLLM_HOST", "127.0.0.1")
PORT = int(os.environ.get("VLLM_PORT", "30010"))  # 避免与 sglang 默认 30000 冲突
URL = f"http://{HOST}:{PORT}/v1/chat/completions"
TP = os.environ.get("VLLM_TP", "4")

LAUNCH_CMD = [
    sys.executable, "-m", "vllm.entrypoints.openai.api_server",
    "--model", MODEL_PATH,
    "--host", HOST,
    "--port", str(PORT),
    "--tensor-parallel-size", TP,
    "--dtype", "auto",
    "--trust-remote-code",
    "--enforce-eager",               # 关闭 CUDA Graph，加快启动、提升兼容
    "--kv-cache-dtype", "auto",
]

ENV = os.environ.copy()
ENV.update({
    # 避免 NCCL/IB 带来的环境差异
    "NCCL_P2P_DISABLE": "1",
    "NCCL_IB_DISABLE": "1",
    # 若遇到 UCX 相关告警，可按需关闭 VFS
    "UCX_VFS_ENABLE": "n",
})


def wait_ready(timeout_s=240, interval_s=2):
    deadline = time.time() + timeout_s
    payload = {
        "model": MODEL_PATH,
        "messages": [{"role": "user", "content": "ping"}],
        "max_tokens": 1,
        "temperature": 0.0,
        "stream": False,
    }
    headers = {"Content-Type": "application/json"}
    last_err = None
    while time.time() < deadline:
        try:
            r = requests.post(URL, headers=headers, data=json.dumps(payload), timeout=8)
            if r.status_code == 200:
                return True
            last_err = f"HTTP {r.status_code}: {r.text[:200]}"
        except Exception as e:
            last_err = str(e)
        time.sleep(interval_s)
    print(f"[vLLM] Server not ready in {timeout_s}s. Last error: {last_err}")
    return False


def main():
    print("Launching vLLM OpenAI server ...")
    proc = subprocess.Popen(LAUNCH_CMD, env=ENV)
    try:
        if not wait_ready():
            proc.terminate()
            try:
                proc.wait(timeout=10)
            except subprocess.TimeoutExpired:
                proc.kill()
            sys.exit(1)

        print("vLLM server ready. Sending generate request ...")
        headers = {"Content-Type": "application/json"}
        body = {
            "model": MODEL_PATH,
            "messages": [{"role": "user", "content": "用一句话介绍你自己。"}],
            "max_tokens": 32,
            "temperature": 0.7,
            "stream": False,
        }
        r = requests.post(URL, headers=headers, data=json.dumps(body), timeout=180)
        r.raise_for_status()
        data = r.json()
        text = data["choices"][0]["message"]["content"]
        usage = data.get("usage", {})
        print("Generate OK (vLLM)\n---\n", text)
        print("---\nUsage:", usage)
        sys.exit(0)
    finally:
        # Gracefully stop server
        try:
            os.kill(proc.pid, signal.SIGTERM)
        except Exception:
            pass
        try:
            proc.wait(timeout=10)
        except Exception:
            pass


if __name__ == "__main__":
    main()
