算子名称
rms_norm
Linear
ROPE
softmax
add
silu
sigmoid
topK
scatter
gather
bincount
where

训练和推理GEMM分析																	
                                                                    
data_type	fp8																
batch_size	128																
seq_len	1																
kv_cache_len	4096						128										
TP	1														访存量/（计算量/算力）		
                                                            算力X放存量/计算量		
hidden_size	7168																
intermediate_size	18432																
q_lora_rank	1536																
kv_lora_rank	512				576												
moe_intermediate_size	2048																
num_heads	128																
qk_nope_head_dim	128														算力	8	
qk_rope_head_dim	64																
v_head_dim	128																
n_routed_experts	256																
                                                                    
native方案：训练+推理									absorb方案：推理								
    shape									shape							
layer	B	M	N	K	计算量(GFlops)	计算访存比	备注		layer	B	M	N	K	计算量(GFlops)	计算访存比		备注
attn_wqa	1	128	1536	7168	2.8186	232.4757			attn_wqa	1	128	1536	7168	2.8186	232.4757	34.4122	
attn_wqb	1	128	24576	1536	9.6637	235.1770			attn_wqb	1	128	24576	1536	9.6637	235.1770	34.0169	
attn_wkv_a	1	128	576	7168	1.0570	206.4384			attn_wkv_a	1	128	576	7168	1.0570	206.4384	38.7525	
attn_wkv_b	1	128	32768	512	4.2950	204.1620			attn_q_nope	128	128	512	128	2.1475	113.7778	70.3125	
attn_qk	16384	1	4097	192	25.7761	1.9892			attn_qk_nope	128	128	4097	512	68.7363	199.8061	40.0388	
attn_pv	16384	1	128	4097	17.1841	1.9840			attn_qk_pe	128	128	4097	64	8.5920	84.4538	94.7263	
attn_o	1	128	7168	16384	30.0648	249.5930			attn_pv	128	128	512	4097	68.7363	199.8061	40.0388	
dense_up	1	128	18432	7168	33.8229	249.8045			attn_wv_b	128	128	128	512	2.1475	113.7778	70.3125	
dense_gate	1	128	18432	7168	33.8229	249.8045			attn_o	1	128	7168	16384	30.0648	249.5930	32.0522	
dense_down	1	128	7168	18432	33.8229	249.8045											
moe_export_up	1	128	2048	7168	3.7581	236.9587							attn	193.9635			
moe_export_gate	1	128	2048	7168	3.7581	236.9587								146.0645			
moe_export_down	1	128	7168	2048	3.7581	236.9587								75.3%			
moe_gate	1	128	256	7168	0.4698	168.6588											
                                    layer	B	M	N	K				
                attn	90.8591				attn_qk_nope	128	32	4097	512				
重计算attn_wkv_b	1	524288	32768	512	17592.18604		重计算量太大		attn_qk_pe	128	32	4097	64				
                                    attn_pv	128	32	512	4097				
                                                                    
                                    attn_qk	128	32	4097	576				
                                    attn_pv	128	32	512	4097				


参数：fp8训练				
    参数	计算说明	数值	备注
    卡数		2048	8卡256个node
训练参数	流水并行数	p	16	
    副本数	v	1	
    数据并行数(MOE)	d	2	Zero-1，只切优化器参数
    数据并行数(ATTN)	d	128	论文没讲，暂且认为也做DP，采用Zero-1，只切优化器参数
    模型并行数(MOE)	t	1	
    模型并行数(ATTN)	t	1	
    专家并行数(MOE)	e	64	
    batch size	B=2*d*b*m	15360	469B tokens后的训练batch
    microbath size	b	1	大概率还是1
    micro size(microbath数)	m	60	
    训练数据集	dataset	14.8T	
    sequence length	s	4096	只考虑pre-traing阶段
模型参数	hidden size	h	7168	
    layers	L	61	猜测切分是首层+MTP是一个PP级别，其他PP是4个block
    num_experts_per_tok		8	
    num_attention_heads	a	128	
    num_key_value_heads		128	
    qk_nope_head_dim		128	
    qk_rope_head_dim		64	
    v_head_dim		128	
    q_lora_rank		1536	
    kv_lora_rank		512	
    intermediate_size		18432	
    moe_intermediate_size		2048	
    moe_layer_freq		1	
    n_routed_experts		256	
    n_shared_experts		1	
    first_k_dense_replace		3	前3层是dense MLP
    每个expert的参数量		0.044040192	element, 单位B
    单层MOE的参数量	expert + gate参数	11.32016435	element, 单位B
    所有层MOE的参数量		656.5695324	element, 单位B
    每层dense MLP的参数量		0.396361728	element, 单位B
    前面N层dense MLP的参数量		1.189085184	element, 单位B
    单层ATTN参数量	q_lora+ kv_lora+o	0.18710528	element, 单位B
    所有层ATTN参数量		11.41342208	element, 单位B
    总参数量		669.1720397	element, 单位B
硬件参数	训练卡数	n=ptd	2048	
    单卡算力	c_device	1979	Tflops, fp8
    算力利用率	c_percentage	0.4	
    节点内带宽		200	GB/s,单向
    节点内带宽利用率		0.8	
    跨节点带宽		50	GB/s,单向
    跨节点带宽利用率	b_percentage	0.7	
    单卡内存		80	G
计算时间	前向单个专家计算量/token	2 * dim * inter_dim * 3	0.088080384	Gflops
    前向选中专家计算量/token		0.792723456	Gflops, 这里认为均匀分布下，分布式跨卡分配可等效为每张卡顺序处理自己选中的tokens
    反向wgrad单个专家计算量/token		0.088080384	认为前向和反向wgrad,dgrad相等
    反向wgrad选中专家计算量/token		0.792723456	
    反向dgrad单个专家计算量/token		0.088080384	
    反向dgrad选中专家计算量/token		0.792723456	
    前向选中专家计算时间		4.101813133	ms
    反向wgrad选中专家计算时间		4.101813133	
    反向dgrad选中专家计算时间		4.101813133	
    前向ATTN wqa计算量/token	2 * dim * q_lora_rank	0.022020096	Gflops
    前向ATTN wqb计算量/token	2 * dim * (kv_lora_rank + qk_rope_head_dim)	0.008257536	
    前向ATTN wkv_a计算量/token	2 * q_lora_rank *n_heads * qk_head_dim	0.075497472	
    前向ATTN wkv_b计算量/token	2 *  kv_lora_rank * n_heads * (qk_nope_head_dim + v_head_dim)	0.033554432	
    前向ATTN qk计算量	2 * b*s*s * n_heads * qk_head_dim	824.6337208	
    前向ATTN pv计算量	2 * b* s*s * n_heads * v_head_dim 	549.7558139	
    前向ATTN o计算量/token	2 * n_heads * v_head_dim * dim	0.234881024	
    前向ATTN计算时间		3.672506302	ms
存储	模型MOE参数		1.776287744	GB, fp8, PP+MOE+重复的shared专家, DP不切参数,dualpipe需要乘以2
    模型dense MLP参数		1.189085184	GB, fp8,PP,DP不切参数
    模型ATTN参数		1.49684224	GB, fp8, PP,DP不切参数，dualpipe需要乘以2
通信时间	分发传输量/token		7168	B，只算的一个专家的，fp8
    分发IB传输时间/token		0.0008192	ms， 由于限制4个节点，所以IB最多4次，每个节点内平均2个
    分发NVLINK传输时间/token		0.0000896	
    分发max传输时间/token		0.0008192	
    分发max传输时间		3.3554432	
    组合传输量/token		14336	B，只算的一个专家的， fp16
    组合IB传输时间/token		0.0016384	
    组合NVLINK传输时间/token		0.0001792	
    组合max传输时间/token		0.0016384	
    组合max传输时间		6.7108864	


参数：fp8训练				
    参数	计算说明	数值	备注
    卡数		2048	8卡256个node
训练参数	流水并行数	p	16	
    副本数	v	1	
    数据并行数(MOE)	d	2	Zero-1，只切优化器参数
    数据并行数(ATTN)	d	128	论文没讲，暂且认为也做DP，采用Zero-1，只切优化器参数
    模型并行数(MOE)	t	1	
    模型并行数(ATTN)	t	1	
    专家并行数(MOE)	e	64	
    batch size	B=2*d*b*m	15360	469B tokens后的训练batch
    microbath size	b	1	大概率还是1
    micro size(microbath数)	m	60	
    训练数据集	dataset	14.8T	
    sequence length	s	4096	只考虑pre-traing阶段
模型参数	hidden size	h	7168	
    layers	L	61	猜测切分是首层+MTP是一个PP级别，其他PP是4个block
    num_experts_per_tok		8	
    num_attention_heads	a	128	
    num_key_value_heads		128	
    qk_nope_head_dim		128	
    qk_rope_head_dim		64	
    v_head_dim		128	
    q_lora_rank		1536	
    kv_lora_rank		512	
    intermediate_size		18432	
    moe_intermediate_size		2048	
    moe_layer_freq		1	
    n_routed_experts		256	
    n_shared_experts		1	
    first_k_dense_replace		3	前3层是dense MLP
    每个expert的参数量		0.044040192	element, 单位B
    单层MOE的参数量	expert + gate参数	11.32016435	element, 单位B
    所有层MOE的参数量		656.5695324	element, 单位B
    每层dense MLP的参数量		0.396361728	element, 单位B
    前面N层dense MLP的参数量		1.189085184	element, 单位B
    单层ATTN参数量	q_lora+ kv_lora+o	0.18710528	element, 单位B
    所有层ATTN参数量		11.41342208	element, 单位B
    总参数量		669.1720397	element, 单位B
硬件参数	训练卡数	n=ptd	2048	
    单卡算力	c_device	1979	Tflops, fp8
    算力利用率	c_percentage	0.4	
    节点内带宽		200	GB/s,单向
    节点内带宽利用率		0.8	
    跨节点带宽		50	GB/s,单向
    跨节点带宽利用率	b_percentage	0.7	
    单卡内存		80	G
计算时间	前向单个专家计算量/token	2 * dim * inter_dim * 3	0.088080384	Gflops
    前向选中专家计算量/token		0.792723456	Gflops, 这里认为均匀分布下，分布式跨卡分配可等效为每张卡顺序处理自己选中的tokens
    反向wgrad单个专家计算量/token		0.088080384	认为前向和反向wgrad,dgrad相等
    反向wgrad选中专家计算量/token		0.792723456	
    反向dgrad单个专家计算量/token		0.088080384	
    反向dgrad选中专家计算量/token		0.792723456	
    前向选中专家计算时间		4.101813133	ms
    反向wgrad选中专家计算时间		4.101813133	
    反向dgrad选中专家计算时间		4.101813133	
    前向ATTN wqa计算量/token	2 * dim * q_lora_rank	0.022020096	Gflops
    前向ATTN wqb计算量/token	2 * dim * (kv_lora_rank + qk_rope_head_dim)	0.008257536	
    前向ATTN wkv_a计算量/token	2 * q_lora_rank *n_heads * qk_head_dim	0.075497472	
    前向ATTN wkv_b计算量/token	2 *  kv_lora_rank * n_heads * (qk_nope_head_dim + v_head_dim)	0.033554432	
    前向ATTN qk计算量	2 * b*s*s * n_heads * qk_head_dim	824.6337208	
    前向ATTN pv计算量	2 * b* s*s * n_heads * v_head_dim 	549.7558139	
    前向ATTN o计算量/token	2 * n_heads * v_head_dim * dim	0.234881024	
    前向ATTN计算时间		3.672506302	ms
存储	模型MOE参数		1.776287744	GB, fp8, PP+MOE+重复的shared专家, DP不切参数,dualpipe需要乘以2
    模型dense MLP参数		1.189085184	GB, fp8,PP,DP不切参数
    模型ATTN参数		1.49684224	GB, fp8, PP,DP不切参数，dualpipe需要乘以2
通信时间	分发传输量/token		7168	B，只算的一个专家的，fp8
    分发IB传输时间/token		0.0008192	ms， 由于限制4个节点，所以IB最多4次，每个节点内平均2个
    分发NVLINK传输时间/token		0.0000896	
    分发max传输时间/token		0.0008192	
    分发max传输时间		3.3554432	
    组合传输量/token		14336	B，只算的一个专家的， fp16
    组合IB传输时间/token		0.0016384	
    组合NVLINK传输时间/token		0.0001792	
    组合max传输时间/token		0.0016384	
    组合max传输时间		6.7108864	



参数：fp8推理				
    参数	计算说明	数值	
训练参数	流水并行数	p	1	
    副本数	v	1	
    数据并行数(MOE)	d	1	
    数据并行数(ATTN)	d	80	
    模型并行数(MOE)	t	1	
    模型并行数(ATTN)	t	4	
    专家并行数(MOE)	e	320	
    batch size	B=2*d*b*m	128	假设4096， 双batch都是4096
    microbath size	b	128	
    micro size(microbath数)	m	1	
    sequence length	s	1	
    kv_sequence length		4096	
模型参数	hidden size	h	7168	
    layers	L	61	
    num_experts_per_tok		8	
    num_attention_heads	a	128	
    num_key_value_heads		128	
    qk_nope_head_dim		128	
    qk_rope_head_dim		64	
    q_lora_rank		1536	
    kv_lora_rank		512	
    intermediate_size		18432	
    moe_intermediate_size		2048	
    moe_layer_freq		1	
    n_routed_experts		256	
    n_shared_experts		1	
    first_k_dense_replace		3	
    每个expert的参数量		0.044040192	
    单层MOE的参数量	expert + gate参数	11.32016435	
    所有层MOE的参数量		656.5695324	
    每层dense MLP的参数量		0.396361728	
    前面N层dense MLP的参数量		1.189085184	
    单层ATTN参数量	q_lora+ kv_lora+o	0.18710528	
    所有层ATTN参数量		11.41342208	
    总参数量		669.1720397	
    单卡MOE参数量		2.554331136	只放一个
    单卡dense MLP参数量		1.189085184	
    单卡ATTN参数量		2.85335552	
    单卡embedding层参数		0.92667904	
    单卡总参数量		7.52345088	
    单卡最大激活参数量		0.004718592	
硬件参数	训练卡数	n=ptd	32	
    单卡算力	c_device	1979	
    算力利用率	c_percentage	0.4	
    节点内带宽		200	
    节点内带宽利用率		0.8	
    跨节点带宽		50	
    跨节点带宽利用率	b_percentage	0.7	
    单卡内存		80	
    前向单个专家计算量/token		0.088080384	
    前向选中专家计算量/token		0.704643072	
    前向选中专家计算时间		0.113939254	
    前向ATTN计算量/token		/	
    前向ATTN计算时间/token		/	
通信时间	每个专家处理的token个数		4	GEMM M小,带宽瓶颈。 需要很少的SM的计算就OK
    分发传输量/token		7168	
    分发IB传输时间/token		0.0018432	ms， 每个卡一个专家，所以需要发9个，
    分发NVLINK传输时间/token		0	无nvlink
    分发max传输时间/token		0.0018432	
    分发max传输时间		0.2359296	
    组合传输量/token		14336	
    组合IB传输时间/token		0.0036864	
    组合NVLINK传输时间/token		0	
    组合max传输时间/token		0.0036864	
    组合max传输时间		0.4718592	
