# SGLang DeepSeek-V3 量化推理分析报告

## 1. W8A8 INT8 量化分析

**量化方式**: W8A8 INT8

### 权重量化
- 类型: 静态量化
- 精度: INT8
- 粒度: per-channel
- 对称性: symmetric

### 激活量化
- 类型: 动态量化
- 精度: INT8
- 粒度: per-token
- 对称性: symmetric

### 核心算子
**线性层**:
- per_token_quant_int8
- int8_scaled_mm

**MoE层**:
- fused_experts_impl
- TritonMoeQuantInfo

**注意力层**:
- qkv_proj
- o_proj
- RadixAttention

### 推理流程
1. 模型加载时权重已量化为INT8格式
2. 前向传播时激活动态量化为INT8
3. 使用int8_scaled_mm进行量化矩阵乘法
4. MoE层使用融合kernel优化专家计算
5. 输出反量化回原精度

## 2. Blockwise INT8 量化分析

**量化方式**: Blockwise INT8

### 权重量化
- 类型: 静态block-wise量化
- 精度: INT8
- 粒度: block-wise (default: [128, 128])
- 对称性: symmetric

### 激活量化
- 类型: 动态per-token-group量化
- 精度: INT8
- 粒度: per-token-group
- 对称性: symmetric

### 核心算子
**线性层**:
- per_token_group_quant_int8
- w8a8_block_int8_matmul
- apply_w8a8_block_int8_linear

**MoE层**:
- TritonMoeQuantInfo
- fused_experts_impl

**参数管理**:
- BlockQuantScaleParameter
- ModelWeightParameter

### 推理流程
1. 模型加载时权重按block进行INT8量化
2. 前向传播时激活按token-group动态量化为INT8
3. 使用w8a8_block_int8_matmul进行块级量化矩阵乘法
4. MoE层传递block_shape参数给量化信息
5. 输出反量化回原精度

## 3. 算子表格对比

### W8A8 INT8 算子表格

| Layer | B | M | N | K | 计算量(GFlops) | 计算访存比 | 备注 |
|-------|---|---|---|---|---------------|------------|------|
| attn_qkv_proj | 1 | 128 | 2560 | 7168 | 4.6976 | 257.41 | Q/K/V投影合并 |
| attn_qk | 128 | 128 | 4096 | 192 | 25.7698 | 80.95 | 注意力权重计算 |
| attn_pv | 128 | 128 | 128 | 4096 | 17.1799 | 67.66 | 注意力值计算 |
| attn_o | 1 | 128 | 7168 | 16384 | 30.0648 | 268.00 | 输出投影 |
| moe_gate | 1 | 128 | 256 | 7168 | 0.4698 | 181.10 | MoE路由 |
| moe_experts | 1 | 1024 | 11264 | 7168 | 90.1943 | 7104.54 | 专家网络 |
| shared_experts | 1 | 128 | 11264 | 7168 | 11.2743 | 7104.54 | 共享专家 |
| dense_mlp | 1 | 128 | 44032 | 7168 | 101.4686 | 13626.73 | 密集MLP |

### Blockwise INT8 算子表格

| Layer | B | M | N | K | 计算量(GFlops) | 计算访存比 | 块大小 | 备注 |
|-------|---|---|---|---|---------------|------------|--------|------|
| attn_qkv_proj_block | 1 | 128 | 2560 | 7168 | 4.6976 | 231.67 | [128, 128] | Q/K/V投影合并 (块级量化) |
| attn_qk_block | 128 | 128 | 4096 | 192 | 25.7698 | 72.85 | [128, 128] | 注意力权重计算 (块级量化) |
| attn_pv_block | 128 | 128 | 128 | 4096 | 17.1799 | 60.90 | [128, 128] | 注意力值计算 (块级量化) |
| attn_o_block | 1 | 128 | 7168 | 16384 | 30.0648 | 241.20 | [128, 128] | 输出投影 (块级量化) |
| moe_gate_block | 1 | 128 | 256 | 7168 | 0.4698 | 162.99 | [128, 128] | MoE路由 (块级量化) |
| moe_experts_block | 1 | 1024 | 11264 | 7168 | 90.1943 | 6394.08 | [128, 128] | 专家网络 (块级量化) |
| shared_experts_block | 1 | 128 | 11264 | 7168 | 11.2743 | 6394.08 | [128, 128] | 共享专家 (块级量化) |
| dense_mlp_block | 1 | 128 | 44032 | 7168 | 101.4686 | 12264.06 | [128, 128] | 密集MLP (块级量化) |

## 4. 总结

### 主要区别
1. **权重量化粒度**: W8A8使用per-channel量化，Blockwise使用block-wise量化
2. **激活量化粒度**: W8A8使用per-token量化，Blockwise使用per-token-group量化
3. **内存开销**: Blockwise由于额外的block scale参数，内存开销略高
4. **精度损失**: Blockwise由于更细粒度的量化，精度损失相对较小
5. **计算复杂度**: 两者计算复杂度相似，但Blockwise有额外的block处理开销

