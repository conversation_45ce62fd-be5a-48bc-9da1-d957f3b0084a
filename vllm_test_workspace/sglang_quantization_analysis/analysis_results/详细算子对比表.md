# DeepSeek-V3 量化算子详细对比表

## 模型参数

| 参数名 | 值 | 说明 |
|--------|----|---------|
| batch_size | 128 | 推理批次大小 |
| seq_len | 1 | 输入序列长度 |
| kv_cache_len | 4096 | KV缓存长度 |
| hidden_size | 7168 | 隐藏层维度 |
| intermediate_size | 18432 | 中间层维度 |
| q_lora_rank | 1536 | Q投影的LoRA rank |
| kv_lora_rank | 512 | K/V投影的LoRA rank |
| moe_intermediate_size | 2048 | MoE专家中间层维度 |
| num_heads | 128 | 注意力头数 |
| qk_nope_head_dim | 128 | Q/K非位置编码头维度 |
| qk_rope_head_dim | 64 | Q/K位置编码头维度 |
| v_head_dim | 128 | V头维度 |
| n_routed_experts | 256 | 路由专家总数 |
| num_experts_per_tok | 8 | 每个token选择的专家数 |
| n_shared_experts | 1 | 共享专家数 |
| num_layers | 61 | 总层数 |

## W8A8 INT8 算子表格

| Layer | B | M | N | K | 计算量(GFlops) | 计算访存比 | 备注 |
|-------|---|---|---|---|---------------|------------|------|
| attn_qkv_proj | 1 | 128 | 2560 | 7168 | 4.6976 | 257.28 | Q/K/V投影合并，per-channel量化 |
| attn_qk | 128 | 128 | 4096 | 192 | 25.7698 | 80.95 | 注意力权重计算，FP16精度 |
| attn_pv | 128 | 128 | 128 | 4096 | 17.1799 | 67.66 | 注意力值计算，FP16精度 |
| attn_o | 1 | 128 | 7168 | 16384 | 30.0648 | 267.93 | 输出投影，per-channel量化 |
| moe_gate | 1 | 128 | 256 | 7168 | 0.4698 | 90.55 | MoE路由门控，FP16精度 |
| moe_experts_gate_up | 1 | 1024 | 4096 | 7168 | 60.1295 | 1578.15 | 专家gate+up投影，per-channel量化 |
| moe_experts_down | 1 | 1024 | 7168 | 2048 | 30.0648 | 1336.95 | 专家down投影，per-channel量化 |
| shared_experts_gate_up | 1 | 128 | 4096 | 7168 | 7.5162 | 261.87 | 共享专家gate+up，per-channel量化 |
| shared_experts_down | 1 | 128 | 7168 | 2048 | 3.7581 | 253.97 | 共享专家down投影，per-channel量化 |
| dense_gate_up | 1 | 128 | 36864 | 7168 | 67.6457 | 268.99 | 密集MLP gate+up，per-channel量化 |
| dense_down | 1 | 128 | 7168 | 18432 | 33.8229 | 268.17 | 密集MLP down投影，per-channel量化 |

## Blockwise INT8 算子表格

| Layer | B | M | N | K | 计算量(GFlops) | 计算访存比 | 备注 |
|-------|---|---|---|---|---------------|------------|------|
| attn_qkv_proj_block | 1 | 128 | 2560 | 7168 | 4.6976 | 257.22 | Q/K/V投影合并，block-wise量化[128,128] |
| attn_qk_block | 128 | 128 | 4096 | 192 | 25.7698 | 80.95 | 注意力权重计算，FP16精度 (无量化) |
| attn_pv_block | 128 | 128 | 128 | 4096 | 17.1799 | 67.66 | 注意力值计算，FP16精度 (无量化) |
| attn_o_block | 1 | 128 | 7168 | 16384 | 30.0648 | 267.87 | 输出投影，block-wise量化[128,128] |
| moe_gate_block | 1 | 128 | 256 | 7168 | 0.4698 | 90.55 | MoE路由门控，FP16精度 (无量化) |
| moe_experts_gate_up_block | 1 | 1024 | 4096 | 7168 | 60.1295 | 1577.87 | 专家gate+up投影，block-wise量化[128,128] |
| moe_experts_down_block | 1 | 1024 | 7168 | 2048 | 30.0648 | 1336.75 | 专家down投影，block-wise量化[128,128] |
| shared_experts_gate_up_block | 1 | 128 | 4096 | 7168 | 7.5162 | 261.81 | 共享专家gate+up，block-wise量化[128,128] |
| shared_experts_down_block | 1 | 128 | 7168 | 2048 | 3.7581 | 253.91 | 共享专家down投影，block-wise量化[128,128] |
| dense_gate_up_block | 1 | 128 | 36864 | 7168 | 67.6457 | 268.93 | 密集MLP gate+up，block-wise量化[128,128] |
| dense_down_block | 1 | 128 | 7168 | 18432 | 33.8229 | 268.11 | 密集MLP down投影，block-wise量化[128,128] |

## 对比分析

### 计算量对比
- W8A8 INT8总计算量: 281.1191 GFlops
- Blockwise INT8总计算量: 281.1191 GFlops
- 计算量差异: 0.00%

### 访存性能对比
- W8A8 INT8平均计算访存比: 430.22
- Blockwise INT8平均计算访存比: 430.15
- 访存性能差异: -0.02%

### 关键差异
1. **量化粒度**: W8A8使用per-channel量化，Blockwise使用block-wise量化
2. **存储开销**: Blockwise需要额外存储block scale参数
3. **精度**: Blockwise通常有更好的量化精度
4. **计算复杂度**: Blockwise有额外的block处理开销
5. **硬件友好性**: 两者都针对现代GPU优化

