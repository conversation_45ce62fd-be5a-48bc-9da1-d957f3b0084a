{"模型配置": {"hidden_size": 7168, "intermediate_size": 18432, "q_lora_rank": 1536, "kv_lora_rank": 512, "moe_intermediate_size": 2048, "num_heads": 128, "qk_nope_head_dim": 128, "qk_rope_head_dim": 64, "v_head_dim": 128, "n_routed_experts": 256, "num_experts_per_tok": 8, "n_shared_experts": 1, "first_k_dense_replace": 3, "num_layers": 61}, "W8A8_INT8分析": {"量化方式": "W8A8 INT8", "权重量化": {"类型": "静态量化", "精度": "INT8", "粒度": "per-channel", "对称性": "symmetric"}, "激活量化": {"类型": "动态量化", "精度": "INT8", "粒度": "per-token", "对称性": "symmetric"}, "核心算子": {"线性层": ["per_token_quant_int8", "int8_scaled_mm"], "MoE层": ["fused_experts_impl", "TritonMoeQuantInfo"], "注意力层": ["qkv_proj", "o_proj", "RadixAttention"]}, "推理流程": ["1. 模型加载时权重已量化为INT8格式", "2. 前向传播时激活动态量化为INT8", "3. 使用int8_scaled_mm进行量化矩阵乘法", "4. MoE层使用融合kernel优化专家计算", "5. 输出反量化回原精度"]}, "Blockwise_INT8分析": {"量化方式": "Blockwise INT8", "权重量化": {"类型": "静态block-wise量化", "精度": "INT8", "粒度": "block-wise (default: [128, 128])", "对称性": "symmetric"}, "激活量化": {"类型": "动态per-token-group量化", "精度": "INT8", "粒度": "per-token-group", "对称性": "symmetric"}, "核心算子": {"线性层": ["per_token_group_quant_int8", "w8a8_block_int8_matmul", "apply_w8a8_block_int8_linear"], "MoE层": ["TritonMoeQuantInfo", "fused_experts_impl"], "参数管理": ["BlockQuantScaleParameter", "ModelWeightParameter"]}, "推理流程": ["1. 模型加载时权重按block进行INT8量化", "2. 前向传播时激活按token-group动态量化为INT8", "3. 使用w8a8_block_int8_matmul进行块级量化矩阵乘法", "4. MoE层传递block_shape参数给量化信息", "5. 输出反量化回原精度"]}, "算子信息": {"attention": {"qkv_proj": {"input_shape": [1, "seq_len", 7168], "weight_shapes": {"q_proj": [1536, 7168], "k_proj": [512, 7168], "v_proj": [512, 7168]}, "算子类型": "线性投影"}, "attention_compute": {"q_shape": ["batch", "seq_len", 128, 192], "k_shape": ["batch", "kv_seq_len", 128, 192], "v_shape": ["batch", "kv_seq_len", 128, 128], "算子类型": "多头注意力"}, "o_proj": {"input_shape": [1, "seq_len", 16384], "weight_shape": [7168, 16384], "算子类型": "线性投影"}}, "moe": {"gate": {"input_shape": [1, "seq_len", 7168], "weight_shape": [256, 7168], "算子类型": "路由门控"}, "experts": {"gate_proj": {"weight_shape": [256, 2048, 7168], "算子类型": "专家门控投影"}, "up_proj": {"weight_shape": [256, 2048, 7168], "算子类型": "专家上投影"}, "down_proj": {"weight_shape": [256, 7168, 2048], "算子类型": "专家下投影"}}, "shared_experts": {"gate_proj": {"weight_shape": [2048, 7168], "算子类型": "共享专家门控投影"}, "up_proj": {"weight_shape": [2048, 7168], "算子类型": "共享专家上投影"}, "down_proj": {"weight_shape": [7168, 2048], "算子类型": "共享专家下投影"}}}, "dense_mlp": {"gate_up_proj": {"weight_shape": [36864, 7168], "算子类型": "门控和上投影合并"}, "down_proj": {"weight_shape": [7168, 18432], "算子类型": "下投影"}}, "normalization": {"rms_norm": {"weight_shape": [7168], "算子类型": "RMS归一化"}}, "position_encoding": {"rope": {"算子类型": "旋转位置编码", "head_dim": 64}}}, "性能统计": {"attention_per_layer": {"qkv_proj": {"计算量_GFlops": 4.69762048, "参数量_MB": 17.5, "输入形状": [128, 1, 7168], "输出形状": [128, 1, 2560]}, "attention_compute": {"计算量_GFlops": 42.94967296, "访存量_MB": 40970.0}, "o_proj": {"计算量_GFlops": 30.064771072, "参数量_MB": 112.0, "输入形状": [128, 1, 16384], "输出形状": [128, 1, 7168]}}, "moe_per_layer": {"gate": {"计算量_GFlops": 0.469762048, "参数量_MB": 1.75}, "selected_experts": {"计算量_GFlops": 90.194313216, "参数量_MB": 336.0, "有效专家数": 8, "总专家数": 256}, "shared_experts": {"计算量_GFlops": 11.274289152, "参数量_MB": 42.0}}, "dense_mlp_per_layer": {"gate_up_proj": {"计算量_GFlops": 67.645734912, "参数量_MB": 252.0}, "down_proj": {"计算量_GFlops": 33.822867456, "参数量_MB": 126.0}}, "total": {"总层数": 61, "Dense MLP层数": 3, "MoE层数": 58, "总计算量_GFlops": 10957.266878464001, "总参数量_MB": 31059.0}}, "W8A8_INT8算子表格": {"native方案_W8A8_INT8": {"layer": ["attn_qkv_proj", "attn_qk", "attn_pv", "attn_o", "moe_gate", "moe_experts", "shared_experts", "dense_mlp"], "B": [1, 128, 128, 1, 1, 1, 1, 1], "M": [128, 128, 128, 128, 128, 1024, 128, 128], "N": [2560, 4096, 128, 7168, 256, 11264, 11264, 44032], "K": [7168, 192, 4096, 16384, 7168, 7168, 7168, 7168], "计算量_GFlops": [4.69762048, 25.769803776, 17.179869184, 30.064771072, 0.469762048, 90.194313216, 11.274289152, 101.468602368], "计算访存比": [257.4107489776589, 80.94564130866257, 67.66225401698462, 267.9984816341937, 181.0960328101647, 7104.536671783384, 7104.536671783384, 13626.734272109115], "备注": ["Q/K/V投影合并", "注意力权重计算", "注意力值计算", "输出投影", "MoE路由", "专家网络", "共享专家", "密集MLP"]}}, "Blockwise_INT8算子表格": {"blockwise_INT8方案": {"layer": ["attn_qkv_proj_block", "attn_qk_block", "attn_pv_block", "attn_o_block", "moe_gate_block", "moe_experts_block", "shared_experts_block", "dense_mlp_block"], "B": [1, 128, 128, 1, 1, 1, 1, 1], "M": [128, 128, 128, 128, 128, 1024, 128, 128], "N": [2560, 4096, 128, 7168, 256, 11264, 11264, 44032], "K": [7168, 192, 4096, 16384, 7168, 7168, 7168, 7168], "计算量_GFlops": [4.69762048, 25.769803776, 17.179869184, 30.064771072, 0.469762048, 90.194313216, 11.274289152, 101.468602368], "计算访存比": [231.669674079893, 72.85107717779631, 60.896028615286156, 241.19863347077433, 162.98642952914824, 6394.083004605046, 6394.083004605046, 12264.060844898204], "备注": ["Q/K/V投影合并 (块级量化)", "注意力权重计算 (块级量化)", "注意力值计算 (块级量化)", "输出投影 (块级量化)", "MoE路由 (块级量化)", "专家网络 (块级量化)", "共享专家 (块级量化)", "密集MLP (块级量化)"], "块大小": ["[128, 128]", "[128, 128]", "[128, 128]", "[128, 128]", "[128, 128]", "[128, 128]", "[128, 128]", "[128, 128]"]}}}