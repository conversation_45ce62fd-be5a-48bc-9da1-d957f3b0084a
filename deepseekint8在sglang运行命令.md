https://github.com/sgl-project/sglang/pull/3888保存了sglang的相关issue
Reproduce

Launch
remind to add --quantization w8a8_int8


SGLANG_IS_FLASHINFER_AVAILABLE=false python3 -m sglang.launch_server \
--model /home/<USER>/deepseek-int8 --tp 4 --dist-init-addr \
127.0.0.1:8001 --trust-remote --enable-torch-compile --torch-compile-max-bs 2 \
--quantization w8a8_int8
# node0
SGLANG_IS_FLASHINFER_AVAILABLE=false python3 -m sglang.launch_server \
--model /home/<USER>/deepseek-int8 --tp 4 --dist-init-addr \
127.0.0.1:8001 --nnodes 2 --node-rank 0 --trust-remote --enable-torch-compile --torch-compile-max-bs 2 \
--quantization w8a8_int8
  
# node1
SGLANG_IS_FLASHINFER_AVAILABLE=false python3 -m sglang.launch_server \
--model /home/<USER>/deepseek-int8 --tp 4 --dist-init-addr \
127.0.0.1:8001 --nnodes 2 --node-rank 1 --trust-remote --enable-torch-compile --torch-compile-max-bs 2 \
--quantization w8a8_int8

Accuracy

# gsm8k
python3 /path/to/sglang/benchmark/gsm8k/bench_sglang.py --num-questions 1400 --parallel 200

# mmlu
python3 /path/to/sglang/benchmark/mmlu/bench_sglang.py --parallel 200

Throughput
# qps=128
python3 -m sglang.bench_serving --dataset-path /path/to/ShareGPT_V3_unfiltered_cleaned_split.json --dataset-name random  --random-input 128 --random-output 128 --num-prompts 1000 --request-rate 128 --random-range-ratio 1.0
